<template>
  <div class="ai-model-selection">
    <div class="container">
      <h1 class="page-title">选择AI模型</h1>

      <div class="model-categories">
        <div v-for="category in categories" :key="category.id"
          :class="['category-tab', { active: activeCategory === category.id }]" @click="activeCategory = category.id">
          {{ category.name }}
        </div>
      </div>

      <div class="models-grid">
        <div v-for="model in filteredModels" :key="model.id" class="model-card" @click="selectModel(model)">
          <div class="model-card__header">
            <h3 class="model-card__title">{{ model.name }}</h3>
            <span class="model-card__provider">{{ model.provider }}</span>
          </div>

          <div class="model-card__body">
            <p class="model-card__description">{{ model.description }}</p>

            <div class="model-card__stats">
              <div class="stat">
                <span class="stat__label">语言能力</span>
                <div class="stat__bar">
                  <div class="stat__fill" :style="{ width: `${model.languageAbility}%` }"></div>
                </div>
              </div>

              <div class="stat">
                <span class="stat__label">逻辑推理</span>
                <div class="stat__bar">
                  <div class="stat__fill" :style="{ width: `${model.logicalReasoning}%` }"></div>
                </div>
              </div>

              <div class="stat">
                <span class="stat__label">辩论技巧</span>
                <div class="stat__bar">
                  <div class="stat__fill" :style="{ width: `${model.debateSkill}%` }"></div>
                </div>
              </div>
            </div>
          </div>

          <div class="model-card__footer">
            <span class="model-card__type">{{ model.type }}</span>
            <span class="model-card__status" :class="model.available ? 'available' : 'unavailable'">
              {{ model.available ? '可用' : '不可用' }}
            </span>
          </div>
        </div>
      </div>

      <div class="add-custom-model">
        <router-link to="/ai-model/custom" class="btn btn--primary">
          添加自定义模型
        </router-link>
      </div>
    </div>

    <!-- 模型详情模态框 -->
    <div v-if="selectedModel" class="modal-overlay" @click.self="closeModal">
      <div class="modal">
        <div class="modal__header">
          <h2 class="modal__title">{{ selectedModel.name }}</h2>
          <button class="modal__close" @click="closeModal">×</button>
        </div>

        <div class="modal__body">
          <div class="model-detail">
            <div class="model-detail__provider">
              <strong>提供商:</strong> {{ selectedModel.provider }}
            </div>

            <div class="model-detail__description">
              <p>{{ selectedModel.description }}</p>
            </div>

            <div class="model-detail__capabilities">
              <h3>能力指标</h3>

              <div class="capability">
                <span class="capability__label">语言能力</span>
                <div class="capability__bar">
                  <div class="capability__fill" :style="{ width: `${selectedModel.languageAbility}%` }"></div>
                </div>
                <span class="capability__value">{{ selectedModel.languageAbility }}%</span>
              </div>

              <div class="capability">
                <span class="capability__label">逻辑推理</span>
                <div class="capability__bar">
                  <div class="capability__fill" :style="{ width: `${selectedModel.logicalReasoning}%` }"></div>
                </div>
                <span class="capability__value">{{ selectedModel.logicalReasoning }}%</span>
              </div>

              <div class="capability">
                <span class="capability__label">辩论技巧</span>
                <div class="capability__bar">
                  <div class="capability__fill" :style="{ width: `${selectedModel.debateSkill}%` }"></div>
                </div>
                <span class="capability__value">{{ selectedModel.debateSkill }}%</span>
              </div>

              <div class="capability">
                <span class="capability__label">知识广度</span>
                <div class="capability__bar">
                  <div class="capability__fill" :style="{ width: `${selectedModel.knowledgeBreadth}%` }"></div>
                </div>
                <span class="capability__value">{{ selectedModel.knowledgeBreadth }}%</span>
              </div>

              <div class="capability">
                <span class="capability__label">回应速度</span>
                <div class="capability__bar">
                  <div class="capability__fill" :style="{ width: `${selectedModel.responseSpeed}%` }"></div>
                </div>
                <span class="capability__value">{{ selectedModel.responseSpeed }}%</span>
              </div>
            </div>

            <div class="model-detail__features">
              <h3>特点</h3>
              <ul>
                <li v-for="(feature, index) in selectedModel.features" :key="index">
                  {{ feature }}
                </li>
              </ul>
            </div>
          </div>
        </div>

        <div class="modal__footer">
          <button class="btn btn--outline" @click="closeModal">取消</button>
          <button class="btn btn--primary" @click="confirmModelSelection" :disabled="!selectedModel.available">
            选择此模型
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script src="./js/AIModelSelection.js"></script>

<style lang="scss">
@use './scss/AIModelSelection.scss';
</style>
