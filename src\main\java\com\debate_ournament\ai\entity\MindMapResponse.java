package com.debate_ournament.ai.entity;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Lob;
import jakarta.persistence.Table;

/**
 * 思维导图响应实体
 */
@Entity
@Table(name = "mindmap_responses")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MindMapResponse {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 会话ID
     */
    @Column(name = "session_id", length = 100)
    private String sessionId;

    /**
     * 用户ID
     */
    @Column(name = "user_id")
    private Long userId;

    /**
     * 关联的聊天消息ID
     */
    @Column(name = "chat_message_id")
    private Long chatMessageId;

    /**
     * 原始请求文本
     */
    @Lob
    @Column(name = "request_text", columnDefinition = "TEXT")
    private String requestText;

    /**
     * 思维导图标题
     */
    @Column(name = "title", length = 200)
    private String title;

    /**
     * 思维导图描述
     */
    @Column(name = "description", length = 500)
    private String description;

    /**
     * 思维导图JSON数据
     */
    @Lob
    @Column(name = "mindmap_data", columnDefinition = "LONGTEXT")
    private String mindmapData;

    /**
     * 思维导图根节点
     */
    @JsonIgnore
    private transient MindMapNode rootNode;

    /**
     * 思维导图类型
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "mindmap_type", length = 20)
    private MindMapType mindmapType;

    /**
     * 主题领域
     */
    @Column(name = "topic_domain", length = 100)
    private String topicDomain;

    /**
     * 节点总数
     */
    @Column(name = "node_count")
    private Integer nodeCount;

    /**
     * 最大深度
     */
    @Column(name = "max_depth")
    private Integer maxDepth;

    /**
     * AI提供商
     */
    @Column(name = "ai_provider", length = 50)
    private String aiProvider;

    /**
     * 使用的AI模型
     */
    @Column(name = "model_name", length = 100)
    private String modelName;

    /**
     * 处理时间（毫秒）
     */
    @Column(name = "processing_time")
    private Long processingTime;

    /**
     * 生成成本
     */
    @Column(name = "cost", precision = 10, scale = 4)
    private java.math.BigDecimal cost;

    /**
     * 生成状态
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "status", length = 20)
    private ResponseStatus status;

    /**
     * 错误信息
     */
    @Column(name = "error_message", length = 1000)
    private String errorMessage;

    /**
     * 标签
     */
    @Column(name = "tags", length = 500)
    private String tags;

    /**
     * 是否公开
     */
    @Column(name = "is_public")
    private Boolean isPublic;

    /**
     * 访问次数
     */
    @Column(name = "view_count")
    private Integer viewCount;

    /**
     * 点赞数
     */
    @Column(name = "like_count")
    private Integer likeCount;

    /**
     * 扩展属性
     */
    @Lob
    @Column(name = "metadata", columnDefinition = "TEXT")
    private String metadata;

    /**
     * 创建时间
     */
    @Column(name = "created_at")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    public MindMapResponse() {
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
        this.status = ResponseStatus.PENDING;
        this.isPublic = false;
        this.viewCount = 0;
        this.likeCount = 0;
    }

    /**
     * 设置根节点并序列化为JSON
     */
    public void setRootNode(MindMapNode rootNode) {
        this.rootNode = rootNode;
        if (rootNode != null) {
            try {
                ObjectMapper mapper = new ObjectMapper();
                this.mindmapData = mapper.writeValueAsString(rootNode);
                this.nodeCount = countNodes(rootNode);
                this.maxDepth = calculateMaxDepth(rootNode);
            } catch (JsonProcessingException e) {
                throw new RuntimeException("序列化思维导图数据失败", e);
            }
        }
    }

    /**
     * 从JSON反序列化获取根节点
     */
    public MindMapNode getRootNode() {
        if (rootNode == null && mindmapData != null) {
            try {
                ObjectMapper mapper = new ObjectMapper();
                rootNode = mapper.readValue(mindmapData, MindMapNode.class);
            } catch (JsonProcessingException e) {
                throw new RuntimeException("反序列化思维导图数据失败", e);
            }
        }
        return rootNode;
    }

    /**
     * 计算节点总数
     */
    private int countNodes(MindMapNode node) {
        if (node == null) return 0;
        int count = 1;
        if (node.getChildren() != null) {
            for (MindMapNode child : node.getChildren()) {
                count += countNodes(child);
            }
        }
        return count;
    }

    /**
     * 计算最大深度
     */
    private int calculateMaxDepth(MindMapNode node) {
        if (node == null) return 0;
        int maxChildDepth = 0;
        if (node.getChildren() != null) {
            for (MindMapNode child : node.getChildren()) {
                maxChildDepth = Math.max(maxChildDepth, calculateMaxDepth(child));
            }
        }
        return maxChildDepth + 1;
    }

    /**
     * 设置扩展属性
     */
    public void setMetadataProperty(String key, Object value) {
        try {
            ObjectMapper mapper = new ObjectMapper();
            Map<String, Object> metaMap;
            if (this.metadata != null) {
                metaMap = mapper.readValue(this.metadata, Map.class);
            } else {
                metaMap = new HashMap<>();
            }
            metaMap.put(key, value);
            this.metadata = mapper.writeValueAsString(metaMap);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("设置元数据失败", e);
        }
    }

    /**
     * 获取扩展属性
     */
    public Object getMetadataProperty(String key) {
        if (this.metadata == null) return null;
        try {
            ObjectMapper mapper = new ObjectMapper();
            Map<String, Object> metaMap = mapper.readValue(this.metadata, Map.class);
            return metaMap.get(key);
        } catch (JsonProcessingException e) {
            return null;
        }
    }

    /**
     * 更新时间戳
     */
    public void updateTimestamp() {
        this.updatedAt = LocalDateTime.now();
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getChatMessageId() {
        return chatMessageId;
    }

    public void setChatMessageId(Long chatMessageId) {
        this.chatMessageId = chatMessageId;
    }

    public String getRequestText() {
        return requestText;
    }

    public void setRequestText(String requestText) {
        this.requestText = requestText;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getMindmapData() {
        return mindmapData;
    }

    public void setMindmapData(String mindmapData) {
        this.mindmapData = mindmapData;
    }

    public MindMapType getMindmapType() {
        return mindmapType;
    }

    public void setMindmapType(MindMapType mindmapType) {
        this.mindmapType = mindmapType;
    }

    public String getTopicDomain() {
        return topicDomain;
    }

    public void setTopicDomain(String topicDomain) {
        this.topicDomain = topicDomain;
    }

    public Integer getNodeCount() {
        return nodeCount;
    }

    public void setNodeCount(Integer nodeCount) {
        this.nodeCount = nodeCount;
    }

    public Integer getMaxDepth() {
        return maxDepth;
    }

    public void setMaxDepth(Integer maxDepth) {
        this.maxDepth = maxDepth;
    }

    public String getAiProvider() {
        return aiProvider;
    }

    public void setAiProvider(String aiProvider) {
        this.aiProvider = aiProvider;
    }

    public String getModelName() {
        return modelName;
    }

    public void setModelName(String modelName) {
        this.modelName = modelName;
    }

    public Long getProcessingTime() {
        return processingTime;
    }

    public void setProcessingTime(Long processingTime) {
        this.processingTime = processingTime;
    }

    public java.math.BigDecimal getCost() {
        return cost;
    }

    public void setCost(java.math.BigDecimal cost) {
        this.cost = cost;
    }

    public ResponseStatus getStatus() {
        return status;
    }

    public void setStatus(ResponseStatus status) {
        this.status = status;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public String getTags() {
        return tags;
    }

    public void setTags(String tags) {
        this.tags = tags;
    }

    public Boolean getIsPublic() {
        return isPublic;
    }

    public void setIsPublic(Boolean isPublic) {
        this.isPublic = isPublic;
    }

    public Integer getViewCount() {
        return viewCount;
    }

    public void setViewCount(Integer viewCount) {
        this.viewCount = viewCount;
    }

    public Integer getLikeCount() {
        return likeCount;
    }

    public void setLikeCount(Integer likeCount) {
        this.likeCount = likeCount;
    }

    public String getMetadata() {
        return metadata;
    }

    public void setMetadata(String metadata) {
        this.metadata = metadata;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    /**
     * 思维导图类型枚举
     */
    public enum MindMapType {
        CONCEPT("概念图"),
        KNOWLEDGE("知识图"),
        ARGUMENT("论证图"),
        FLOWCHART("流程图"),
        HIERARCHY("层级图"),
        NETWORK("网络图"),
        TIMELINE("时间线"),
        COMPARISON("对比图");

        private final String description;

        MindMapType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 响应状态枚举
     */
    public enum ResponseStatus {
        PENDING("处理中"),
        SUCCESS("成功"),
        FAILED("失败"),
        CANCELLED("已取消");

        private final String description;

        ResponseStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
