/**
 * 首页组件的业务逻辑
 */
import { ref, reactive, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import DebateCard from '@/components/debate/DebateCard.vue';
import DebateLiveCard from '@/components/debate/DebateLiveCard.vue';
// import DebateScene3d from '@/components/3d/DebateScene3d.vue';
import MindMapVisualization from '@/components/visualization/MindMapVisualization.vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import axios from 'axios';

export default {
  name: 'HomePage',

  components: {
    DebateCard,
    DebateLiveCard,
    MindMapVisualization
  },

  setup() {
    const router = useRouter();
    const activeModelTab = ref('preset');
    const selectedDebateTopic = ref('AI是否应该拥有投票权？');

    // 主页核心功能卡片（可由后端返回）
    const features = ref([
      {
        icon: 'ChatRound',
        title: 'AI智能辩手',
        items: ['多种风格：学术、激进、温和', '实时语音：自然语音合成', '智能反驳：逻辑链分析'],
        button: '立即体验',
        action: () => navigateToDebateHall()
      },
      {
        icon: 'SetUp',
        title: '自定义AI大模型',
        items: ['模型选择：GPT-4, Llama等', '参数调节：创意度、响应长度', '个性定制：上传知识库'],
        button: '开始定制',
        action: () => navigateToAIModels()
      },
      {
        icon: 'DataAnalysis',
        title: '思维导图可视化',
        items: ['实时生成：论点关系图谱', '交互分析：点击查看详情', '导出分享：多格式输出'],
        button: '查看示例',
        action: () => showMindMapDemo()
      },
      {
        icon: 'Avatar',
        title: '个性化头像系统',
        items: ['用户头像：上传、编辑、美化', 'AI形象：3D模型、2D插画', '动态效果：表情、动作包'],
        button: '自定义形象',
        action: () => navigateToUserCenter()
      }
    ]);

    // 实时辩论数据
    const liveDebates = ref([]);
    // AI模型数据
    const aiModels = reactive({ preset: [], custom: [] });
    // 今日辩论之星
    const starUser = ref({
      avatar: '',
      name: '',
      debates: 0,
      winRate: '',
      likes: 0,
      quote: ''
    });
    // 热门话题
    const hotTopic = ref({
      title: '',
      participants: 0,
      comments: 0,
      support: 0,
      oppose: 0
    });

    // 示例：页面加载时从后端获取数据
    onMounted(async () => {
      // 获取实时辩论
      try {
        const res = await axios.get('/api/debates/live');
        liveDebates.value = res.data;
      } catch (e) {
        // fallback 示例数据
        liveDebates.value = [
          { id: 1, title: 'AI是否应该拥有投票权', supportPercent: 65, opposePercent: 35, viewers: 1234, timeRemaining: '5:23', status: 'live' },
          { id: 2, title: '远程工作VS现场办公', supportPercent: 43, opposePercent: 57, viewers: 856, timeRemaining: '12:45', status: 'live' },
          { id: 3, title: '社交媒体是否有害', supportPercent: 78, opposePercent: 22, viewers: 2109, timeRemaining: '2:11', status: 'live' }
        ];
      }
      // 获取AI模型
      try {
        const res = await axios.get('/api/ai-models');
        aiModels.preset = res.data.preset;
        aiModels.custom = res.data.custom;
      } catch (e) {
        aiModels.preset = [
          { id: 1, name: 'GPT-4o', features: [ { icon: 'Aim', text: '逻辑严密' }, { icon: 'DataAnalysis', text: '数据支撑' } ] },
          { id: 2, name: 'Claude-3.5', features: [ { icon: 'Light', text: '创意丰富' }, { icon: 'Edit', text: '文采斐然' } ] },
          { id: 3, name: 'Llama-3', features: [ { icon: 'Lightning', text: '响应快速' }, { icon: 'Globe', text: '多语言' } ] }
        ];
      }
      // 获取今日辩论之星
      try {
        const res = await axios.get('/api/community/star-user');
        starUser.value = res.data;
      } catch (e) {
        starUser.value = {
          avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
          name: '@逻辑大师',
          debates: 28,
          winRate: '78%',
          likes: 1234,
          quote: '在人工智能伦理讨论中，我们应该...'
        };
      }
      // 获取热门话题
      try {
        const res = await axios.get('/api/community/hot-topic');
        hotTopic.value = res.data;
      } catch (e) {
        hotTopic.value = {
          title: '#元宇宙是否会改变教育方式',
          participants: 2456,
          comments: 8923,
          support: 62,
          oppose: 38
        };
      }
    });

    // 热门辩论数据
    const featuredDebates = ref([
      {
        id: 1,
        title: "人工智能是否会超越人类智能？",
        category: "科技",
        participants: 120,
        supportersCount: 68,
        opposersCount: 52,
        status: "active"
      },
      {
        id: 2,
        title: "网络文学是否可以与传统文学相提并论？",
        category: "文化",
        participants: 85,
        supportersCount: 45,
        opposersCount: 40,
        status: "active"
      },
      {
        id: 3,
        title: "素食主义是否是更健康的生活方式？",
        category: "生活",
        participants: 92,
        supportersCount: 47,
        opposersCount: 45,
        status: "completed"
      }
    ]);

    // 辩论分类数据
    const categories = ref([
      { id: 1, name: "科技", icon: "💻", count: 48 },
      { id: 2, name: "文化", icon: "🎭", count: 36 },
      { id: 3, name: "教育", icon: "📚", count: 29 },
      { id: 4, name: "社会", icon: "🌍", count: 52 },
      { id: 5, name: "经济", icon: "💰", count: 25 },
      { id: 6, name: "生活", icon: "🏠", count: 41 }
    ]);

    // 参与步骤数据
    const howToSteps = ref([
      {
        title: "选择辩题",
        description: "浏览热门辩题或创建自己的辩题"
      },
      {
        title: "选择立场",
        description: "支持方、反对方或担任评委"
      },
      {
        title: "参与辩论",
        description: "发表观点、回应对方、说服评委"
      },
      {
        title: "查看结果",
        description: "评委投票，决定胜负"
      }
    ]);

    /**
     * 导航到辩论大厅
     */
    function navigateToDebateHall() {
      router.push('/debate-hall');
    }

    /**
     * 导航到关于页面
     */
    function navigateToAbout() {
      router.push('/about');
    }

    /**
     * 导航到特定辩论
     * @param {number} id - 辩论ID
     */
    function navigateToDebate(id) {
      router.push(`/debate/${id}`);
    }

    /**
     * 导航到特定分类的辩论列表
     * @param {number} categoryId - 分类ID
     */
    function navigateToCategory(categoryId) {
      router.push(`/debate-hall?category=${categoryId}`);
    }

    /**
     * 导航到注册页面
     */
    function navigateToRegister() {
      router.push('/register');
    }

    /**
     * 导航到AI模型选择页面
     */
    function navigateToAIModels() {
      router.push('/ai-models');
    }

    /**
     * 导航到自定义AI模型页面
     */
    function navigateToCustomAI() {
      router.push('/ai-models/custom');
    }

    /**
     * 导航到用户中心
     */
    function navigateToUserCenter() {
      router.push('/user');
    }

    /**
     * 展示演示视频
     */
    function showDemo() {
      ElMessageBox.alert(
        '演示视频功能正在开发中，敬请期待！',
        '功能提示',
        {
          confirmButtonText: '确定',
          type: 'info',
        }
      );
    }

    /**
     * 观看直播辩论
     * @param {number} id - 辩论ID
     */
    function watchLiveDebate(id) {
      router.push(`/debate/${id}`);
    }

    /**
     * 选择AI模型
     * @param {Object} model - 模型对象
     */
    function selectAIModel(model) {
      ElMessage({
        message: `已选择 ${model.name} 模型`,
        type: 'success'
      });

      // 跳转到辩论创建页面
      setTimeout(() => {
        router.push('/debate-hall');
      }, 1000);
    }

    /**
     * 展示思维导图演示
     */
    function showMindMapDemo() {
      // 滚动到思维导图部分
      const mindMapSection = document.querySelector('.mind-map-section');
      if (mindMapSection) {
        mindMapSection.scrollIntoView({ behavior: 'smooth' });
      }
    }

    /**
     * 更新思维导图
     */
    function updateMindMap() {
      ElMessage({
        message: '思维导图已更新',
        type: 'success'
      });
    }

    /**
     * 导出思维导图
     * @param {string} format - 导出格式
     */
    function exportMindMap(format) {
      ElMessage({
        message: `思维导图已导出为 ${format.toUpperCase()} 格式`,
        type: 'success'
      });
    }

    /**
     * 分享思维导图
     */
    function shareMindMap() {
      ElMessageBox.prompt('复制以下链接分享给好友', '分享思维导图', {
        confirmButtonText: '复制',
        cancelButtonText: '取消',
        inputValue: `https://ai-debate.example.com/mind-map/${Date.now()}`,
        inputPattern: /^https:\/\/.+/,
        inputErrorMessage: '无效的链接'
      }).then(({ value }) => {
        // 复制到剪贴板
        navigator.clipboard.writeText(value).then(() => {
          ElMessage({
            message: '链接已复制到剪贴板',
            type: 'success'
          });
        });
      });
    }

    /**
     * 启用协作编辑
     */
    function enableCollaboration() {
      ElMessage({
        message: '协作编辑功能正在开发中，敬请期待！',
        type: 'info'
      });
    }

    return {
      features,
      liveDebates,
      aiModels,
      starUser,
      hotTopic,
      featuredDebates,
      categories,
      howToSteps,
      activeModelTab,
      selectedDebateTopic,
      navigateToDebateHall,
      navigateToAbout,
      navigateToDebate,
      navigateToCategory,
      navigateToRegister,
      navigateToAIModels,
      navigateToCustomAI,
      navigateToUserCenter,
      showDemo,
      watchLiveDebate,
      selectAIModel,
      showMindMapDemo,
      updateMindMap,
      exportMindMap,
      shareMindMap,
      enableCollaboration
    };
  }
};
