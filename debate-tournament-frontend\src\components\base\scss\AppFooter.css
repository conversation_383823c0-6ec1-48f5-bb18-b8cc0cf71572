.app-footer {
  background-color: #f8f9fa;
  padding: 3rem 0 1.5rem;
  margin-top: 2rem;
}

.app-footer__content {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  margin-bottom: 2rem;
}

.app-footer__logo {
  flex: 0 0 100%;
  margin-bottom: 2rem;
}

.app-footer__logo .logo__text {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--color-primary);
  display: block;
  margin-bottom: 0.5rem;
}

.app-footer__logo .logo__slogan {
  color: var(--color-secondary);
  font-size: 0.875rem;
}

.app-footer__links {
  flex: 0 0 100%;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.app-footer__bottom {
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  padding-top: 1.5rem;
  text-align: center;
}

.app-footer__bottom .copyright {
  font-size: 0.875rem;
  color: rgba(0, 0, 0, 0.6);
}

.footer-links {
  flex: 0 0 100%;
  margin-bottom: 1.5rem;
}

.footer-links__title {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.footer-links__list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-links__list li {
  margin-bottom: 0.5rem;
}

.footer-links__list li a {
  color: rgba(0, 0, 0, 0.7);
  text-decoration: none;
  transition: color 0.2s;
}

.footer-links__list li a:hover {
  color: var(--color-primary);
  text-decoration: none;
}

/* 响应式样式 */
@media (min-width: 768px) {
  .app-footer__logo {
    flex: 0 0 30%;
    margin-bottom: 0;
  }
  
  .app-footer__links {
    flex: 0 0 65%;
  }
}

@media (min-width: 576px) {
  .footer-links {
    flex: 0 0 30%;
    margin-bottom: 0;
  }
} 