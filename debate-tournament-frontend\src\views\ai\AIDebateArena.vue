<template>
  <div class="ai-debate-arena">
    <!-- 顶部信息区 -->
    <header class="arena-header">
      <div class="container">
        <h1 class="arena-title">{{ debate.title }}</h1>
        <div class="arena-meta">
          <span class="arena-category">{{ debate.category }}</span>
          <span class="arena-status" :class="'status--' + debate.status">
            {{ getStatusText(debate.status) }}
          </span>
        </div>
      </div>
    </header>

    <!-- 主辩论区域 -->
    <main class="arena-main">
      <div class="container">
        <!-- 辩论舞台 -->
        <section class="debate-stage">
          <!-- 主持人AI -->
          <div class="host-ai">
            <div class="ai-avatar" :class="{ speaking: currentSpeaker === 'host' }">
              <img :src="debate.host.avatar" :alt="debate.host.name">
            </div>
            <div class="ai-info">
              <h3>{{ debate.host.name }}</h3>
              <span class="ai-role">主持人</span>
            </div>
          </div>

          <!-- 辩论双方 -->
          <div class="debate-sides">
            <!-- 正方AI -->
            <div class="debate-side supporter">
              <div class="ai-avatar" :class="{ speaking: currentSpeaker === 'supporter' }">
                <img :src="debate.supporter.avatar" :alt="debate.supporter.name">
              </div>
              <div class="ai-info">
                <h3>{{ debate.supporter.name }}</h3>
                <span class="ai-role">正方</span>
              </div>
            </div>

            <!-- 反方AI -->
            <div class="debate-side opposer">
              <div class="ai-avatar" :class="{ speaking: currentSpeaker === 'opposer' }">
                <img :src="debate.opposer.avatar" :alt="debate.opposer.name">
              </div>
              <div class="ai-info">
                <h3>{{ debate.opposer.name }}</h3>
                <span class="ai-role">反方</span>
              </div>
            </div>
          </div>

          <!-- 评委团 -->
          <div class="judges-panel">
            <div v-for="(judge, index) in debate.judges" :key="index" class="judge-ai">
              <div class="ai-avatar" :class="{ speaking: currentSpeaker === 'judge-' + index }">
                <img :src="judge.avatar" :alt="judge.name">
              </div>
              <div class="ai-info">
                <h3>{{ judge.name }}</h3>
                <span class="ai-role">评委 {{ index + 1 }}</span>
              </div>
            </div>
          </div>
        </section>

        <!-- 辩论内容区 -->
        <section class="debate-content">
          <div class="speech-history">
            <div v-for="speech in speeches" :key="speech.id" class="speech-item" :class="speech.role">
              <div class="speech-header">
                <span class="speaker-name">{{ speech.speaker }}</span>
                <span class="speech-time">{{ formatTime(speech.timestamp) }}</span>
              </div>
              <div class="speech-content" v-html="speech.content"></div>
            </div>
          </div>
        </section>

        <!-- 控制面板 -->
        <section class="debate-controls">
          <div class="control-panel">
            <div class="debate-progress">
              <span class="round-info">第 {{ currentRound }} 轮</span>
              <div class="progress-bar">
                <div class="progress" :style="{ width: progress + '%' }"></div>
              </div>
              <span class="time-remaining">{{ formatDuration(timeRemaining) }}</span>
            </div>
            <div class="control-buttons">
              <button class="btn btn--primary" @click="toggleDebate" :disabled="!canControl">
                {{ isPlaying ? '暂停' : '开始' }}
              </button>
              <button class="btn btn--outline" @click="nextRound" :disabled="!canNextRound">
                下一轮
              </button>
              <button class="btn btn--danger" @click="endDebate" :disabled="!canEnd">
                结束辩论
              </button>
            </div>
          </div>
        </section>
      </div>
    </main>
  </div>
</template>

<script>
import AIDebateArenaJS from './js/AIDebateArena.js';

export default {
  name: 'AIDebateArena',
  ...AIDebateArenaJS
};
</script>

<style lang="scss">
@use './scss/AIDebateArena.scss';
</style>
