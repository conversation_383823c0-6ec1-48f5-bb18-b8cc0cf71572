package com.debate_ournament.users.controller;

import java.io.IOException;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.debate_ournament.dto.common.ApiResponse;
import com.debate_ournament.users.entity.UserProfile;
import com.debate_ournament.users.service.UserProfileService;
import com.debate_ournament.users.service.UserService;

/**
 * 用户资料控制器
 * 提供用户资料管理的RESTful API接口，支持前后端数据交互
 *
 * <AUTHOR> Debate Tournament Platform
 * @version 1.0
 * @since 2025-05-25
 */
@RestController
@RequestMapping("/api/users/profile")
public class UserProfileController {

    private final UserProfileService userProfileService;
    private final UserService userService;

    @Autowired
    public UserProfileController(UserProfileService userProfileService, UserService userService) {
        this.userProfileService = userProfileService;
        this.userService = userService;
    }

    /**
     * 获取当前用户的资料信息 (前端调用接口)
     * GET /api/users/profile/current
     */
    @GetMapping("/current")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<UserProfileService.UserDisplayInfo>> getCurrentUserProfile(Authentication authentication) {
        try {
            String username = authentication.getName();
            Long userId = userService.findByUsername(username)
                    .orElseThrow(() -> new RuntimeException("用户不存在"))
                    .getId();

            UserProfileService.UserDisplayInfo displayInfo = userProfileService.getUserDisplayInfo(userId);
            return ResponseEntity.ok(ApiResponse.success("获取当前用户资料成功", displayInfo));
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("获取用户资料失败: " + e.getMessage()));
        }
    }

    /**
     * 获取当前用户的资料信息 (兼容接口)
     * GET /api/users/profile/my
     */
    @GetMapping("/my")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<UserProfileService.UserDisplayInfo>> getMyProfile(Authentication authentication) {
        return getCurrentUserProfile(authentication);
    }

    /**
     * 根据用户ID获取资料信息
     * GET /api/users/profile/{userId}
     */
    @GetMapping("/{userId}")
    public ResponseEntity<ApiResponse<UserProfileService.UserDisplayInfo>> getUserProfileById(@PathVariable Long userId) {
        try {
            UserProfileService.UserDisplayInfo displayInfo = userProfileService.getUserDisplayInfo(userId);

            // 检查资料是否公开
            if (!displayInfo.isProfilePublic()) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("该用户资料不公开"));
            }

            return ResponseEntity.ok(ApiResponse.success("获取用户资料成功", displayInfo));
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("获取用户资料失败: " + e.getMessage()));
        }
    }

    /**
     * 根据用户名获取资料信息
     * GET /api/users/profile/user/{username}
     */
    @GetMapping("/user/{username}")
    public ResponseEntity<ApiResponse<UserProfileService.UserDisplayInfo>> getUserProfileByUsername(@PathVariable String username) {
        try {
            Long userId = userService.findByUsername(username)
                    .orElseThrow(() -> new RuntimeException("用户不存在"))
                    .getId();

            UserProfileService.UserDisplayInfo displayInfo = userProfileService.getUserDisplayInfo(userId);

            // 检查资料是否公开
            if (!displayInfo.isProfilePublic()) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("该用户资料不公开"));
            }

            return ResponseEntity.ok(ApiResponse.success("获取用户资料成功", displayInfo));
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("获取用户资料失败: " + e.getMessage()));
        }
    }

    /**
     * 获取用户资料列表（分页）- 前端调用接口
     * GET /api/users/profile/list
     */
    @GetMapping("/list")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getUserProfiles(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "") String search) {
        try {
            // 这里需要使用分页查询，暂时返回基础数据
            List<UserProfile> allProfiles;

            if (search != null && !search.trim().isEmpty()) {
                allProfiles = userProfileService.searchProfiles(search);
            } else {
                allProfiles = userProfileService.getPublicProfiles();
            }

            // 简单的分页处理（实际应该在Service层实现）
            int start = page * size;
            int end = Math.min(start + size, allProfiles.size());
            List<UserProfile> pageContent = start < allProfiles.size() ?
                allProfiles.subList(start, end) : List.of();

            Map<String, Object> response = new HashMap<>();
            response.put("profiles", pageContent);
            response.put("totalElements", allProfiles.size());
            response.put("totalPages", (int) Math.ceil((double) allProfiles.size() / size));
            response.put("currentPage", page);
            response.put("size", size);
            response.put("hasNext", end < allProfiles.size());
            response.put("hasPrevious", page > 0);

            return ResponseEntity.ok(ApiResponse.success("获取用户资料列表成功", response));
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("获取用户资料列表失败: " + e.getMessage()));
        }
    }

    /**
     * 更新当前用户的资料信息（支持JSON请求体）
     * PUT /api/users/profile/my
     */
    @PutMapping("/my")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<UserProfile>> updateMyProfile(
            @RequestBody Map<String, Object> profileData,
            Authentication authentication) {
        try {
            String username = authentication.getName();
            Long userId = userService.findByUsername(username)
                    .orElseThrow(() -> new RuntimeException("用户不存在"))
                    .getId();

            // 构建要更新的资料对象
            UserProfile profileUpdate = new UserProfile();

            if (profileData.containsKey("displayName")) {
                profileUpdate.setDisplayName((String) profileData.get("displayName"));
            }
            if (profileData.containsKey("bio")) {
                profileUpdate.setBio((String) profileData.get("bio"));
            }
            if (profileData.containsKey("birthDate")) {
                String birthDateStr = (String) profileData.get("birthDate");
                if (birthDateStr != null && !birthDateStr.isEmpty()) {
                    profileUpdate.setBirthDate(LocalDate.parse(birthDateStr));
                }
            }
            if (profileData.containsKey("gender")) {
                String genderStr = (String) profileData.get("gender");
                if (genderStr != null && !genderStr.isEmpty()) {
                    profileUpdate.setGender(UserProfile.Gender.valueOf(genderStr.toUpperCase()));
                }
            }
            if (profileData.containsKey("location")) {
                profileUpdate.setLocation((String) profileData.get("location"));
            }
            if (profileData.containsKey("schoolOrOrganization")) {
                profileUpdate.setSchoolOrOrganization((String) profileData.get("schoolOrOrganization"));
            }
            if (profileData.containsKey("majorOrField")) {
                profileUpdate.setMajorOrField((String) profileData.get("majorOrField"));
            }
            if (profileData.containsKey("isProfilePublic")) {
                profileUpdate.setIsProfilePublic((Boolean) profileData.get("isProfilePublic"));
            }
            if (profileData.containsKey("showEmail")) {
                profileUpdate.setShowEmail((Boolean) profileData.get("showEmail"));
            }
            if (profileData.containsKey("showRealName")) {
                profileUpdate.setShowRealName((Boolean) profileData.get("showRealName"));
            }

            UserProfile updatedProfile = userProfileService.updateUserProfile(userId, profileUpdate);
            return ResponseEntity.ok(ApiResponse.success("用户资料更新成功", updatedProfile));
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("更新用户资料失败: " + e.getMessage()));
        }
    }

    /**
     * 更新资料可见性设置 - 前端调用接口
     * PUT /api/users/profile/my/visibility
     */
    @PutMapping("/my/visibility")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<UserProfile>> updateProfileVisibility(
            @RequestBody Map<String, Object> visibilitySettings,
            Authentication authentication) {
        try {
            String username = authentication.getName();
            Long userId = userService.findByUsername(username)
                    .orElseThrow(() -> new RuntimeException("用户不存在"))
                    .getId();

            UserProfile profileUpdate = new UserProfile();

            if (visibilitySettings.containsKey("isProfilePublic")) {
                profileUpdate.setIsProfilePublic((Boolean) visibilitySettings.get("isProfilePublic"));
            }
            if (visibilitySettings.containsKey("showEmail")) {
                profileUpdate.setShowEmail((Boolean) visibilitySettings.get("showEmail"));
            }
            if (visibilitySettings.containsKey("showRealName")) {
                profileUpdate.setShowRealName((Boolean) visibilitySettings.get("showRealName"));
            }

            UserProfile updatedProfile = userProfileService.updateUserProfile(userId, profileUpdate);
            return ResponseEntity.ok(ApiResponse.success("可见性设置更新成功", updatedProfile));
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("更新可见性设置失败: " + e.getMessage()));
        }
    }

    /**
     * 上传用户头像
     * POST /api/users/profile/my/avatar
     */
    @PostMapping("/my/avatar")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Map<String, String>>> uploadAvatar(
            @RequestParam("file") MultipartFile file,
            Authentication authentication) {
        try {
            // 验证文件
            String validationError = userProfileService.validateAvatarFile(file);
            if (validationError != null) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error(validationError));
            }

            String username = authentication.getName();
            Long userId = userService.findByUsername(username)
                    .orElseThrow(() -> new RuntimeException("用户不存在"))
                    .getId();

            String avatarUrl = userProfileService.uploadAvatar(userId, file);

            Map<String, String> result = new HashMap<>();
            result.put("avatarUrl", avatarUrl);
            result.put("message", "头像上传成功");

            return ResponseEntity.ok(ApiResponse.success("头像上传成功", result));
        } catch (IOException e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("文件上传失败: " + e.getMessage()));
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("上传头像失败: " + e.getMessage()));
        }
    }

    /**
     * 删除用户头像
     * DELETE /api/users/profile/my/avatar
     */
    @DeleteMapping("/my/avatar")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<String>> deleteAvatar(Authentication authentication) {
        try {
            String username = authentication.getName();
            Long userId = userService.findByUsername(username)
                    .orElseThrow(() -> new RuntimeException("用户不存在"))
                    .getId();

            userProfileService.deleteAvatar(userId);
            return ResponseEntity.ok(ApiResponse.success("头像删除成功"));
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("删除头像失败: " + e.getMessage()));
        }
    }

    /**
     * 获取所有公开的用户资料
     * GET /api/users/profile/public
     */
    @GetMapping("/public")
    public ResponseEntity<ApiResponse<List<UserProfile>>> getPublicProfiles() {
        try {
            List<UserProfile> profiles = userProfileService.getPublicProfiles();
            return ResponseEntity.ok(ApiResponse.success("获取公开用户资料成功", profiles));
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("获取公开用户资料失败: " + e.getMessage()));
        }
    }

    /**
     * 搜索用户资料
     * GET /api/users/profile/search
     */
    @GetMapping("/search")
    public ResponseEntity<ApiResponse<List<UserProfile>>> searchProfiles(
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) String school,
            @RequestParam(required = false) String location,
            @RequestParam(required = false) UserProfile.Gender gender) {
        try {
            List<UserProfile> profiles;

            if (keyword != null && !keyword.trim().isEmpty()) {
                profiles = userProfileService.searchProfiles(keyword);
            } else if (school != null && !school.trim().isEmpty()) {
                profiles = userProfileService.searchBySchoolOrOrganization(school);
            } else if (location != null && !location.trim().isEmpty()) {
                profiles = userProfileService.searchByLocation(location);
            } else if (gender != null) {
                profiles = userProfileService.getUserProfilesByGender(gender);
            } else {
                profiles = userProfileService.getPublicProfiles();
            }

            return ResponseEntity.ok(ApiResponse.success("搜索用户资料成功", profiles));
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("搜索用户资料失败: " + e.getMessage()));
        }
    }

    /**
     * 获取用户资料统计信息
     * GET /api/users/profile/statistics
     */
    @GetMapping("/statistics")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getProfileStatistics() {
        try {
            Map<String, Object> statistics = new HashMap<>();

            statistics.put("usersWithAvatar", userProfileService.countUsersWithAvatar());
            statistics.put("publicProfiles", userProfileService.countPublicProfiles());

            // 按性别统计
            Map<String, Long> genderStats = new HashMap<>();
            for (UserProfile.Gender gender : UserProfile.Gender.values()) {
                genderStats.put(gender.name(),
                    (long) userProfileService.getUserProfilesByGender(gender).size());
            }
            statistics.put("genderDistribution", genderStats);

            return ResponseEntity.ok(ApiResponse.success("获取用户资料统计成功", statistics));
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("获取用户资料统计失败: " + e.getMessage()));
        }
    }

    /**
     * 获取头像文件信息
     * GET /api/users/profile/avatar-info
     */
    @GetMapping("/avatar-info")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getAvatarInfo(Authentication authentication) {
        try {
            String username = authentication.getName();
            UserProfile profile = userProfileService.getUserProfileByUsername(username)
                    .orElse(null);

            Map<String, Object> info = new HashMap<>();
            if (profile != null && profile.hasAvatar()) {
                info.put("hasAvatar", true);
                info.put("avatarUrl", profile.getAvatarUrl());
                info.put("fileName", profile.getAvatarFileName());
                info.put("fileSize", profile.getAvatarFileSize());
                info.put("contentType", profile.getAvatarContentType());
            } else {
                info.put("hasAvatar", false);
                info.put("avatarUrl", "/images/default-avatar.png");
            }

            return ResponseEntity.ok(ApiResponse.success("获取头像信息成功", info));
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("获取头像信息失败: " + e.getMessage()));
        }
    }

    /**
     * 初始化用户资料信息（管理员功能）
     * POST /api/users/profile/initialize/{userId}
     */
    @PostMapping("/initialize/{userId}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<UserProfile>> initializeUserProfile(@PathVariable Long userId) {
        try {
            UserProfile userProfile = userProfileService.initializeUserProfile(userId);
            return ResponseEntity.ok(ApiResponse.success("初始化用户资料成功", userProfile));
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("初始化用户资料失败: " + e.getMessage()));
        }
    }

    /**
     * 批量更新用户资料的公开状态（管理员功能）
     * PUT /api/users/profile/visibility
     */
    @PutMapping("/visibility")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<String>> batchUpdateProfileVisibility(
            @RequestParam List<Long> userIds,
            @RequestParam boolean isPublic) {
        try {
            userProfileService.updateProfileVisibility(userIds, isPublic);
            return ResponseEntity.ok(ApiResponse.success("用户资料公开状态批量更新成功"));
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("批量更新用户资料公开状态失败: " + e.getMessage()));
        }
    }
}
