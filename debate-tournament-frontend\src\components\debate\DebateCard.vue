<template>
  <div class="debate-card" @click="$emit('click')">
    <div class="debate-card__header">
      <h3 class="debate-card__title">{{ debate.title }}</h3>
      <div class="debate-card__meta">
        <span class="debate-card__category">{{ debate.category }}</span>
        <span class="debate-card__status" :class="`debate-card__status--${debate.status}`">
          {{ statusText }}
        </span>
      </div>
    </div>

    <div v-if="debate.description" class="debate-card__description">
      {{ debate.description }}
    </div>

    <div class="debate-card__stats">
      <div class="debate-card__stat">
        <span class="debate-card__icon debate-card__icon--user"></span>
        <span>{{ debate.participants || debate.participantCount || 0 }}</span>
      </div>
      <div class="debate-card__stat">
        <span class="debate-card__icon debate-card__icon--speech"></span>
        <span>{{ debate.speeches || debate.speechCount || 0 }}</span>
      </div>
      <div class="debate-card__stat">
        <span class="debate-card__icon debate-card__icon--vote"></span>
        <span>{{ debate.votes || debate.voteCount || 0 }}</span>
      </div>
    </div>

    <div class="debate-card__sides" v-if="showDebateSides">
      <div class="side side--supporter">
        <div class="count">{{ debate.supportersCount || 0 }}</div>
        <div class="label">正方</div>
      </div>
      <div class="debate-card__sides-separator">VS</div>
      <div class="side side--opposer">
        <div class="count">{{ debate.opposersCount || 0 }}</div>
        <div class="label">反方</div>
      </div>
    </div>

    <div class="debate-card__footer">
      <div class="debate-card__creator" v-if="debate.creator">
        <img :src="debate.creator.avatar || 'https://via.placeholder.com/24'" :alt="debate.creator.username"
          class="debate-card__avatar">
        <span>{{ debate.creator.username }}</span>
      </div>
      <div class="debate-card__participants" v-if="debate.participants && debate.participants.length">
        <div class="debate-card__participants-avatars">
          <img v-for="(participant, index) in participantAvatars" :key="index"
            :src="participant.avatar || 'https://via.placeholder.com/24'" :alt="participant.name" class="avatar">
        </div>
        <span class="debate-card__participants-count">{{ participantsCount }}</span>
      </div>
      <span class="debate-card__time">{{ formatTime(debate.createTime || debate.createdAt) }}</span>
    </div>
  </div>
</template>

<script>
import DebateCardJS from './js/DebateCard.js';
export default DebateCardJS;
</script>

<style lang="scss" src="./scss/DebateCard.scss"></style>
