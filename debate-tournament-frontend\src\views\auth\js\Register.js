import { ref, reactive, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useUserStore } from '@/store/user';
import { register } from '@/api/auth';
import { useEncryptionStore } from '@/store/encryption';

// 生成验证码
export const generateCaptcha = () => {
  // 生成4位随机数字
  const captcha = Math.floor(1000 + Math.random() * 9000).toString();
  // 使用在线验证码服务，不依赖后端API
  // 添加时间戳参数确保图片不被缓存
  const timestamp = new Date().getTime();
  return `https://dummyimage.com/120x40/e0e0e0/000000.png&text=${captcha}&_=${timestamp}`;
}

// 刷新验证码
export const refreshCaptcha = () => {
  return generateCaptcha();
}

// 验证表单
export const validateForm = async (formRef) => {
  if (!formRef) return false;
  try {
    await formRef.validate();
    return true;
  } catch (error) {
    return false;
  }
}

export default {
  name: 'Register',
  setup() {
    const router = useRouter()
    const userStore = useUserStore()
    const registerForm = ref(null)

    const formData = reactive({
      username: '',
      email: '',
      password: '',
      confirmPassword: '',
      captcha: '',
      agreeTerms: false
    })

    // 校验密码匹配
    const validatePasswordMatch = (rule, value, callback) => {
      if (value !== formData.password) {
        callback(new Error('两次输入的密码不一致'));
      } else {
        callback();
      }
    };

    // Element Plus表单验证规则
    const rules = {
      username: [
        { required: true, message: '请输入用户名', trigger: 'blur' },
        { min: 3, max: 20, message: '用户名长度在3到20个字符之间', trigger: 'blur' }
      ],
      email: [
        { required: true, message: '请输入邮箱', trigger: 'blur' },
        { type: 'email', message: '请输入有效的邮箱地址', trigger: 'blur' }
      ],
      password: [
        { required: true, message: '请输入密码', trigger: 'blur' },
        { min: 8, message: '密码长度不能少于8个字符', trigger: 'blur' }
      ],
      confirmPassword: [
        { required: true, message: '请确认密码', trigger: 'blur' },
        { validator: validatePasswordMatch, trigger: 'blur' }
      ],
      captcha: [
        { required: true, message: '请输入验证码', trigger: 'blur' },
        { len: 4, message: '验证码必须是4位', trigger: 'blur' },
        {
          validator: (rule, value, callback) => {
            // 这里的 currentCaptcha 只在 setup 里有用，导出方法不依赖它
            callback();
          },
          trigger: 'blur'
        }
      ],
      agreeTerms: [
        {
          validator: (rule, value, callback) => {
            if (!value) {
              callback(new Error('请同意服务条款和隐私政策'));
            } else {
              callback();
            }
          },
          trigger: 'change'
        }
      ]
    }

    const errors = reactive({
      username: '',
      email: '',
      password: '',
      confirmPassword: '',
      captcha: '',
      agreeTerms: '',
      general: ''
    })

    const loading = ref(false)
    const showPassword = reactive({
      password: false,
      confirmPassword: false
    })
    // 使用本地静态图像作为验证码，避免后端API错误
    const captchaUrl = ref('/api/captcha/image?' + Date.now())
    // 当前验证码的值 (模拟)
    const currentCaptcha = ref('1234')

    const handleRegister = async () => {
      if (!registerForm.value) return;

      await registerForm.value.validate(async (valid) => {
        if (!valid) return;

        loading.value = true;
        errors.general = '';

        try {
          const encryptionStore = useEncryptionStore();
          await encryptionStore.initialize();
          const encryptedRequest = encryptionStore.encryptRequest({
            username: formData.username,
            email: formData.email,
            password: formData.password,
            captcha: formData.captcha
          });

          const response = await register(encryptedRequest);
          const decryptedResponse = encryptionStore.decryptResponse(response.data);

          userStore.setUser(decryptedResponse.user);
          userStore.setToken(decryptedResponse.token);

          router.push('/debate-hall');
          loading.value = false;
        } catch (error) {
          if (error.response?.status === 400) {
            if (error.response.data.field === 'captcha') {
              errors.captcha = error.response.data.message;
              currentCaptcha.value = generateCaptcha();
              captchaUrl.value = currentCaptcha.value;
            } else {
              errors[error.response.data.field] = error.response.data.message;
            }
          } else {
            errors.general = error.response?.data?.message || '注册失败,请重试';
          }
          loading.value = false;
        }
      });
    }

    const togglePasswordVisibility = (field) => {
      showPassword[field] = !showPassword[field]
    }

    const goToLogin = () => {
      router.push('/login')
    }

    // 组件挂载时初始化验证码
    onMounted(() => {
      captchaUrl.value = generateCaptcha();
    })

    const refreshCaptcha = () => {
      currentCaptcha.value = generateCaptcha();
      captchaUrl.value = currentCaptcha.value;
    };

    return {
      formData,
      errors,
      loading,
      showPassword,
      captchaUrl,
      handleRegister,
      togglePasswordVisibility,
      refreshCaptcha,
      goToLogin,
      rules,
      registerForm
    }
  }
}
