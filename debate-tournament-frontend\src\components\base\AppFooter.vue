<template>
  <el-footer class="app-footer">
    <div class="container">
      <div class="app-footer__content">
        <div class="app-footer__top">
          <div class="app-footer__logo">
            <router-link to="/" class="logo">
              <el-image class="logo__image" src="/logo.png" alt="AI辩论赛" fit="contain">
                <template #error>
                  <div class="logo__text">AI辩论赛</div>
                </template>
              </el-image>
            </router-link>
            <p class="app-footer__slogan">
              探索AI辩论的无限可能
            </p>
          </div>

          <div class="app-footer__links">
            <div class="footer-links-group">
              <h4 class="footer-links-group__title">平台</h4>
              <ul class="footer-links-group__list">
                <li><router-link to="/">首页</router-link></li>
                <li><router-link to="/debate-hall">辩论大厅</router-link></li>
                <li><router-link to="/ai-models">AI模型</router-link></li>
                <li><router-link to="/about">关于我们</router-link></li>
              </ul>
            </div>

            <div class="footer-links-group">
              <h4 class="footer-links-group__title">资源</h4>
              <ul class="footer-links-group__list">
                <li><router-link to="/help-center">帮助中心</router-link></li>
                <li><router-link to="/faq">常见问题</router-link></li>
                <li><router-link to="/contact">联系我们</router-link></li>
              </ul>
            </div>

            <div class="footer-links-group">
              <h4 class="footer-links-group__title">法律</h4>
              <ul class="footer-links-group__list">
                <li><router-link to="/terms">服务条款</router-link></li>
                <li><router-link to="/privacy">隐私政策</router-link></li>
              </ul>
            </div>

            <div class="footer-links-group footer-links-group--contact">
              <h4 class="footer-links-group__title">联系我们</h4>
              <ul class="footer-links-group__list">
                <li>
                  <el-icon><Message /></el-icon>
                  <a href="mailto:<EMAIL>"><EMAIL></a>
                </li>
                <li>
                  <el-icon><Location /></el-icon>
                  <span>中国，北京</span>
                </li>
              </ul>
              <div class="social-links">
                <a href="#" class="social-link" title="微信">
                  <el-icon><ChatDotRound /></el-icon>
                </a>
                <a href="#" class="social-link" title="微博">
                  <el-icon><Share /></el-icon>
                </a>
                <a href="#" class="social-link" title="GitHub">
                  <el-icon><Link /></el-icon>
                </a>
              </div>
            </div>
          </div>
        </div>

        <div class="app-footer__bottom">
          <div class="app-footer__copyright">
            &copy; {{ currentYear }} AI辩论赛平台 版权所有
          </div>
          <div class="app-footer__language">
            <el-dropdown trigger="click" @command="handleLanguageChange">
              <span class="language-selector">
                <el-icon><Basketball /></el-icon>
                {{ currentLanguage }}
                <el-icon class="el-icon--right"><arrow-down /></el-icon>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="zh-CN">简体中文</el-dropdown-item>
                  <el-dropdown-item command="en-US">English</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </div>
    </div>
  </el-footer>
</template>

<script setup>
import { ref, computed } from 'vue';
import {
  Message, Location, ChatDotRound, Share,
  Link, ArrowDown, Basketball, Place
} from '@element-plus/icons-vue';

// 当前年份
const currentYear = computed(() => new Date().getFullYear());

// 当前语言
const currentLanguage = ref('简体中文');

/**
 * 处理语言切换
 */
function handleLanguageChange(lang) {
  if (lang === 'zh-CN') {
    currentLanguage.value = '简体中文';
  } else if (lang === 'en-US') {
    currentLanguage.value = 'English';
  }
  // 这里可以添加实际的语言切换逻辑
  console.log('语言已切换为:', lang);
}
</script>

<style lang="scss">
.app-footer {
  background-color: #f5f7fa;
  padding: 60px 0 20px;
  color: #606266;

  &__content {
    display: flex;
    flex-direction: column;
    gap: 40px;
  }

  &__top {
    display: flex;
    flex-wrap: wrap;
    gap: 40px;

    @media (max-width: 768px) {
      flex-direction: column;
    }
  }

  &__logo {
    flex: 0 0 240px;

    .logo {
      display: inline-block;
      margin-bottom: 15px;

      &__image {
        height: 40px;
        width: auto;
      }

      &__text {
        font-size: 20px;
        font-weight: bold;
        color: var(--primary-color);
      }
    }
  }

  &__slogan {
    font-size: 14px;
    color: #909399;
    margin-top: 10px;
  }

  &__links {
    flex: 1;
    display: flex;
    flex-wrap: wrap;
    gap: 30px;
    justify-content: space-between;

    @media (max-width: 768px) {
      gap: 20px;
    }
  }

  &__bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 20px;
    border-top: 1px solid #e4e7ed;

    @media (max-width: 576px) {
      flex-direction: column;
      gap: 15px;
    }
  }

  &__copyright {
    font-size: 14px;
    color: #909399;
  }

  &__language {
    .language-selector {
      display: flex;
      align-items: center;
      gap: 5px;
      cursor: pointer;
      padding: 5px 10px;
      border-radius: 4px;
      transition: background-color 0.3s;

      &:hover {
        background-color: #ecf5ff;
      }
    }
  }
}

.footer-links-group {
  min-width: 120px;

  &__title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 15px;
    color: #303133;
  }

  &__list {
    list-style: none;
    padding: 0;
    margin: 0;

    li {
      margin-bottom: 10px;

      a {
        color: #606266;
        text-decoration: none;
        transition: color 0.3s;

        &:hover {
          color: var(--primary-color);
        }
      }

      display: flex;
      align-items: center;
      gap: 5px;
    }
  }

  &--contact {
    min-width: 200px;
  }
}

.social-links {
  display: flex;
  gap: 15px;
  margin-top: 15px;

  .social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: #e4e7ed;
    color: #606266;
    transition: all 0.3s;

    &:hover {
      background-color: var(--primary-color);
      color: white;
    }
  }
}
</style>
