import request from './index'

// 设置基础URL
const API_URL = '/api/auth';

/**
 * 用户登录
 * @param {Object} credentials - 登录凭证
 * @param {string} credentials.username - 用户名
 * @param {string} credentials.password - 密码
 * @returns {Promise} - 返回登录结果
 */
export function login(data) {
  return request({
    url: '/api/auth/login',
    method: 'post',
    data
  })
}

/**
 * 用户注册
 * @param {Object} userData - 用户数据
 * @param {string} userData.username - 用户名
 * @param {string} userData.email - 邮箱
 * @param {string} userData.password - 密码
 * @returns {Promise} - 返回注册结果
 */
export function register(data) {
  return request({
    url: '/api/auth/register',
    method: 'post',
    data
  })
}

/**
 * 用户登出
 * @returns {Promise} - 返回登出结果
 */
export function logout() {
  return request({
    url: '/api/auth/logout',
    method: 'post'
  })
}

/**
 * 获取用户信息
 * @returns {Promise} - 返回用户信息
 */
export function getUserInfo() {
  return request({
    url: '/api/auth/me',
    method: 'get'
  })
}

/**
 * 更新用户信息
 * @param {Object} userData - 用户数据
 * @returns {Promise} - 返回更新结果
 */
export function updateUserInfo(userData) {
  return request({
    url: '/api/auth/me',
    method: 'put',
    data: userData
  })
}

/**
 * 修改密码
 * @param {Object} passwordData - 密码数据
 * @param {string} passwordData.oldPassword - 旧密码
 * @param {string} passwordData.newPassword - 新密码
 * @returns {Promise} - 返回修改结果
 */
export function changePassword(passwordData) {
  return request({
    url: '/api/auth/password',
    method: 'put',
    data: passwordData
  })
}

// 获取验证码
export function getCaptcha() {
  return request({
    url: '/api/auth/captcha',
    method: 'get',
    responseType: 'blob'
  })
}

// 验证验证码
export function verifyCaptcha(code) {
  return request({
    url: '/api/auth/verify-captcha',
    method: 'post',
    data: { code }
  })
}

// 忘记密码
export function forgotPassword(data) {
  return request({
    url: '/api/auth/forgot-password',
    method: 'post',
    data
  })
}

// 重置密码
export function resetPassword(data) {
  return request({
    url: '/api/auth/reset-password',
    method: 'post',
    data
  })
}

// 验证邮箱
export function verifyEmail(token) {
  return request({
    url: '/api/auth/verify-email',
    method: 'post',
    data: { token }
  })
}

// 重新发送验证邮件
export function resendVerificationEmail(email) {
  return request({
    url: '/api/auth/resend-verification',
    method: 'post',
    data: { email }
  })
} 