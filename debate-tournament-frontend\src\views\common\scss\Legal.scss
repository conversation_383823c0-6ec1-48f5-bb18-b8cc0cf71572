@use '@/styles/variables' as v;
@use '@/styles/mixins' as m;

// 法律页面通用样式
.legal-page {
    padding: 40px 0;
    color: #333;
    line-height: 1.6;

    .container {
        max-width: 1000px;
        margin: 0 auto;
        padding: 0 20px;
    }

    .page-header {
        margin-bottom: 40px;
        text-align: center;

        .page-title {
            font-size: 32px;
            font-weight: 600;
            margin-bottom: 10px;
            color: #333;
        }

        .page-subtitle {
            font-size: 16px;
            color: #666;
        }

        .page-updated {
            font-size: 14px;
            color: #888;
            margin-top: 10px;
        }
    }

    .legal-content {
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        padding: 30px;

        .section {
            margin-bottom: 30px;

            &:last-child {
                margin-bottom: 0;
            }

            .section-title {
                font-size: 22px;
                font-weight: 600;
                margin-bottom: 15px;
                color: #333;
                padding-bottom: 10px;
                border-bottom: 1px solid #eee;
            }

            .section-content {
                font-size: 16px;
                color: #444;
                white-space: pre-line;

                p {
                    margin-bottom: 15px;
                }

                ul,
                ol {
                    padding-left: 20px;
                    margin-bottom: 15px;

                    li {
                        margin-bottom: 8px;
                    }
                }
            }
        }
    }

    .legal-footer {
        margin-top: 40px;
        text-align: center;

        .footer-links {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 20px;

            .footer-link {
                color: #1976d2;
                text-decoration: none;

                &:hover {
                    text-decoration: underline;
                }
            }
        }

        .copyright {
            font-size: 14px;
            color: #666;
        }
    }
}

// 帮助中心特定样式
.help-center {
    .help-container {
        display: flex;
        gap: 30px;

        .help-sidebar {
            width: 250px;
            flex-shrink: 0;

            .category-list {
                background: #fff;
                border-radius: 8px;
                box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
                overflow: hidden;

                .category-item {
                    padding: 15px 20px;
                    cursor: pointer;
                    border-bottom: 1px solid #eee;
                    transition: all 0.3s;

                    &:last-child {
                        border-bottom: none;
                    }

                    &.active {
                        background: #f0f7ff;
                        color: #1976d2;
                        font-weight: 500;
                    }

                    &:hover:not(.active) {
                        background: #f5f5f5;
                    }

                    .category-title {
                        display: flex;
                        align-items: center;
                        gap: 10px;

                        .icon {
                            font-size: 18px;
                        }
                    }
                }
            }

            .help-contact {
                margin-top: 20px;
                background: #f0f7ff;
                border-radius: 8px;
                padding: 20px;
                text-align: center;

                .contact-title {
                    font-weight: 600;
                    margin-bottom: 10px;
                    color: #333;
                }

                .contact-text {
                    font-size: 14px;
                    color: #666;
                    margin-bottom: 15px;
                }

                .contact-button {
                    width: 100%;
                }
            }
        }

        .help-content {
            flex: 1;
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
            padding: 30px;

            .topic-header {
                margin-bottom: 20px;

                .topic-title {
                    font-size: 24px;
                    font-weight: 600;
                    margin-bottom: 10px;
                    color: #333;
                }

                .topic-category {
                    font-size: 14px;
                    color: #666;
                }
            }

            .topic-content {
                font-size: 16px;
                color: #444;
                line-height: 1.6;
            }

            .topic-list {
                .topic-item {
                    padding: 15px;
                    border-bottom: 1px solid #eee;
                    cursor: pointer;
                    transition: all 0.3s;

                    &:last-child {
                        border-bottom: none;
                    }

                    &.active {
                        background: #f0f7ff;
                    }

                    &:hover:not(.active) {
                        background: #f5f5f5;
                    }

                    .topic-item-title {
                        font-weight: 500;
                        color: #333;
                        margin-bottom: 5px;
                    }

                    .topic-item-preview {
                        font-size: 14px;
                        color: #666;
                        display: -webkit-box;
                        -webkit-line-clamp: 2;
                        -webkit-box-orient: vertical;
                        overflow: hidden;
                    }
                }
            }
        }
    }
}

// FAQ特定样式
.faq-page {
    .faq-list {
        .faq-category {
            margin-bottom: 40px;

            &:last-child {
                margin-bottom: 0;
            }

            .category-title {
                font-size: 24px;
                font-weight: 600;
                margin-bottom: 20px;
                color: #333;
                padding-bottom: 10px;
                border-bottom: 1px solid #eee;
            }

            .faq-items {
                .faq-item {
                    margin-bottom: 15px;
                    border-radius: 8px;
                    overflow: hidden;
                    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);

                    .faq-question {
                        padding: 15px 20px;
                        background: #f5f5f5;
                        font-weight: 500;
                        cursor: pointer;
                        display: flex;
                        justify-content: space-between;
                        align-items: center;

                        &:hover {
                            background: #eee;
                        }

                        .question-text {
                            flex: 1;
                        }

                        .icon {
                            transition: transform 0.3s;

                            &.expanded {
                                transform: rotate(180deg);
                            }
                        }
                    }

                    .faq-answer {
                        padding: 0;
                        max-height: 0;
                        overflow: hidden;
                        transition: all 0.3s ease;

                        &.expanded {
                            padding: 20px;
                            max-height: 500px;
                        }

                        .answer-content {
                            color: #444;
                        }
                    }
                }
            }
        }
    }
}

// 联系我们特定样式
.contact-page {
    .contact-container {
        display: flex;
        gap: 30px;

        @media (max-width: 768px) {
            flex-direction: column;
        }

        .contact-form-section {
            flex: 1;

            .form-title {
                font-size: 24px;
                font-weight: 600;
                margin-bottom: 20px;
                color: #333;
            }
        }

        .contact-info-section {
            width: 300px;

            @media (max-width: 768px) {
                width: 100%;
            }

            .info-title {
                font-size: 20px;
                font-weight: 600;
                margin-bottom: 20px;
                color: #333;
            }

            .info-cards {
                display: flex;
                flex-direction: column;
                gap: 15px;
                margin-bottom: 30px;

                .info-card {
                    background: #f5f5f5;
                    border-radius: 8px;
                    padding: 15px;
                    display: flex;
                    align-items: center;
                    gap: 15px;

                    .info-icon {
                        width: 40px;
                        height: 40px;
                        border-radius: 50%;
                        background: #1976d2;
                        color: white;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        font-size: 20px;
                    }

                    .info-content {
                        .info-label {
                            font-weight: 500;
                            color: #333;
                            margin-bottom: 5px;
                        }

                        .info-value {
                            color: #666;
                            font-size: 14px;
                        }
                    }
                }
            }

            .social-media {
                margin-bottom: 30px;

                .social-title {
                    font-size: 18px;
                    font-weight: 500;
                    margin-bottom: 15px;
                    color: #333;
                }

                .social-links {
                    display: flex;
                    gap: 15px;

                    .social-link {
                        width: 40px;
                        height: 40px;
                        border-radius: 50%;
                        background: #f0f0f0;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        color: #555;
                        font-size: 20px;
                        transition: all 0.3s;

                        &:hover {
                            background: #1976d2;
                            color: white;
                        }
                    }
                }
            }

            .common-questions {
                .questions-title {
                    font-size: 18px;
                    font-weight: 500;
                    margin-bottom: 15px;
                    color: #333;
                }

                .questions-list {
                    .question-item {
                        margin-bottom: 10px;

                        .question-link {
                            color: #1976d2;
                            text-decoration: none;
                            display: flex;
                            align-items: center;
                            gap: 8px;

                            &:hover {
                                text-decoration: underline;
                            }

                            .icon {
                                font-size: 14px;
                            }
                        }
                    }
                }
            }
        }
    }
}
