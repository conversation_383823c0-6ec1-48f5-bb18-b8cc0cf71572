/**
 * 辩论卡片组件的业务逻辑
 */
import { computed } from 'vue';

export default {
  name: 'DebateCard',

  props: {
    /**
     * 辩论数据对象
     */
    debate: {
      type: Object,
      required: true
    },

    /**
     * 是否显示辩论双方信息
     */
    showSides: {
      type: Boolean,
      default: false
    }
  },

  emits: ['click'],

  setup(props) {
    /**
     * 状态文本映射
     */
    const statusText = computed(() => {
      const statusMap = {
        'waiting': '等待中',
        'active': '进行中',
        'paused': '已暂停',
        'completed': '已结束'
      };
      return statusMap[props.debate.status] || '未知';
    });

    /**
     * 是否显示辩论两方的信息
     */
    const showDebateSides = computed(() => {
      return props.showSides ||
        (props.debate.supportersCount !== undefined && props.debate.opposersCount !== undefined);
    });

    /**
     * 辩论参与者头像数组，最多显示3个
     */
    const participantAvatars = computed(() => {
      if (!props.debate.participants || !Array.isArray(props.debate.participants)) {
        return [];
      }

      return props.debate.participants.slice(0, 3);
    });

    /**
     * 辩论参与者显示文本
     */
    const participantsCount = computed(() => {
      if (!props.debate.participants || !Array.isArray(props.debate.participants)) {
        return '';
      }

      const total = props.debate.participants.length;
      const displayed = participantAvatars.value.length;

      if (total <= displayed) {
        return `${total} 人参与`;
      } else {
        return `+${total - displayed} 人参与`;
      }
    });

    /**
     * 格式化时间为相对时间
     * @param {string|number} timestamp - 时间戳或日期字符串
     * @returns {string} - 格式化后的相对时间
     */
    function formatTime(timestamp) {
      if (!timestamp) return '未知时间';

      const date = new Date(timestamp);
      const now = new Date();
      const diff = Math.floor((now - date) / 1000); // 秒数差

      if (diff < 60) {
        return '刚刚';
      } else if (diff < 3600) {
        return Math.floor(diff / 60) + '分钟前';
      } else if (diff < 86400) {
        return Math.floor(diff / 3600) + '小时前';
      } else if (diff < 2592000) {
        return Math.floor(diff / 86400) + '天前';
      } else {
        return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
      }
    }

    return {
      statusText,
      showDebateSides,
      participantAvatars,
      participantsCount,
      formatTime
    };
  }
};
