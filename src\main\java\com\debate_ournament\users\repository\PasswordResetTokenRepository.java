package com.debate_ournament.users.repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.debate_ournament.users.entity.PasswordResetToken;

/**
 * 密码重置令牌数据访问接口
 */
@Repository
public interface PasswordResetTokenRepository extends JpaRepository<PasswordResetToken, Long> {

    /**
     * 根据令牌查找记录
     */
    Optional<PasswordResetToken> findByToken(String token);

    /**
     * 根据用户ID查找最新的重置令牌
     */
    @Query("SELECT prt FROM PasswordResetToken prt WHERE prt.user.id = :userId ORDER BY prt.createdAt DESC")
    List<PasswordResetToken> findByUserIdOrderByCreatedAtDesc(@Param("userId") Long userId);

    /**
     * 查找有效的重置令牌（未使用且未过期）
     */
    @Query("SELECT prt FROM PasswordResetToken prt WHERE prt.token = :token AND prt.used = false AND prt.expiresAt > :currentTime")
    Optional<PasswordResetToken> findValidToken(@Param("token") String token, @Param("currentTime") LocalDateTime currentTime);

    /**
     * 查找已过期的令牌
     */
    @Query("SELECT prt FROM PasswordResetToken prt WHERE prt.expiresAt < :currentTime")
    List<PasswordResetToken> findExpiredTokens(@Param("currentTime") LocalDateTime currentTime);

    /**
     * 查找指定用户的未使用令牌
     */
    @Query("SELECT prt FROM PasswordResetToken prt WHERE prt.user.id = :userId AND prt.used = false")
    List<PasswordResetToken> findUnusedByUserId(@Param("userId") Long userId);

    /**
     * 查找指定时间之前创建的未使用令牌
     */
    @Query("SELECT prt FROM PasswordResetToken prt WHERE prt.used = false AND prt.createdAt < :cutoffTime")
    List<PasswordResetToken> findUnusedTokensBefore(@Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * 删除过期的令牌
     */
    @Modifying
    @Query("DELETE FROM PasswordResetToken prt WHERE prt.expiresAt < :currentTime")
    int deleteExpiredTokens(@Param("currentTime") LocalDateTime currentTime);

    /**
     * 删除已使用的令牌
     */
    @Modifying
    @Query("DELETE FROM PasswordResetToken prt WHERE prt.used = true AND prt.usedAt < :cutoffTime")
    int deleteUsedTokens(@Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * 删除指定用户的所有令牌
     */
    @Modifying
    @Query("DELETE FROM PasswordResetToken prt WHERE prt.user.id = :userId")
    int deleteAllByUserId(@Param("userId") Long userId);

    /**
     * 标记令牌为已使用
     */
    @Modifying
    @Query("UPDATE PasswordResetToken prt SET prt.used = true, prt.usedAt = :usedAt, prt.clientIp = :clientIp WHERE prt.token = :token")
    int markTokenAsUsed(@Param("token") String token, @Param("usedAt") LocalDateTime usedAt, @Param("clientIp") String clientIp);

    /**
     * 禁用指定用户的所有未使用令牌
     */
    @Modifying
    @Query("UPDATE PasswordResetToken prt SET prt.used = true WHERE prt.user.id = :userId AND prt.used = false")
    int disableAllUnusedByUserId(@Param("userId") Long userId);

    /**
     * 统计指定用户在时间范围内的重置尝试次数
     */
    @Query("SELECT COUNT(prt) FROM PasswordResetToken prt WHERE prt.user.id = :userId AND prt.createdAt > :cutoffTime")
    long countResetAttemptsByUserId(@Param("userId") Long userId, @Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * 查找最近成功使用的令牌
     */
    @Query("SELECT prt FROM PasswordResetToken prt WHERE prt.used = true AND prt.usedAt > :cutoffTime ORDER BY prt.usedAt DESC")
    List<PasswordResetToken> findRecentSuccessfulResets(@Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * 检查令牌是否存在
     */
    boolean existsByToken(String token);

    /**
     * 检查用户是否有有效的待用令牌
     */
    @Query("SELECT COUNT(prt) > 0 FROM PasswordResetToken prt WHERE prt.user.id = :userId AND prt.used = false AND prt.expiresAt > :currentTime")
    boolean hasValidPendingToken(@Param("userId") Long userId, @Param("currentTime") LocalDateTime currentTime);

    /**
     * 根据用户邮箱查找最新的重置令牌
     */
    @Query("SELECT prt FROM PasswordResetToken prt WHERE prt.user.email = :email ORDER BY prt.createdAt DESC")
    List<PasswordResetToken> findByUserEmailOrderByCreatedAtDesc(@Param("email") String email);

    /**
     * 查找指定IP地址的重置历史
     */
    @Query("SELECT prt FROM PasswordResetToken prt WHERE prt.clientIp = :clientIp AND prt.createdAt > :cutoffTime ORDER BY prt.createdAt DESC")
    List<PasswordResetToken> findResetHistoryByIp(@Param("clientIp") String clientIp, @Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * 统计在指定时间段内创建的令牌总数
     */
    @Query("SELECT COUNT(prt) FROM PasswordResetToken prt WHERE prt.createdAt BETWEEN :startTime AND :endTime")
    long countTokensCreatedBetween(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);
}
