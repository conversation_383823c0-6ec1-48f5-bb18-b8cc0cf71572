<template>
  <div class="legal-page help-center">
    <div class="container">
      <div class="page-header">
        <h1 class="page-title">帮助中心</h1>
        <p class="page-subtitle">寻找您需要的帮助和支持</p>
      </div>

      <div class="help-container">
        <!-- 左侧分类导航 -->
        <div class="help-sidebar">
          <div class="category-list">
            <div
              v-for="category in helpCategories"
              :key="category.id"
              class="category-item"
              :class="{ active: activeCategory.id === category.id }"
              @click="selectCategory(category)"
            >
              <div class="category-title">
                <el-icon><component :is="category.icon" /></el-icon>
                <span>{{ category.title }}</span>
              </div>
            </div>
          </div>

          <div class="help-contact">
            <h3 class="contact-title">需要更多帮助？</h3>
            <p class="contact-text">如果您没有找到所需的信息，可以直接联系我们的客服团队。</p>
            <el-button type="primary" class="contact-button" @click="goToContact">
              联系我们
            </el-button>
          </div>
        </div>

        <!-- 右侧内容区域 -->
        <div class="help-content">
          <template v-if="activeCategory.topics.length > 1">
            <h2>{{ activeCategory.title }}</h2>
            <div class="topic-list">
              <div
                v-for="topic in activeCategory.topics"
                :key="topic.id"
                class="topic-item"
                :class="{ active: activeTopic.id === topic.id }"
                @click="selectTopic(topic)"
              >
                <h3 class="topic-item-title">{{ topic.title }}</h3>
                <p class="topic-item-preview">{{ topic.content }}</p>
              </div>
            </div>
          </template>

          <template v-else>
            <div class="topic-header">
              <h2 class="topic-title">{{ activeTopic.title }}</h2>
              <div class="topic-category">{{ activeCategory.title }}</div>
            </div>
            <div class="topic-content">
              {{ activeTopic.content }}
            </div>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<script src="./js/HelpCenter.js"></script>

<style lang="scss">
@use './scss/Legal.scss';
</style>
