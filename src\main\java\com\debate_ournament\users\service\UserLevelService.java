package com.debate_ournament.users.service;

import java.util.List;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.debate_ournament.users.entity.User;
import com.debate_ournament.users.entity.UserLevel;
import com.debate_ournament.users.repository.UserLevelRepository;
import com.debate_ournament.users.repository.UserRepository;

/**
 * 用户等级服务类
 */
@Service
@Transactional
public class UserLevelService {

    @Autowired
    private UserLevelRepository userLevelRepository;

    @Autowired
    private UserRepository userRepository;

    /**
     * 为新用户创建等级信息
     * @param user 用户
     * @return 用户等级信息
     */
    public UserLevel createUserLevel(User user) {
        if (userLevelRepository.existsByUserId(user.getId())) {
            throw new IllegalArgumentException("用户已存在等级信息");
        }

        UserLevel userLevel = new UserLevel(user);
        return userLevelRepository.save(userLevel);
    }

    /**
     * 根据用户ID获取等级信息
     * @param userId 用户ID
     * @return 用户等级信息
     */
    @Transactional(readOnly = true)
    public Optional<UserLevel> getUserLevel(Long userId) {
        return userLevelRepository.findByUserId(userId);
    }

    /**
     * 根据用户名获取等级信息
     * @param username 用户名
     * @return 用户等级信息
     */
    @Transactional(readOnly = true)
    public Optional<UserLevel> getUserLevelByUsername(String username) {
        return userLevelRepository.findByUsername(username);
    }

    /**
     * 为用户增加经验值
     * @param userId 用户ID
     * @param experience 经验值
     * @return 是否升级了
     */
    public boolean addExperience(Long userId, long experience) {
        UserLevel userLevel = getUserLevel(userId)
                .orElseThrow(() -> new IllegalArgumentException("用户等级信息不存在"));

        boolean leveledUp = userLevel.addExperience(experience);
        userLevelRepository.save(userLevel);

        return leveledUp;
    }

    /**
     * 记录辩论结果
     * @param userId 用户ID
     * @param isWin 是否获胜
     * @param experienceGained 获得的经验值
     */
    public void recordDebateResult(Long userId, boolean isWin, long experienceGained) {
        UserLevel userLevel = getUserLevel(userId)
                .orElseThrow(() -> new IllegalArgumentException("用户等级信息不存在"));

        // 记录辩论结果
        userLevel.recordDebateResult(isWin);

        // 增加经验值
        userLevel.addExperience(experienceGained);

        userLevelRepository.save(userLevel);
    }

    /**
     * 获取等级排行榜
     * @return 用户等级列表
     */
    @Transactional(readOnly = true)
    public List<UserLevel> getLevelLeaderboard() {
        return userLevelRepository.findTopUsersByLevel();
    }

    /**
     * 获取胜率排行榜
     * @param minDebates 最少辩论次数
     * @return 用户等级列表
     */
    @Transactional(readOnly = true)
    public List<UserLevel> getWinRateLeaderboard(int minDebates) {
        return userLevelRepository.findTopUsersByWinRate(minDebates);
    }

    /**
     * 获取连胜排行榜
     * @return 用户等级列表
     */
    @Transactional(readOnly = true)
    public List<UserLevel> getCurrentStreakLeaderboard() {
        return userLevelRepository.findTopUsersByCurrentStreak();
    }

    /**
     * 获取用户在等级排行榜中的排名
     * @param userId 用户ID
     * @return 排名
     */
    @Transactional(readOnly = true)
    public long getUserRank(Long userId) {
        return userLevelRepository.findUserRankByLevel(userId);
    }

    /**
     * 获取指定等级范围内的用户数量
     * @param minLevel 最小等级
     * @param maxLevel 最大等级
     * @return 用户数量
     */
    @Transactional(readOnly = true)
    public long getUserCountByLevelRange(int minLevel, int maxLevel) {
        return userLevelRepository.countUsersByLevelRange(minLevel, maxLevel);
    }

    /**
     * 获取平均等级
     * @return 平均等级
     */
    @Transactional(readOnly = true)
    public Double getAverageLevel() {
        return userLevelRepository.getAverageLevel();
    }

    /**
     * 获取最高等级
     * @return 最高等级
     */
    @Transactional(readOnly = true)
    public Integer getMaxLevel() {
        return userLevelRepository.getMaxLevel();
    }

    /**
     * 批量为用户增加经验值
     * @param userIds 用户ID列表
     * @param experience 经验值
     */
    public void addExperienceToUsers(List<Long> userIds, long experience) {
        userLevelRepository.addExperienceToUsers(userIds, experience);

        // 检查每个用户是否需要升级
        for (Long userId : userIds) {
            getUserLevel(userId).ifPresent(userLevel -> {
                // 重新检查等级并保存
                userLevel.addExperience(0); // 触发等级检查
                userLevelRepository.save(userLevel);
            });
        }
    }

    /**
     * 根据辩论结果计算经验值
     * @param isWin 是否获胜
     * @param opponentLevel 对手等级
     * @param debateDuration 辩论时长（分钟）
     * @return 经验值
     */
    public long calculateExperience(boolean isWin, int opponentLevel, int debateDuration) {
        long baseExperience = 10; // 基础经验值

        // 胜利奖励
        if (isWin) {
            baseExperience += 20;
        } else {
            baseExperience += 5; // 参与奖励
        }

        // 对手等级影响
        if (opponentLevel > 0) {
            baseExperience += Math.max(1, opponentLevel / 5);
        }

        // 辩论时长影响
        if (debateDuration > 0) {
            baseExperience += Math.min(10, debateDuration / 5);
        }

        return baseExperience;
    }

    /**
     * 初始化用户等级信息（用于已存在的用户）
     * @param userId 用户ID
     * @return 用户等级信息
     */
    public UserLevel initializeUserLevel(Long userId) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new IllegalArgumentException("用户不存在"));

        if (userLevelRepository.existsByUserId(userId)) {
            return getUserLevel(userId).get();
        }

        return createUserLevel(user);
    }

    /**
     * 删除用户等级信息
     * @param userId 用户ID
     */
    public void deleteUserLevel(Long userId) {
        userLevelRepository.deleteByUserId(userId);
    }

    /**
     * 更新用户等级信息
     * @param userLevel 用户等级信息
     * @return 更新后的用户等级信息
     */
    public UserLevel updateUserLevel(UserLevel userLevel) {
        return userLevelRepository.save(userLevel);
    }
}
