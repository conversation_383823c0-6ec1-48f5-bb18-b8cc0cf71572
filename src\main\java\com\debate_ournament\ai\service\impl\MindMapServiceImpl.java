package com.debate_ournament.ai.service.impl;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.debate_ournament.ai.entity.AiConfig;
import com.debate_ournament.ai.entity.AiProvider;
import com.debate_ournament.ai.entity.ChatMessage;
import com.debate_ournament.ai.entity.MindMapNode;
import com.debate_ournament.ai.entity.MindMapResponse;
import com.debate_ournament.ai.repository.AiConfigRepository;
import com.debate_ournament.ai.repository.ChatMessageRepository;
import com.debate_ournament.ai.repository.MindMapResponseRepository;
import com.debate_ournament.ai.service.AiClient;
import com.debate_ournament.ai.service.AiClientFactory;
import com.debate_ournament.ai.service.MindMapService;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * 思维导图生成服务实现类
 */
@Service
@Transactional
public class MindMapServiceImpl implements MindMapService {

    private static final Logger logger = LoggerFactory.getLogger(MindMapServiceImpl.class);

    private final MindMapResponseRepository mindMapResponseRepository;
    private final ChatMessageRepository chatMessageRepository;
    private final AiConfigRepository aiConfigRepository;
    private final AiClientFactory aiClientFactory;
    private final ObjectMapper objectMapper;

    public MindMapServiceImpl(
            MindMapResponseRepository mindMapResponseRepository,
            ChatMessageRepository chatMessageRepository,
            AiConfigRepository aiConfigRepository,
            AiClientFactory aiClientFactory) {
        this.mindMapResponseRepository = mindMapResponseRepository;
        this.chatMessageRepository = chatMessageRepository;
        this.aiConfigRepository = aiConfigRepository;
        this.aiClientFactory = aiClientFactory;
        this.objectMapper = new ObjectMapper();
    }

    @Override
    public MindMapResponse generateMindMap(String content, String sessionId, Long userId) {
        logger.info("开始生成思维导图 - 用户ID: {}, 会话ID: {}", userId, sessionId);

        // 创建思维导图响应实体
        MindMapResponse response = new MindMapResponse();
        response.setSessionId(sessionId);
        response.setUserId(userId);
        response.setRequestText(content);
        response.setStatus(MindMapResponse.ResponseStatus.PENDING);

        // 保存初始状态
        response = mindMapResponseRepository.save(response);

        // 异步生成思维导图
        final Long responseId = response.getId();
        CompletableFuture.runAsync(() -> generateMindMapAsync(responseId, content, sessionId, userId));

        return response;
    }

    @Override
    public MindMapResponse generateMindMapFromConversation(String sessionId, Long userId) {
        logger.info("从对话历史生成思维导图 - 用户ID: {}, 会话ID: {}", userId, sessionId);

        // 获取对话历史
        List<ChatMessage> messages = chatMessageRepository.findByUserIdAndSessionIdOrderByCreatedAtAsc(userId, sessionId);
        if (messages.isEmpty()) {
            throw new RuntimeException("指定会话中未找到聊天记录");
        }

        // 构建对话内容
        StringBuilder conversationBuilder = new StringBuilder();
        conversationBuilder.append("以下是用户的对话历史，请基于这些内容生成思维导图：\n\n");

        for (ChatMessage message : messages) {
            if ("user".equals(message.getRole())) {
                conversationBuilder.append("用户: ").append(message.getContent()).append("\n");
            } else if ("assistant".equals(message.getRole())) {
                conversationBuilder.append("AI助手: ").append(message.getContent()).append("\n");
            }
        }

        String conversationContent = conversationBuilder.toString();
        return generateMindMap(conversationContent, sessionId, userId);
    }

    @Override
    public MindMapNode generateStructuredMindMap(String content, String topic) {
        logger.info("生成结构化思维导图 - 主题: {}", topic);

        try {
            // 创建根节点
            MindMapNode rootNode = new MindMapNode(
                    UUID.randomUUID().toString(),
                    topic != null ? topic : "主题",
                    MindMapNode.NodeType.ROOT
            );
            rootNode.setLevel(0);

            // 解析内容并构建思维导图结构
            List<String> keyPoints = extractKeyPoints(content);

            int childIndex = 0;
            for (String point : keyPoints) {
                MindMapNode childNode = new MindMapNode(
                        UUID.randomUUID().toString(),
                        point,
                        MindMapNode.NodeType.TOPIC
                );
                childNode.setLevel(1);

                // 设置节点样式
                MindMapNode.NodeStyle style = new MindMapNode.NodeStyle();
                style.setBackgroundColor(getNodeColor(childIndex));
                style.setBorderColor("#CCCCCC");
                style.setFontSize("14px");
                childNode.setStyle(style);

                // 尝试提取子要点
                List<String> subPoints = extractSubPoints(point);
                for (String subPoint : subPoints) {
                    MindMapNode subNode = new MindMapNode(
                            UUID.randomUUID().toString(),
                            subPoint,
                            MindMapNode.NodeType.SUBTOPIC
                    );
                    subNode.setLevel(2);

                    MindMapNode.NodeStyle subStyle = new MindMapNode.NodeStyle();
                    subStyle.setBackgroundColor("#F8F9FA");
                    subStyle.setBorderColor("#DEE2E6");
                    subStyle.setFontSize("12px");
                    subNode.setStyle(subStyle);

                    childNode.addChild(subNode);
                }

                rootNode.addChild(childNode);
                childIndex++;
            }

            return rootNode;

        } catch (Exception e) {
            logger.error("生成结构化思维导图失败", e);
            throw new RuntimeException("生成思维导图失败: " + e.getMessage(), e);
        }
    }

    @Override
    public String exportMindMap(Long mindMapId, String format) {
        logger.info("导出思维导图 - ID: {}, 格式: {}", mindMapId, format);

        MindMapResponse mindMapResponse = mindMapResponseRepository.findById(mindMapId)
                .orElseThrow(() -> new RuntimeException("思维导图不存在: " + mindMapId));

        MindMapNode rootNode = mindMapResponse.getRootNode();
        if (rootNode == null) {
            throw new RuntimeException("思维导图数据为空");
        }

        switch (format.toLowerCase()) {
            case "json":
                return exportToJson(rootNode);
            case "text":
                return exportToText(rootNode);
            case "markdown":
                return exportToMarkdown(rootNode);
            case "xml":
                return exportToXml(rootNode);
            default:
                throw new RuntimeException("不支持的导出格式: " + format);
        }
    }

    @Override
    public List<MindMapResponse> getUserMindMaps(Long userId, int page, int size) {
        logger.info("获取用户思维导图列表 - 用户ID: {}, 页码: {}, 大小: {}", userId, page, size);

        Pageable pageable = PageRequest.of(page, size);
        return mindMapResponseRepository.findByUserIdOrderByCreatedAtDesc(userId, pageable).getContent();
    }

    @Override
    public void deleteMindMap(Long mindMapId, Long userId) {
        logger.info("删除思维导图 - ID: {}, 用户ID: {}", mindMapId, userId);

        MindMapResponse mindMap = mindMapResponseRepository.findById(mindMapId)
                .orElseThrow(() -> new RuntimeException("思维导图不存在: " + mindMapId));

        if (!mindMap.getUserId().equals(userId)) {
            throw new RuntimeException("无权限删除此思维导图");
        }

        mindMapResponseRepository.delete(mindMap);
        logger.info("思维导图删除成功 - ID: {}", mindMapId);
    }

    @Override
    public MindMapResponse updateMindMap(Long mindMapId, MindMapNode updatedNode, Long userId) {
        logger.info("更新思维导图 - ID: {}, 用户ID: {}", mindMapId, userId);

        MindMapResponse mindMap = mindMapResponseRepository.findById(mindMapId)
                .orElseThrow(() -> new RuntimeException("思维导图不存在: " + mindMapId));

        if (!mindMap.getUserId().equals(userId)) {
            throw new RuntimeException("无权限修改此思维导图");
        }

        // 更新根节点
        mindMap.setRootNode(updatedNode);
        mindMap.updateTimestamp();

        return mindMapResponseRepository.save(mindMap);
    }

    /**
     * 异步生成思维导图
     */
    private void generateMindMapAsync(Long responseId, String content, String sessionId, Long userId) {
        try {
            logger.info("异步生成思维导图开始 - 响应ID: {}", responseId);

            // 获取响应实体
            MindMapResponse response = mindMapResponseRepository.findById(responseId)
                    .orElseThrow(() -> new RuntimeException("思维导图响应不存在: " + responseId));

            long startTime = System.currentTimeMillis();

            // 获取AI配置
            AiConfig aiConfig = getAvailableAiConfig();
            if (aiConfig == null) {
                throw new RuntimeException("没有可用的AI配置");
            }

            // 构建思维导图生成提示
            String prompt = buildMindMapPrompt(content);

            // 调用AI生成思维导图
            AiClient aiClient = aiClientFactory.getClient(aiConfig.getProvider());
            String aiResponse = aiClient.chat(prompt, aiConfig);

            // 解析AI响应并构建思维导图
            MindMapNode rootNode = parseAiResponseToMindMap(aiResponse, content);

            // 设置响应信息
            response.setRootNode(rootNode);
            response.setTitle(extractTitle(content));
            response.setDescription(extractDescription(content));
            response.setMindmapType(determineMindMapType(content));
            response.setTopicDomain(extractTopicDomain(content));
            response.setAiProvider(aiConfig.getProvider().name());
            response.setModelName(aiConfig.getModelName());
            response.setProcessingTime(System.currentTimeMillis() - startTime);
            response.setStatus(MindMapResponse.ResponseStatus.SUCCESS);

            mindMapResponseRepository.save(response);
            logger.info("思维导图生成成功 - 响应ID: {}", responseId);

        } catch (Exception e) {
            logger.error("异步生成思维导图失败 - 响应ID: " + responseId, e);

            // 更新失败状态
            try {
                MindMapResponse response = mindMapResponseRepository.findById(responseId).orElse(null);
                if (response != null) {
                    response.setStatus(MindMapResponse.ResponseStatus.FAILED);
                    response.setErrorMessage(e.getMessage());
                    mindMapResponseRepository.save(response);
                }
            } catch (Exception updateException) {
                logger.error("更新失败状态时出错", updateException);
            }
        }
    }

    /**
     * 获取可用的AI配置
     */
    private AiConfig getAvailableAiConfig() {
        List<AiConfig> configs = aiConfigRepository.findByIsActiveOrderByPriorityAsc(true);
        return configs.isEmpty() ? null : configs.get(0);
    }

    /**
     * 构建思维导图生成提示
     */
    private String buildMindMapPrompt(String content) {
        return "请基于以下内容生成一个结构化的思维导图。要求：\n\n" +
                "1. 提取主要主题和关键概念\n" +
                "2. 建立逻辑层次结构\n" +
                "3. 使用JSON格式输出思维导图结构\n" +
                "4. 每个节点包含id、text、type、children字段\n" +
                "5. 节点类型包括：ROOT（根节点）、TOPIC（主题节点）、SUBTOPIC（子主题节点）、CONCEPT（概念节点）、DETAIL（详细节点）\n\n" +
                "内容：\n" + content + "\n\n" +
                "请以JSON格式输出思维导图结构：";
    }

    /**
     * 解析AI响应为思维导图
     */
    private MindMapNode parseAiResponseToMindMap(String aiResponse, String originalContent) {
        try {
            // 尝试从AI响应中提取JSON
            String jsonStr = extractJsonFromResponse(aiResponse);

            if (jsonStr != null) {
                // 尝试解析JSON
                try {
                    MindMapNode node = objectMapper.readValue(jsonStr, MindMapNode.class);
                    if (node != null && node.getText() != null) {
                        assignNodeIds(node);
                        return node;
                    }
                } catch (Exception e) {
                    logger.warn("解析AI JSON响应失败，使用备用方案", e);
                }
            }

            // 备用方案：基于原始内容生成结构化思维导图
            return generateStructuredMindMap(originalContent, extractTitle(originalContent));

        } catch (Exception e) {
            logger.error("解析AI响应失败", e);
            throw new RuntimeException("解析思维导图数据失败: " + e.getMessage(), e);
        }
    }

    /**
     * 从响应中提取JSON
     */
    private String extractJsonFromResponse(String response) {
        // 查找JSON块
        Pattern jsonPattern = Pattern.compile("\\{[\\s\\S]*\\}", Pattern.MULTILINE);
        Matcher matcher = jsonPattern.matcher(response);

        if (matcher.find()) {
            return matcher.group();
        }

        return null;
    }

    /**
     * 为节点分配ID
     */
    private void assignNodeIds(MindMapNode node) {
        if (node.getId() == null) {
            node.setId(UUID.randomUUID().toString());
        }

        if (node.getChildren() != null) {
            for (MindMapNode child : node.getChildren()) {
                child.setParentId(node.getId());
                assignNodeIds(child);
            }
        }
    }

    /**
     * 提取关键要点
     */
    private List<String> extractKeyPoints(String content) {
        List<String> points = new ArrayList<>();

        // 按句子分割
        String[] sentences = content.split("[.。!！?？;；]");

        for (String sentence : sentences) {
            sentence = sentence.trim();
            if (sentence.length() > 10 && sentence.length() < 200) {
                points.add(sentence);
            }
        }

        // 限制要点数量
        return points.size() > 8 ? points.subList(0, 8) : points;
    }

    /**
     * 提取子要点
     */
    private List<String> extractSubPoints(String point) {
        List<String> subPoints = new ArrayList<>();

        // 简单的子要点提取逻辑
        String[] parts = point.split("[,，、]");
        for (String part : parts) {
            part = part.trim();
            if (part.length() > 5 && part.length() < 100 && !part.equals(point)) {
                subPoints.add(part);
            }
        }

        return subPoints.size() > 3 ? subPoints.subList(0, 3) : subPoints;
    }

    /**
     * 获取节点颜色
     */
    private String getNodeColor(int index) {
        String[] colors = {
                "#E3F2FD", "#E8F5E8", "#FFF3E0", "#F3E5F5",
                "#E0F2F1", "#FFF8E1", "#FCE4EC", "#F1F8E9"
        };
        return colors[index % colors.length];
    }

    /**
     * 提取标题
     */
    private String extractTitle(String content) {
        if (content == null || content.trim().isEmpty()) {
            return "思维导图";
        }

        String[] lines = content.split("\n");
        String firstLine = lines[0].trim();

        return firstLine.length() > 50 ? firstLine.substring(0, 50) + "..." : firstLine;
    }

    /**
     * 提取描述
     */
    private String extractDescription(String content) {
        if (content == null || content.trim().isEmpty()) {
            return "基于内容生成的思维导图";
        }

        return content.length() > 200 ? content.substring(0, 200) + "..." : content;
    }

    /**
     * 确定思维导图类型
     */
    private MindMapResponse.MindMapType determineMindMapType(String content) {
        String lowerContent = content.toLowerCase();

        if (lowerContent.contains("流程") || lowerContent.contains("步骤") || lowerContent.contains("过程")) {
            return MindMapResponse.MindMapType.FLOWCHART;
        } else if (lowerContent.contains("对比") || lowerContent.contains("比较") || lowerContent.contains("vs")) {
            return MindMapResponse.MindMapType.COMPARISON;
        } else if (lowerContent.contains("论证") || lowerContent.contains("论点") || lowerContent.contains("辩论")) {
            return MindMapResponse.MindMapType.ARGUMENT;
        } else if (lowerContent.contains("知识") || lowerContent.contains("学习")) {
            return MindMapResponse.MindMapType.KNOWLEDGE;
        } else if (lowerContent.contains("时间") || lowerContent.contains("历史") || lowerContent.contains("发展")) {
            return MindMapResponse.MindMapType.TIMELINE;
        } else {
            return MindMapResponse.MindMapType.CONCEPT;
        }
    }

    /**
     * 提取主题领域
     */
    private String extractTopicDomain(String content) {
        String lowerContent = content.toLowerCase();

        if (lowerContent.contains("科技") || lowerContent.contains("技术") || lowerContent.contains("ai") || lowerContent.contains("人工智能")) {
            return "科技";
        } else if (lowerContent.contains("教育") || lowerContent.contains("学习") || lowerContent.contains("知识")) {
            return "教育";
        } else if (lowerContent.contains("经济") || lowerContent.contains("商业") || lowerContent.contains("管理")) {
            return "商业";
        } else if (lowerContent.contains("政治") || lowerContent.contains("社会") || lowerContent.contains("法律")) {
            return "社会";
        } else if (lowerContent.contains("健康") || lowerContent.contains("医疗") || lowerContent.contains("生物")) {
            return "健康";
        } else if (lowerContent.contains("环境") || lowerContent.contains("气候") || lowerContent.contains("生态")) {
            return "环境";
        } else {
            return "通用";
        }
    }

    /**
     * 导出为JSON格式
     */
    private String exportToJson(MindMapNode rootNode) {
        try {
            return objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(rootNode);
        } catch (Exception e) {
            throw new RuntimeException("导出JSON格式失败", e);
        }
    }

    /**
     * 导出为文本格式
     */
    private String exportToText(MindMapNode rootNode) {
        StringBuilder sb = new StringBuilder();
        exportNodeToText(rootNode, sb, 0);
        return sb.toString();
    }

    private void exportNodeToText(MindMapNode node, StringBuilder sb, int level) {
        // 添加缩进
        for (int i = 0; i < level; i++) {
            sb.append("  ");
        }

        // 添加节点文本
        sb.append("- ").append(node.getText()).append("\n");

        // 递归处理子节点
        if (node.getChildren() != null) {
            for (MindMapNode child : node.getChildren()) {
                exportNodeToText(child, sb, level + 1);
            }
        }
    }

    /**
     * 导出为Markdown格式
     */
    private String exportToMarkdown(MindMapNode rootNode) {
        StringBuilder sb = new StringBuilder();
        sb.append("# ").append(rootNode.getText()).append("\n\n");
        exportNodeToMarkdown(rootNode, sb, 1);
        return sb.toString();
    }

    private void exportNodeToMarkdown(MindMapNode node, StringBuilder sb, int level) {
        if (node.getChildren() != null) {
            for (MindMapNode child : node.getChildren()) {
                // 添加Markdown标题
                for (int i = 0; i < level; i++) {
                    sb.append("#");
                }
                sb.append(" ").append(child.getText()).append("\n\n");

                // 递归处理子节点
                exportNodeToMarkdown(child, sb, level + 1);
            }
        }
    }

    /**
     * 导出为XML格式
     */
    private String exportToXml(MindMapNode rootNode) {
        StringBuilder sb = new StringBuilder();
        sb.append("<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n");
        sb.append("<mindmap>\n");
        exportNodeToXml(rootNode, sb, 1);
        sb.append("</mindmap>");
        return sb.toString();
    }

    private void exportNodeToXml(MindMapNode node, StringBuilder sb, int level) {
        String indent = "  ".repeat(level);

        sb.append(indent).append("<node");
        sb.append(" id=\"").append(node.getId()).append("\"");
        sb.append(" type=\"").append(node.getType()).append("\"");
        sb.append(">\n");

        sb.append(indent).append("  <text>").append(escapeXml(node.getText())).append("</text>\n");

        if (node.getChildren() != null && !node.getChildren().isEmpty()) {
            sb.append(indent).append("  <children>\n");
            for (MindMapNode child : node.getChildren()) {
                exportNodeToXml(child, sb, level + 2);
            }
            sb.append(indent).append("  </children>\n");
        }

        sb.append(indent).append("</node>\n");
    }

    private String escapeXml(String text) {
        if (text == null) return "";
        return text.replace("&", "&amp;")
                   .replace("<", "&lt;")
                   .replace(">", "&gt;")
                   .replace("\"", "&quot;")
                   .replace("'", "&apos;");
    }
}
