<template>
  <div class="settings-page">
    <div class="container">
      <h1 class="settings-title">设置</h1>
      <el-tabs v-model="activeTab">
        <el-tab-pane label="主题" name="theme">
          <div class="settings-section">
            <h2>主题切换</h2>
            <el-radio-group v-model="theme" @change="changeTheme">
              <el-radio label="light">浅色模式</el-radio>
              <el-radio label="dark">深色模式</el-radio>
              <el-radio label="auto">跟随系统</el-radio>
            </el-radio-group>
          </div>
        </el-tab-pane>
        <el-tab-pane label="语言" name="language">
          <div class="settings-section">
            <h2>语言选择</h2>
            <el-select v-model="language" placeholder="请选择语言" @change="changeLanguage">
              <el-option label="简体中文" value="zh-CN" />
              <el-option label="English" value="en-US" />
            </el-select>
          </div>
        </el-tab-pane>
        <el-tab-pane label="通知" name="notification">
          <div class="settings-section">
            <h2>通知偏好</h2>
            <el-switch v-model="notifyDebate" /> 接收辩论动态通知
            <br />
            <el-switch v-model="notifySystem" /> 接收系统消息
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script src="./js/Settings.js"></script>

<style lang="scss" src="./scss/Settings.scss"></style>
