@use '@/styles/variables' as v;

.not-found-page {
    min-height: calc(100vh - 150px);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: v.$spacing-xl 0;
}

.not-found-content {
    text-align: center;
    max-width: 600px;
    margin: 0 auto;
}

.error-code {
    font-size: 8rem;
    font-weight: 700;
    color: v.$color-primary;
    margin: 0;
    line-height: 1;
}

.error-title {
    font-size: 2rem;
    margin-bottom: v.$spacing-md;
}

.error-message {
    font-size: 1.125rem;
    color: rgba(0, 0, 0, 0.6);
    margin-bottom: v.$spacing-xl;
}

.action-buttons {
    display: flex;
    justify-content: center;
    gap: v.$spacing-md;
}

@media (max-width: 768px) {
    .error-code {
        font-size: 6rem;
    }

    .action-buttons {
        flex-direction: column;
    }
}
