import { ref } from 'vue';
import { useRouter } from 'vue-router';

export default {
  name: 'HelpCenter',
  setup() {
    const router = useRouter();

    // 帮助中心分类
    const helpCategories = ref([
      {
        id: 1,
        title: '账号相关',
        icon: 'user',
        topics: [
          { id: 101, title: '如何注册账号', content: '通过首页的"注册"按钮，填写相关信息即可完成注册。' },
          { id: 102, title: '如何修改密码', content: '登录后在个人中心可以找到密码修改选项。' },
          { id: 103, title: '账号安全设置', content: '我们提供多种账号安全选项，包括两步验证等。' }
        ]
      },
      {
        id: 2,
        title: '辩论参与',
        icon: 'comment',
        topics: [
          { id: 201, title: '如何发起辩论', content: '在辩论大厅点击"发起辩论"按钮，设置辩题和参数。' },
          { id: 202, title: '如何参与辩论', content: '在辩论大厅浏览可参与的辩论，点击"参与"按钮。' },
          { id: 203, title: '辩论规则说明', content: '每场辩论有特定时间限制和轮次安排，参与前请仔细阅读。' }
        ]
      },
      {
        id: 3,
        title: '技术支持',
        icon: 'settings',
        topics: [
          { id: 301, title: '浏览器兼容性', content: '推荐使用Chrome、Firefox、Edge等现代浏览器。' },
          { id: 302, title: '网络连接问题', content: '辩论过程需要稳定的网络连接，建议使用有线网络或稳定的WiFi。' },
          { id: 303, title: '页面加载缓慢', content: '可能是网络问题或浏览器缓存导致，请尝试清除缓存。' }
        ]
      },
      {
        id: 4,
        title: '内容与社区',
        icon: 'file-text',
        topics: [
          { id: 401, title: '内容审核标准', content: '我们对辩论内容有严格的审核标准，禁止违法、色情、暴力等内容。' },
          { id: 402, title: '如何举报内容', content: '在任何辩论页面，都可以点击"举报"按钮进行内容举报。' },
          { id: 403, title: '社区行为准则', content: '请保持尊重，文明辩论，详情请参考我们的社区准则。' }
        ]
      }
    ]);

    // 激活的分类和主题
    const activeCategory = ref(helpCategories.value[0]);
    const activeTopic = ref(helpCategories.value[0].topics[0]);

    // 选择分类
    const selectCategory = (category) => {
      activeCategory.value = category;
      activeTopic.value = category.topics[0];
    };

    // 选择主题
    const selectTopic = (topic) => {
      activeTopic.value = topic;
    };

    // 导航到联系我们页面
    const goToContact = () => {
      router.push('/contact');
    };

    return {
      helpCategories,
      activeCategory,
      activeTopic,
      selectCategory,
      selectTopic,
      goToContact
    };
  }
};
