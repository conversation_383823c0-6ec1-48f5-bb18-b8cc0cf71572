// 基础样式定义
@use './variables' as v;
@use './mixins' as m;

// 重置和标准化样式
html,
body {
  margin: 0;
  padding: 0;
  font-family: 'Open Sans', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
  font-size: 16px;
  line-height: 1.5;
  color: v.$color-on-surface;
  background-color: #f9fafb;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

*,
*::before,
*::after {
  box-sizing: border-box;
}

// 容器样式
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 v.$spacing-md;

  @include m.respond-to(md) {
    padding: 0 v.$spacing-lg;
  }
}

// 标题样式
h1,
h2,
h3,
h4,
h5,
h6 {
  margin-top: 0;
  margin-bottom: v.$spacing-md;
  font-weight: 700;
  line-height: 1.2;
}

h1 {
  font-size: v.$font-size-h1;
}

h2 {
  font-size: v.$font-size-h2;
}

// 链接样式
a {
  color: v.$color-primary;
  text-decoration: none;
  transition: color 0.2s ease-in-out;

  &:hover {
    color: darken(v.$color-primary, 10%);
  }
}

// 按钮样式
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: v.$spacing-sm v.$spacing-md;
  border: none;
  border-radius: v.$shape-corner-small;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  text-align: center;

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  &--primary {
    background-color: v.$color-primary;
    color: white;

    &:hover:not(:disabled) {
      background-color: darken(v.$color-primary, 5%);
    }
  }

  &--secondary {
    background-color: v.$color-secondary;
    color: white;

    &:hover:not(:disabled) {
      background-color: darken(v.$color-secondary, 5%);
    }
  }

  &--outline {
    background-color: transparent;
    border: 1px solid v.$color-primary;
    color: v.$color-primary;

    &:hover:not(:disabled) {
      background-color: rgba(v.$color-primary, 0.05);
    }
  }

  &--lg {
    padding: v.$spacing-md v.$spacing-lg;
    font-size: 1.1rem;
  }

  &--sm {
    padding: v.$spacing-xs v.$spacing-sm;
    font-size: 0.9rem;
  }

  &--icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    padding: 0;

    svg {
      width: 20px;
      height: 20px;
    }
  }
}

// 表单样式
.form-group {
  margin-bottom: v.$spacing-md;
}

.form-label {
  display: block;
  margin-bottom: v.$spacing-xs;
  font-weight: 500;
}

.form-control {
  display: block;
  width: 100%;
  padding: v.$spacing-sm v.$spacing-md;
  font-size: 1rem;
  line-height: 1.5;
  color: v.$color-on-surface;
  background-color: #fff;
  border: 1px solid #ced4da;
  border-radius: v.$shape-corner-small;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;

  &:focus {
    border-color: v.$color-primary;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(v.$color-primary, 0.25);
  }

  &:disabled {
    background-color: #e9ecef;
    opacity: 1;
  }
}

// 加载指示器
.spinner {
  display: inline-block;
  width: 40px;
  height: 40px;
  border: 4px solid rgba(v.$color-primary, 0.3);
  border-radius: 50%;
  border-top-color: v.$color-primary;
  animation: spin 1s linear infinite;
}

.spinner-small {
  width: 20px;
  height: 20px;
  border-width: 2px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

// 辅助类
.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.d-flex {
  display: flex;
}

.align-items-center {
  align-items: center;
}

.justify-content-center {
  justify-content: center;
}

.justify-content-between {
  justify-content: space-between;
}

.flex-column {
  flex-direction: column;
}

.mt-1 {
  margin-top: v.$spacing-xs;
}

.mt-2 {
  margin-top: v.$spacing-sm;
}

.mt-3 {
  margin-top: v.$spacing-md;
}

.mt-4 {
  margin-top: v.$spacing-lg;
}

.mt-5 {
  margin-top: v.$spacing-xl;
}

.mb-1 {
  margin-bottom: v.$spacing-xs;
}

.mb-2 {
  margin-bottom: v.$spacing-sm;
}

.mb-3 {
  margin-bottom: v.$spacing-md;
}

.mb-4 {
  margin-bottom: v.$spacing-lg;
}

.mb-5 {
  margin-bottom: v.$spacing-xl;
}

// 卡片样式
.card {
  background-color: white;
  border-radius: v.$shape-corner-medium;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  transition: transform 0.2s ease, box-shadow 0.2s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  &__header {
    padding: v.$spacing-md;
    border-bottom: 1px solid #eee;
  }

  &__body {
    padding: v.$spacing-md;
  }

  &__footer {
    padding: v.$spacing-md;
    border-top: 1px solid #eee;
    background-color: #f9f9f9;
  }
}

// 状态标签
.status-badge {
  display: inline-block;
  padding: v.$spacing-xs v.$spacing-sm;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 500;

  &--success {
    background-color: rgba(v.$color-success, 0.1);
    color: v.$color-success;
  }

  &--warning {
    background-color: rgba(v.$color-warning, 0.1);
    color: v.$color-warning;
  }

  &--error {
    background-color: rgba(v.$color-error, 0.1);
    color: v.$color-error;
  }

  &--neutral {
    background-color: rgba(v.$debate-neutral-color, 0.1);
    color: v.$debate-neutral-color;
  }
}

// 辩论特定样式
.debate-side {
  &--supporter {
    color: v.$debate-supporter-color;
  }

  &--opposer {
    color: v.$debate-opposer-color;
  }

  &--judge {
    color: v.$debate-neutral-color;
  }
}

// 模态框
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal {
  background-color: white;
  border-radius: v.$shape-corner-medium;
  max-width: 90%;
  width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);

  &__header {
    padding: v.$spacing-md;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #eee;
  }

  &__title {
    margin: 0;
    font-size: 1.25rem;
  }

  &__close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #999;

    &:hover {
      color: v.$color-error;
    }
  }

  &__body {
    padding: v.$spacing-md;
  }

  &__footer {
    padding: v.$spacing-md;
    border-top: 1px solid #eee;
    display: flex;
    justify-content: flex-end;
    gap: v.$spacing-sm;
  }
}
