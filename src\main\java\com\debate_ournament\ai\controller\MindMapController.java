package com.debate_ournament.ai.controller;

import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import com.debate_ournament.ai.entity.MindMapResponse;
import com.debate_ournament.ai.service.MindMapService;
import com.debate_ournament.dto.common.ApiResponse;
import com.debate_ournament.security.CustomUserDetailsService.CustomUserPrincipal;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;

/**
 * 思维导图控制器
 * 提供AI驱动的思维导图生成、管理和导出功能
 */
@RestController
@RequestMapping("/mindmap")
@Tag(name = "思维导图管理", description = "AI驱动的思维导图生成和管理接口")
public class MindMapController {

    private static final Logger log = LoggerFactory.getLogger(MindMapController.class);

    @Autowired
    private MindMapService mindMapService;

    /**
     * 从文本内容生成思维导图
     */
    @PostMapping("/generate")
    @Operation(summary = "生成思维导图", description = "基于文本内容使用AI生成思维导图")
    public ResponseEntity<ApiResponse<MindMapResponse>> generateMindMap(
            @RequestBody Map<String, Object> request,
            Authentication authentication) {

        log.info("用户 {} 请求生成思维导图", getUserId(authentication));

        try {
            String content = (String) request.get("content");
            String title = (String) request.get("title");
            String topicDomain = (String) request.get("topicDomain");
            Integer maxDepth = (Integer) request.get("maxDepth");
            Integer maxNodes = (Integer) request.get("maxNodes");
            Boolean isPublic = (Boolean) request.get("isPublic");
            @SuppressWarnings("unchecked")
            List<String> tags = (List<String>) request.get("tags");

            // 设置默认值
            if (maxDepth == null) maxDepth = 5;
            if (maxNodes == null) maxNodes = 50;
            if (isPublic == null) isPublic = false;

            Long userId = getUserId(authentication);

            MindMapResponse mindMap = mindMapService.generateMindMapFromContent(
                userId, content, title, topicDomain, maxDepth, maxNodes, isPublic, tags
            );

            return ResponseEntity.ok(ApiResponse.success("思维导图生成成功", mindMap));

        } catch (Exception e) {
            log.error("生成思维导图失败", e);
            return ResponseEntity.badRequest()
                .body(ApiResponse.error("生成思维导图失败: " + e.getMessage()));
        }
    }

    /**
     * 从会话历史生成思维导图
     */
    @PostMapping("/generate/session/{sessionId}")
    @Operation(summary = "从会话生成思维导图", description = "基于聊天会话历史生成思维导图")
    public ResponseEntity<ApiResponse<MindMapResponse>> generateMindMapFromSession(
            @PathVariable String sessionId,
            @RequestBody Map<String, Object> request,
            Authentication authentication) {

        log.info("用户 {} 请求从会话 {} 生成思维导图", getUserId(authentication), sessionId);

        try {
            String title = (String) request.get("title");
            String topicDomain = (String) request.get("topicDomain");
            Integer maxDepth = (Integer) request.get("maxDepth");
            Integer maxNodes = (Integer) request.get("maxNodes");
            Boolean isPublic = (Boolean) request.get("isPublic");
            @SuppressWarnings("unchecked")
            List<String> tags = (List<String>) request.get("tags");

            // 设置默认值
            if (maxDepth == null) maxDepth = 5;
            if (maxNodes == null) maxNodes = 50;
            if (isPublic == null) isPublic = false;

            Long userId = getUserId(authentication);

            MindMapResponse mindMap = mindMapService.generateMindMapFromSession(
                userId, sessionId, title, topicDomain, maxDepth, maxNodes, isPublic, tags
            );

            return ResponseEntity.ok(ApiResponse.success("思维导图生成成功", mindMap));

        } catch (Exception e) {
            log.error("从会话生成思维导图失败", e);
            return ResponseEntity.badRequest()
                .body(ApiResponse.error("生成思维导图失败: " + e.getMessage()));
        }
    }

    /**
     * 异步生成思维导图
     */
    @PostMapping("/generate/async")
    @Operation(summary = "异步生成思维导图", description = "异步方式生成思维导图，适用于大量内容")
    public ResponseEntity<ApiResponse<String>> generateMindMapAsync(
            @RequestBody Map<String, Object> request,
            Authentication authentication) {

        log.info("用户 {} 请求异步生成思维导图", getUserId(authentication));

        try {
            String content = (String) request.get("content");
            String title = (String) request.get("title");
            String topicDomain = (String) request.get("topicDomain");
            Integer maxDepth = (Integer) request.get("maxDepth");
            Integer maxNodes = (Integer) request.get("maxNodes");
            Boolean isPublic = (Boolean) request.get("isPublic");
            @SuppressWarnings("unchecked")
            List<String> tags = (List<String>) request.get("tags");

            // 设置默认值
            if (maxDepth == null) maxDepth = 5;
            if (maxNodes == null) maxNodes = 50;
            if (isPublic == null) isPublic = false;

            Long userId = getUserId(authentication);

            // 启动异步任务
            mindMapService.generateMindMapAsync(
                userId, content, title, topicDomain, maxDepth, maxNodes, isPublic, tags
            );

            // 返回任务ID，前端可以通过轮询方式查询进度
            String taskId = "task_" + System.currentTimeMillis() + "_" + userId;
            
            // 这里可以存储future以便后续查询
            // 为了简化，我们直接返回任务ID让前端轮询

            return ResponseEntity.ok(ApiResponse.success("思维导图生成任务已启动", taskId));

        } catch (Exception e) {
            log.error("启动异步生成思维导图任务失败", e);
            return ResponseEntity.badRequest()
                .body(ApiResponse.error("启动生成任务失败: " + e.getMessage()));
        }
    }

    /**
     * 获取用户的思维导图列表
     */
    @GetMapping("/my")
    @Operation(summary = "获取我的思维导图", description = "分页获取当前用户的思维导图列表")
    public ResponseEntity<ApiResponse<Page<MindMapResponse>>> getMyMindMaps(
            @Parameter(description = "页码，从0开始") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") int size,
            @Parameter(description = "排序字段") @RequestParam(defaultValue = "createdAt") String sort,
            @Parameter(description = "排序方向") @RequestParam(defaultValue = "desc") String direction,
            Authentication authentication) {

        try {
            Long userId = getUserId(authentication);
            Sort.Direction sortDirection = "desc".equalsIgnoreCase(direction) ?
                Sort.Direction.DESC : Sort.Direction.ASC;
            Pageable pageable = PageRequest.of(page, size, Sort.by(sortDirection, sort));

            Page<MindMapResponse> mindMaps = mindMapService.getUserMindMaps(userId, pageable);

            return ResponseEntity.ok(ApiResponse.success("获取思维导图列表成功", mindMaps));

        } catch (Exception e) {
            log.error("获取用户思维导图列表失败", e);
            return ResponseEntity.badRequest()
                .body(ApiResponse.error("获取思维导图列表失败: " + e.getMessage()));
        }
    }

    /**
     * 获取公开的思维导图列表
     */
    @GetMapping("/public")
    @Operation(summary = "获取公开思维导图", description = "分页获取公开的思维导图列表")
    public ResponseEntity<ApiResponse<Page<MindMapResponse>>> getPublicMindMaps(
            @Parameter(description = "页码，从0开始") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") int size,
            @Parameter(description = "排序字段") @RequestParam(defaultValue = "createdAt") String sort,
            @Parameter(description = "排序方向") @RequestParam(defaultValue = "desc") String direction,
            @Parameter(description = "话题领域") @RequestParam(required = false) String topicDomain,
            @Parameter(description = "标签") @RequestParam(required = false) List<String> tags) {

        try {
            Sort.Direction sortDirection = "desc".equalsIgnoreCase(direction) ?
                Sort.Direction.DESC : Sort.Direction.ASC;
            Pageable pageable = PageRequest.of(page, size, Sort.by(sortDirection, sort));

            Page<MindMapResponse> mindMaps = mindMapService.getPublicMindMaps(
                pageable, topicDomain, tags
            );

            return ResponseEntity.ok(ApiResponse.success("获取公开思维导图列表成功", mindMaps));

        } catch (Exception e) {
            log.error("获取公开思维导图列表失败", e);
            return ResponseEntity.badRequest()
                .body(ApiResponse.error("获取思维导图列表失败: " + e.getMessage()));
        }
    }

    /**
     * 搜索思维导图
     */
    @GetMapping("/search")
    @Operation(summary = "搜索思维导图", description = "根据关键词搜索思维导图")
    public ResponseEntity<ApiResponse<Page<MindMapResponse>>> searchMindMaps(
            @Parameter(description = "搜索关键词") @RequestParam String keyword,
            @Parameter(description = "页码，从0开始") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") int size,
            @Parameter(description = "话题领域") @RequestParam(required = false) String topicDomain,
            @Parameter(description = "标签") @RequestParam(required = false) List<String> tags,
            Authentication authentication) {

        try {
            Long userId = getUserId(authentication);
            Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "createdAt"));

            Page<MindMapResponse> mindMaps = mindMapService.searchMindMaps(
                userId, keyword, pageable, topicDomain, tags
            );

            return ResponseEntity.ok(ApiResponse.success("搜索思维导图成功", mindMaps));

        } catch (Exception e) {
            log.error("搜索思维导图失败", e);
            return ResponseEntity.badRequest()
                .body(ApiResponse.error("搜索思维导图失败: " + e.getMessage()));
        }
    }

    /**
     * 获取思维导图详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "获取思维导图详情", description = "根据ID获取思维导图详细信息")
    public ResponseEntity<ApiResponse<MindMapResponse>> getMindMapDetail(
            @PathVariable Long id,
            Authentication authentication) {

        try {
            Long userId = getUserId(authentication);
            MindMapResponse mindMap = mindMapService.getMindMapById(id, userId);

            return ResponseEntity.ok(ApiResponse.success("获取思维导图详情成功", mindMap));

        } catch (Exception e) {
            log.error("获取思维导图详情失败", e);
            return ResponseEntity.badRequest()
                .body(ApiResponse.error("获取思维导图详情失败: " + e.getMessage()));
        }
    }

    /**
     * 更新思维导图
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新思维导图", description = "更新思维导图的元数据信息")
    public ResponseEntity<ApiResponse<MindMapResponse>> updateMindMap(
            @PathVariable Long id,
            @RequestBody Map<String, Object> updates,
            Authentication authentication) {

        try {
            Long userId = getUserId(authentication);

            String title = (String) updates.get("title");
            String topicDomain = (String) updates.get("topicDomain");
            Boolean isPublic = (Boolean) updates.get("isPublic");
            @SuppressWarnings("unchecked")
            List<String> tags = (List<String>) updates.get("tags");

            MindMapResponse mindMap = mindMapService.updateMindMap(
                id, userId, title, topicDomain, isPublic, tags
            );

            return ResponseEntity.ok(ApiResponse.success("思维导图更新成功", mindMap));

        } catch (Exception e) {
            log.error("更新思维导图失败", e);
            return ResponseEntity.badRequest()
                .body(ApiResponse.error("更新思维导图失败: " + e.getMessage()));
        }
    }

    /**
     * 删除思维导图
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除思维导图", description = "删除指定的思维导图")
    public ResponseEntity<ApiResponse<Void>> deleteMindMap(
            @PathVariable Long id,
            Authentication authentication) {

        try {
            Long userId = getUserId(authentication);
            mindMapService.deleteMindMap(id, userId);

            return ResponseEntity.ok(ApiResponse.success("思维导图删除成功", null));

        } catch (Exception e) {
            log.error("删除思维导图失败", e);
            return ResponseEntity.badRequest()
                .body(ApiResponse.error("删除思维导图失败: " + e.getMessage()));
        }
    }

    /**
     * 导出思维导图 - JSON 格式
     */
    @GetMapping("/{id}/export/json")
    @Operation(summary = "导出JSON格式", description = "导出思维导图为JSON格式")
    public ResponseEntity<String> exportMindMapAsJson(
            @PathVariable Long id,
            Authentication authentication) {

        try {
            Long userId = getUserId(authentication);
            String jsonData = mindMapService.exportMindMapAsJson(id, userId);

            return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=mindmap_" + id + ".json")
                .contentType(MediaType.APPLICATION_JSON)
                .body(jsonData);

        } catch (Exception e) {
            log.error("导出JSON格式思维导图失败", e);
            return ResponseEntity.badRequest().body("{\"error\":\"导出失败: " + e.getMessage() + "\"}");
        }
    }

    /**
     * 导出思维导图 - Markdown 格式
     */
    @GetMapping("/{id}/export/markdown")
    @Operation(summary = "导出Markdown格式", description = "导出思维导图为Markdown格式")
    public ResponseEntity<String> exportMindMapAsMarkdown(
            @PathVariable Long id,
            Authentication authentication) {

        try {
            Long userId = getUserId(authentication);
            String markdownData = mindMapService.exportMindMapAsMarkdown(id, userId);

            return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=mindmap_" + id + ".md")
                .contentType(MediaType.TEXT_PLAIN)
                .body(markdownData);

        } catch (Exception e) {
            log.error("导出Markdown格式思维导图失败", e);
            return ResponseEntity.badRequest().body("导出失败: " + e.getMessage());
        }
    }

    /**
     * 导出思维导图 - XML 格式
     */
    @GetMapping("/{id}/export/xml")
    @Operation(summary = "导出XML格式", description = "导出思维导图为XML格式")
    public ResponseEntity<String> exportMindMapAsXml(
            @PathVariable Long id,
            Authentication authentication) {

        try {
            Long userId = getUserId(authentication);
            String xmlData = mindMapService.exportMindMapAsXml(id, userId);

            return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=mindmap_" + id + ".xml")
                .contentType(MediaType.APPLICATION_XML)
                .body(xmlData);

        } catch (Exception e) {
            log.error("导出XML格式思维导图失败", e);
            return ResponseEntity.badRequest().body("<?xml version=\"1.0\"?><error>导出失败: " + e.getMessage() + "</error>");
        }
    }

    /**
     * 导出思维导图 - 纯文本格式
     */
    @GetMapping("/{id}/export/text")
    @Operation(summary = "导出文本格式", description = "导出思维导图为纯文本格式")
    public ResponseEntity<String> exportMindMapAsText(
            @PathVariable Long id,
            Authentication authentication) {

        try {
            Long userId = getUserId(authentication);
            String textData = mindMapService.exportMindMapAsText(id, userId);

            return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=mindmap_" + id + ".txt")
                .contentType(MediaType.TEXT_PLAIN)
                .body(textData);

        } catch (Exception e) {
            log.error("导出文本格式思维导图失败", e);
            return ResponseEntity.badRequest().body("导出失败: " + e.getMessage());
        }
    }

    /**
     * 获取思维导图统计信息
     */
    @GetMapping("/stats")
    @Operation(summary = "获取统计信息", description = "获取用户的思维导图统计信息")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getMindMapStats(
            Authentication authentication) {

        try {
            Long userId = getUserId(authentication);
            Map<String, Object> stats = mindMapService.getUserMindMapStats(userId);

            return ResponseEntity.ok(ApiResponse.success("获取统计信息成功", stats));

        } catch (Exception e) {
            log.error("获取思维导图统计信息失败", e);
            return ResponseEntity.badRequest()
                .body(ApiResponse.error("获取统计信息失败: " + e.getMessage()));
        }
    }

    /**
     * 批量删除思维导图
     */
    @DeleteMapping("/batch")
    @Operation(summary = "批量删除思维导图", description = "批量删除多个思维导图")
    public ResponseEntity<ApiResponse<Void>> batchDeleteMindMaps(
            @RequestBody List<Long> ids,
            Authentication authentication) {

        try {
            Long userId = getUserId(authentication);
            mindMapService.batchDeleteMindMaps(ids, userId);

            return ResponseEntity.ok(ApiResponse.success("批量删除成功", null));

        } catch (Exception e) {
            log.error("批量删除思维导图失败", e);
            return ResponseEntity.badRequest()
                .body(ApiResponse.error("批量删除失败: " + e.getMessage()));
        }
    }

    /**
     * 复制思维导图
     */
    @PostMapping("/{id}/copy")
    @Operation(summary = "复制思维导图", description = "复制现有的思维导图")
    public ResponseEntity<ApiResponse<MindMapResponse>> copyMindMap(
            @PathVariable Long id,
            @RequestBody(required = false) Map<String, Object> options,
            Authentication authentication) {

        try {
            Long userId = getUserId(authentication);
            String newTitle = options != null ? (String) options.get("title") : null;

            MindMapResponse copiedMindMap = mindMapService.copyMindMap(id, userId, newTitle);

            return ResponseEntity.ok(ApiResponse.success("思维导图复制成功", copiedMindMap));

        } catch (Exception e) {
            log.error("复制思维导图失败", e);
            return ResponseEntity.badRequest()
                .body(ApiResponse.error("复制思维导图失败: " + e.getMessage()));
        }
    }

    /**
     * 从认证信息中获取用户ID
     */
    private Long getUserId(Authentication authentication) {
        if (authentication == null) {
            throw new RuntimeException("用户认证信息为空");
        }

        try {
            if (authentication.getPrincipal() instanceof CustomUserPrincipal customUserPrincipal) {
                return customUserPrincipal.getUserId();
            }

            // 增强的回退处理，增加验证
            String username = authentication.getName();
            if (username != null && username.matches("\\d+")) {
                return Long.valueOf(username);
            }

            throw new RuntimeException("无法从认证信息中解析用户ID");

        } catch (NumberFormatException e) {
            throw new RuntimeException("无法解析用户ID: " + authentication.getName(), e);
        } catch (RuntimeException e) {
            throw e;
        } catch (Exception e) {
            throw new RuntimeException("获取用户ID时发生未知错误", e);
        }
    }
}
