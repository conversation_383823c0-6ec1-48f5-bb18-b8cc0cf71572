package com.debate_ournament.ai.entity;

import java.time.LocalDateTime;
import java.util.List;

import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;

/**
 * 多模态响应实体
 * 用于存储AI的多种形式输出
 */
@Entity
@Table(name = "multimodal_responses")
public class MultiModalResponse {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "session_id", nullable = false)
    private String sessionId;

    @Column(name = "user_id", nullable = false)
    private Long userId;

    @Column(name = "chat_message_id")
    private Long chatMessageId;

    @Column(name = "request_text", columnDefinition = "TEXT")
    private String requestText;

    @Enumerated(EnumType.STRING)
    @Column(name = "primary_media_type", nullable = false)
    private MediaType primaryMediaType;

    @Column(name = "text_content", columnDefinition = "TEXT")
    private String textContent;

    @Column(name = "audio_url")
    private String audioUrl;

    @Column(name = "audio_duration")
    private Integer audioDuration; // 音频时长(秒)

    @Column(name = "audio_format")
    private String audioFormat; // 音频格式(mp3, wav等)

    @Column(name = "image_url")
    private String imageUrl;

    @Column(name = "video_url")
    private String videoUrl;

    @Column(name = "document_url")
    private String documentUrl;

    @Column(name = "file_size")
    private Long fileSize; // 文件大小(字节)

    @Column(name = "mime_type")
    private String mimeType;

    @Enumerated(EnumType.STRING)
    @Column(name = "ai_provider")
    private AiProvider aiProvider;

    @Column(name = "model_name")
    private String modelName;

    @Column(name = "processing_time")
    private Long processingTime; // 处理时长(毫秒)

    @Column(name = "cost")
    private Double cost; // 成本

    @Column(name = "status")
    private String status = "completed"; // 状态: processing, completed, failed

    @Column(name = "error_message")
    private String errorMessage;

    @Column(name = "metadata", columnDefinition = "JSON")
    private String metadata; // 额外元数据

    @Column(name = "created_at")
    private LocalDateTime createdAt;

    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    @OneToMany(mappedBy = "multiModalResponse", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<com.debate_ournament.ai.entity.MediaAttachment> attachments;

    // 构造函数
    public MultiModalResponse() {
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }

    // 静态工厂方法
    public static MultiModalResponse createTextResponse(String sessionId, Long userId, String textContent) {
        MultiModalResponse response = new MultiModalResponse();
        response.setSessionId(sessionId);
        response.setUserId(userId);
        response.setPrimaryMediaType(MediaType.TEXT);
        response.setTextContent(textContent);
        return response;
    }

    public static MultiModalResponse createAudioResponse(String sessionId, Long userId, String audioUrl, String textContent) {
        MultiModalResponse response = new MultiModalResponse();
        response.setSessionId(sessionId);
        response.setUserId(userId);
        response.setPrimaryMediaType(MediaType.AUDIO);
        response.setTextContent(textContent);
        response.setAudioUrl(audioUrl);
        return response;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getChatMessageId() {
        return chatMessageId;
    }

    public void setChatMessageId(Long chatMessageId) {
        this.chatMessageId = chatMessageId;
    }

    public String getRequestText() {
        return requestText;
    }

    public void setRequestText(String requestText) {
        this.requestText = requestText;
    }

    public MediaType getPrimaryMediaType() {
        return primaryMediaType;
    }

    public void setPrimaryMediaType(MediaType primaryMediaType) {
        this.primaryMediaType = primaryMediaType;
    }

    public String getTextContent() {
        return textContent;
    }

    public void setTextContent(String textContent) {
        this.textContent = textContent;
    }

    public String getAudioUrl() {
        return audioUrl;
    }

    public void setAudioUrl(String audioUrl) {
        this.audioUrl = audioUrl;
    }

    public Integer getAudioDuration() {
        return audioDuration;
    }

    public void setAudioDuration(Integer audioDuration) {
        this.audioDuration = audioDuration;
    }

    public String getAudioFormat() {
        return audioFormat;
    }

    public void setAudioFormat(String audioFormat) {
        this.audioFormat = audioFormat;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public String getVideoUrl() {
        return videoUrl;
    }

    public void setVideoUrl(String videoUrl) {
        this.videoUrl = videoUrl;
    }

    public String getDocumentUrl() {
        return documentUrl;
    }

    public void setDocumentUrl(String documentUrl) {
        this.documentUrl = documentUrl;
    }

    public Long getFileSize() {
        return fileSize;
    }

    public void setFileSize(Long fileSize) {
        this.fileSize = fileSize;
    }

    public String getMimeType() {
        return mimeType;
    }

    public void setMimeType(String mimeType) {
        this.mimeType = mimeType;
    }

    public AiProvider getAiProvider() {
        return aiProvider;
    }

    public void setAiProvider(AiProvider aiProvider) {
        this.aiProvider = aiProvider;
    }

    public String getModelName() {
        return modelName;
    }

    public void setModelName(String modelName) {
        this.modelName = modelName;
    }

    public Long getProcessingTime() {
        return processingTime;
    }

    public void setProcessingTime(Long processingTime) {
        this.processingTime = processingTime;
    }

    public Double getCost() {
        return cost;
    }

    public void setCost(Double cost) {
        this.cost = cost;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public String getMetadata() {
        return metadata;
    }

    public void setMetadata(String metadata) {
        this.metadata = metadata;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public List<MediaAttachment> getAttachments() {
        return attachments;
    }

    public void setAttachments(List<MediaAttachment> attachments) {
        this.attachments = attachments;
    }
}
