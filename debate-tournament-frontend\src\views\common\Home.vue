<template>
  <div class="home">
    <!-- 顶部导航栏由App.vue提供 -->

    <!-- 英雄区域 -->
    <section class="hero-section">
      <div class="container">
        <div class="hero-content">
          <div class="hero-content__text">
            <h1 class="hero-content__title">AI辩论赛平台</h1>
            <p class="hero-content__subtitle">探索AI辩论的无限可能</p>
            <div class="hero-content__features">
              <div class="feature-item">
                <el-icon><Trophy /></el-icon>
                <span>与AI智能辩手过招</span>
              </div>
              <div class="feature-item">
                <el-icon><SetUp /></el-icon>
                <span>训练专属AI辩论助手</span>
              </div>
              <div class="feature-item">
                <el-icon><DataAnalysis /></el-icon>
                <span>可视化思维导图分析</span>
              </div>
              <div class="feature-item">
                <el-icon><Connection /></el-icon>
                <span>加入全球辩论者社区</span>
              </div>
            </div>
            <div class="hero-content__actions">
              <el-button type="primary" size="large" @click="navigateToDebateHall">开始体验</el-button>
              <el-button size="large" @click="showDemo">观看演示</el-button>
              <el-button size="large" @click="navigateToAIModels">自定义AI</el-button>
            </div>
          </div>
          <div class="hero-content__visual">
          </div>
        </div>
      </div>
    </section>

    <!-- 核心功能展示区 -->
    <section class="features-section">
      <div class="container">
        <h2 class="section-title">核心功能</h2>
        <div class="features-grid">
          <div class="feature-card" v-for="feature in features" :key="feature.title">
            <div class="feature-card__icon">
              <el-icon :is="feature.icon" />
            </div>
            <h3 class="feature-card__title">{{ feature.title }}</h3>
            <ul class="feature-card__list">
              <li v-for="item in feature.items" :key="item">{{ item }}</li>
            </ul>
            <el-button type="primary" @click="feature.action()">{{ feature.button }}</el-button>
          </div>
        </div>
      </div>
    </section>

    <!-- 实时辩论展示区 -->
    <section class="live-debates-section">
      <div class="container">
        <h2 class="section-title">热门辩论实况</h2>
        <div class="live-debates-grid">
          <debate-live-card
            v-for="debate in liveDebates"
            :key="debate.id"
            :debate="debate"
            @watch="watchLiveDebate"
          />
        </div>
        <div class="section-action">
          <el-button type="primary" @click="navigateToDebateHall">查看更多辩论</el-button>
        </div>
      </div>
    </section>

    <!-- AI模型展示区 -->
    <section class="ai-models-section">
      <div class="container">
        <h2 class="section-title">选择你的AI辩论伙伴</h2>
        <div class="models-tabs">
          <el-tabs v-model="activeModelTab">
            <el-tab-pane label="预设模型" name="preset">
              <div class="models-grid">
                <div class="model-card" v-for="model in aiModels.preset" :key="model.id">
                  <h3 class="model-card__name">{{ model.name }}</h3>
                  <div class="model-card__features">
                    <div class="model-feature" v-for="(feature, index) in model.features" :key="index">
                      <el-icon :is="feature.icon"></el-icon>
                      <span>{{ feature.text }}</span>
                    </div>
                  </div>
                  <el-button type="primary" @click="selectAIModel(model)">选择</el-button>
                </div>
              </div>
            </el-tab-pane>
            <el-tab-pane label="自定义模型" name="custom">
              <div class="models-grid">
                <div class="model-card model-card--custom">
                  <h3 class="model-card__name">自定义AI模型</h3>
                  <div class="model-card__features">
                    <div class="model-feature">
                      <el-icon><SetUp /></el-icon>
                      <span>完全自定义参数</span>
                    </div>
                  </div>
                  <el-button type="primary" @click="navigateToCustomAI">配置</el-button>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </section>

    <!-- 思维导图演示区 -->
    <section class="mind-map-section">
      <div class="container">
        <h2 class="section-title">论点关系可视化演示</h2>
        <div class="mind-map-demo">
          <mind-map-visualization :debate-topic="selectedDebateTopic" />
          <div class="mind-map-controls">
            <el-button-group>
              <el-button @click="updateMindMap">实时更新</el-button>
              <el-button @click="exportMindMap('png')">导出PNG</el-button>
              <el-button @click="shareMindMap">分享链接</el-button>
              <el-button @click="enableCollaboration">协作编辑</el-button>
            </el-button-group>
          </div>
        </div>
      </div>
    </section>

    <!-- 社区互动区 -->
    <section class="community-section">
      <div class="container">
        <h2 class="section-title">社区动态广场</h2>
        <div class="community-cards">
          <div class="community-card community-card--user">
            <div class="community-card__header">
              <h3 class="community-card__title">🏆 今日辩论之星</h3>
            </div>
            <div class="community-card__content">
              <div class="user-showcase">
                <div class="user-avatar">
                  <el-avatar :size="64" :src="starUser.avatar"></el-avatar>
                </div>
                <div class="user-info">
                  <h4 class="user-name">{{ starUser.name }}</h4>
                  <p class="user-stats">参与辩论：{{ starUser.debates }}场 | 胜率：{{ starUser.winRate }} | 获赞：{{ starUser.likes }}</p>
                  <p class="user-quote">"{{ starUser.quote }}"</p>
                  <div class="user-actions">
                    <el-button size="small">关注</el-button>
                    <el-button size="small">私信</el-button>
                    <el-button size="small" type="primary">查看详情</el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="community-card community-card--topic">
            <div class="community-card__header">
              <h3 class="community-card__title">🔥 热门话题</h3>
            </div>
            <div class="community-card__content">
              <div class="topic-showcase">
                <h4 class="topic-title">{{ hotTopic.title }}</h4>
                <p class="topic-stats">参与人数：{{ hotTopic.participants }} | 讨论数：{{ hotTopic.comments }}</p>
                <div class="topic-votes">
                  <div class="vote-bar">
                    <div class="vote-bar__support" :style="{width: hotTopic.support + '%'}">{{ hotTopic.support }}%</div>
                    <div class="vote-bar__oppose" :style="{width: hotTopic.oppose + '%'}">{{ hotTopic.oppose }}%</div>
                  </div>
                </div>
                <div class="topic-actions">
                  <el-button type="primary">加入讨论</el-button>
                  <el-button>查看详情</el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 底部信息由AppFooter.vue提供 -->
  </div>
</template>

<script src="./js/Home.js"></script>

<style lang="scss">
@use './scss/Home.scss';
</style>
