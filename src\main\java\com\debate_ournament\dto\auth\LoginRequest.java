package com.debate_ournament.dto.auth;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * 登录请求DTO
 */
public class LoginRequest {

    @NotBlank(message = "用户名不能为空")
    @Size(min = 3, max = 20, message = "用户名长度应在3-20个字符之间")
    private String username;

    @NotBlank(message = "密码不能为空")
    @Size(min = 6, max = 20, message = "密码长度应在6-20个字符之间")
    private String password;

    @NotBlank(message = "验证码不能为空")
    @Size(min = 4, max = 4, message = "验证码长度应为4个字符")
    private String captcha;

    @NotBlank(message = "会话ID不能为空")
    private String sessionId;

    private Boolean remember = false;

    // 默认构造函数
    public LoginRequest() {}

    // 构造函数
    public LoginRequest(String username, String password, String captcha, String sessionId) {
        this.username = username;
        this.password = password;
        this.captcha = captcha;
        this.sessionId = sessionId;
    }

    public LoginRequest(String username, String password, String captcha, String sessionId, Boolean remember) {
        this.username = username;
        this.password = password;
        this.captcha = captcha;
        this.sessionId = sessionId;
        this.remember = remember;
    }

    // Getters and Setters
    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getCaptcha() {
        return captcha;
    }

    public void setCaptcha(String captcha) {
        this.captcha = captcha;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public Boolean getRemember() {
        return remember;
    }

    public void setRemember(Boolean remember) {
        this.remember = remember;
    }

    @Override
    public String toString() {
        return "LoginRequest{" +
                "username='" + username + '\'' +
                ", sessionId='" + sessionId + '\'' +
                ", remember=" + remember +
                '}';
    }
}
