import { ref, reactive } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { resetPassword } from '@/api/auth';
import { useEncryptionStore } from '@/store/encryption';
const captchaUrl = ref('/api/captcha/image?' + Date.now())

export default {
  name: 'ResetPassword',
  setup() {
    const router = useRouter()
    const route = useRoute()

    const formData = reactive({
      password: '',
      confirmPassword: '',
      token: route.query.token
    })

    const errors = reactive({
      password: '',
      confirmPassword: '',
      general: ''
    })

    const loading = ref(false)
    const showPassword = ref(false)
    const showConfirmPassword = ref(false)

    const validateForm = () => {
      let isValid = true
      errors.password = ''
      errors.confirmPassword = ''
      errors.general = ''

      // 密码验证
      if (!formData.password) {
        errors.password = '密码不能为空'
        isValid = false
      } else if (formData.password.length < 8) {
        errors.password = '密码长度至少为8位'
        isValid = false
      }

      // 确认密码验证
      if (!formData.confirmPassword) {
        errors.confirmPassword = '请确认密码'
        isValid = false
      } else if (formData.password !== formData.confirmPassword) {
        errors.confirmPassword = '两次输入的密码不一致'
        isValid = false
      }

      // token验证
      if (!formData.token) {
        errors.general = '无效的重置链接'
        isValid = false
      }

      return isValid
    }

    const handleSubmit = async () => {
      if (!validateForm()) return;

      loading.value = true;
      errors.general = '';

      try {
        const encryptionStore = useEncryptionStore();
        await encryptionStore.initialize();
        const encryptedRequest = encryptionStore.encryptRequest({
          password: formData.password,
          confirmPassword: formData.confirmPassword,
          token: formData.token
        });

        await resetPassword(encryptedRequest);
        router.push({
          path: '/login',
          query: { message: '密码重置成功,请使用新密码登录' }
        });
      } catch (error) {
        errors.general = error.response?.data?.message || '密码重置失败,请重试';
      } finally {
        loading.value = false;
      }
    };

    const togglePasswordVisibility = (field) => {
      if (field === 'password') {
        showPassword.value = !showPassword.value
      } else {
        showConfirmPassword.value = !showConfirmPassword.value
      }
    }

    const goToLogin = () => {
      router.push('/login')
    }

    return {
      formData,
      errors,
      loading,
      showPassword,
      showConfirmPassword,
      handleSubmit,
      togglePasswordVisibility,
      goToLogin
    }
  }
}

captchaUrl.value = '/api/captcha/image?' + Date.now()
