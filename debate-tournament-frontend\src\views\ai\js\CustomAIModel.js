import { ref, reactive, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useAiModelStore } from '@/store/aiModel';
import { useUserStore } from '@/store/user';
import { useEncryptionStore } from '@/store/encryption';
import axios from 'axios';

export default {
  name: 'CustomAIModel',

  setup() {
    const route = useRoute();
    const router = useRouter();
    const aiModelStore = useAiModelStore();
    const userStore = useUserStore();
    const encryptionStore = useEncryptionStore();

    // 状态变量
    const loading = ref(false);
    const testing = ref(false);
    const errors = reactive({});
    const isEditing = ref(false);
    const modelId = ref(route.params.id);
    const showApiKey = ref(false);
    const testPrompt = ref('作为一个AI助手，你的主要功能是什么？');
    const testResult = ref('');

    // 初始化模型数据
    const modelData = reactive({
      name: '',
      provider: '',
      description: '',
      type: '',
      endpoint: '',
      apiKey: '',
      modelIdentifier: '',
      languageAbility: 80,
      logicalReasoning: 75,
      debateSkill: 70,
      knowledgeBreadth: 75,
      responseSpeed: 85,
      features: ['支持上下文理解', '适合辩论场景'],
      parameters: {
        temperature: 0.7,
        maxTokens: 2048,
        topP: 0.9
      },
      available: true,
      ownerId: userStore.user?.id || ''
    });

    // 计算属性
    const canTest = computed(() => {
      return modelData.type &&
             modelData.endpoint &&
             (modelData.apiKey || (isEditing.value && !modelData.apiKey));
    });

    // 方法
    // 获取模型详情 (编辑模式)
    const fetchModel = async () => {
      if (!modelId.value) return;

      loading.value = true;
      errors.value = {};

      try {
        // 获取模型详情
        const response = await axios.get(`/api/user/ai-models/${modelId.value}`);
        const model = response.data;

        // 填充表单
        Object.keys(modelData).forEach(key => {
          if (key !== 'apiKey' && model[key] !== undefined) {
            modelData[key] = model[key];
          }
        });

        // 确保参数对象存在
        if (model.parameters) {
          Object.keys(modelData.parameters).forEach(key => {
            if (model.parameters[key] !== undefined) {
              modelData.parameters[key] = model.parameters[key];
            }
          });
        }

        // 确保特点数组存在
        if (!model.features || !Array.isArray(model.features)) {
          modelData.features = ['支持上下文理解', '适合辩论场景'];
        }

        isEditing.value = true;
      } catch (err) {
        console.error('获取模型详情失败:', err);
        errors.value = { general: '无法加载模型数据，请稍后再试' };
      } finally {
        loading.value = false;
      }
    };

    // 添加特点
    const addFeature = () => {
      modelData.features.push('');
    };

    // 删除特点
    const removeFeature = (index) => {
      modelData.features.splice(index, 1);
    };

    // 测试模型
    const testModel = async () => {
      if (!canTest.value || !testPrompt.value.trim()) return;

      testing.value = true;
      testResult.value = '';

      try {
        // 加密API密钥
        let encryptedApiKey = '';
        if (modelData.apiKey) {
          encryptedApiKey = encryptionStore.encrypt(modelData.apiKey);
        }

        // 发送测试请求
        const response = await axios.post('/api/ai/test-model', {
          modelType: modelData.type,
          endpoint: modelData.endpoint,
          apiKey: encryptedApiKey || null, // 如果编辑模式下未修改，则传null
          modelIdentifier: modelData.modelIdentifier,
          prompt: testPrompt.value,
          parameters: modelData.parameters
        });

        testResult.value = response.data.result;
      } catch (err) {
        console.error('测试模型失败:', err);
        testResult.value = '测试失败：' + (err.response?.data?.message || err.message);
      } finally {
        testing.value = false;
      }
    };

    // 表单验证
    const validateForm = () => {
      errors.value = {};
      let valid = true;

      // 基本验证
      if (!modelData.name) {
        errors.value.name = '请输入模型名称';
        valid = false;
      }

      if (!modelData.provider) {
        errors.value.provider = '请输入提供商名称';
        valid = false;
      }

      if (!modelData.description) {
        errors.value.description = '请输入模型描述';
        valid = false;
      }

      if (!modelData.type) {
        errors.value.type = '请选择模型类型';
        valid = false;
      }

      if (!modelData.endpoint) {
        errors.value.endpoint = '请输入API端点';
        valid = false;
      } else if (!modelData.endpoint.startsWith('http')) {
        errors.value.endpoint = 'API端点必须是有效的URL';
        valid = false;
      }

      if (!isEditing.value && !modelData.apiKey) {
        errors.value.apiKey = '请输入API密钥';
        valid = false;
      }

      if (!modelData.modelIdentifier) {
        errors.value.modelIdentifier = '请输入模型名称标识符';
        valid = false;
      }

      return valid;
    };

    // 提交表单
    const handleSubmit = async () => {
      if (!validateForm()) return;

      loading.value = true;

      try {
        // 加密API密钥
        let encryptedApiKey = '';
        if (modelData.apiKey) {
          encryptedApiKey = encryptionStore.encrypt(modelData.apiKey);
        }

        // 准备要提交的数据
        const submitData = {
          ...modelData,
          apiKey: modelData.apiKey ? encryptedApiKey : undefined
        };

        // 如果编辑模式且未修改API密钥，则不发送API密钥
        if (isEditing.value && !modelData.apiKey) {
          delete submitData.apiKey;
        }

        let response;
        if (isEditing.value) {
          // 更新模型
          response = await axios.put(`/api/user/ai-models/${modelId.value}`, submitData);
        } else {
          // 创建新模型
          response = await axios.post('/api/user/ai-models', submitData);
        }

        // 更新store中的模型
        aiModelStore.addOrUpdateModel(response.data);

        // 返回到模型列表
        router.push('/ai-model');
      } catch (err) {
        console.error(isEditing.value ? '更新模型失败:' : '创建模型失败:', err);

        if (err.response?.data?.errors) {
          // 后端验证错误
          errors.value = err.response.data.errors;
        } else {
          errors.value.general = isEditing.value ?
            '更新模型失败，请稍后再试' :
            '创建模型失败，请稍后再试';
        }
      } finally {
        loading.value = false;
      }
    };

    // 生命周期钩子
    onMounted(async () => {
      // 检查是否已登录
      if (!userStore.isLoggedIn) {
        router.push('/login');
        return;
      }

      // 如果是编辑模式，获取模型详情
      if (modelId.value) {
        await fetchModel();
      }
    });

    return {
      loading,
      testing,
      errors,
      isEditing,
      showApiKey,
      modelData,
      testPrompt,
      testResult,
      canTest,
      addFeature,
      removeFeature,
      testModel,
      handleSubmit
    };
  }
};
