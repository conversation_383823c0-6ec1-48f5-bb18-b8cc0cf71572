import { ref, computed } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { verifyEmail, resendVerificationEmail } from '@/api/auth';
import { useEncryptionStore } from '@/store/encryption';
const captchaUrl = ref('/api/captcha/image?' + Date.now());

export default {
  name: 'VerifyEmail',
  setup() {
    const router = useRouter();
    const route = useRoute();

    const loading = ref(true);
    const verified = ref(false);
    const error = ref('');
    const resending = ref(false);

    const verificationStatus = computed(() => {
      if (loading.value) return '正在验证您的邮箱...';
      if (verified.value) return '邮箱验证成功!';
      if (error.value) return error.value;
      return '';
    });

    // 验证邮箱
    const verifyEmailToken = async () => {
      const token = route.query.token;

      if (!token) {
        loading.value = false;
        error.value = '无效的验证链接';
        return;
      }

      try {
        const encryptionStore = useEncryptionStore();
        await encryptionStore.initialize();
        const encryptedRequest = encryptionStore.encryptRequest({ token });

        await verifyEmail(encryptedRequest);
        verified.value = true;
      } catch (err) {
        error.value = err.response?.data?.message || '邮箱验证失败,请重试';
      } finally {
        loading.value = false;
      }
    };

    // 重新发送验证邮件
    const resendVerification = async () => {
      resending.value = true;
      try {
        const encryptionStore = useEncryptionStore();
        await encryptionStore.initialize();
        const encryptedRequest = encryptionStore.encryptRequest({});

        await resendVerificationEmail(encryptedRequest);
        error.value = '验证邮件已重新发送,请查收';
      } catch (err) {
        error.value = err.response?.data?.message || '发送验证邮件失败,请重试';
      } finally {
        resending.value = false;
        captchaUrl.value = '/api/captcha/image?' + Date.now();
      }
    };

    const goToLogin = () => {
      router.push('/login');
    };

    const goToHome = () => {
      router.push('/');
    };

    // 组件挂载时自动验证
    verifyEmailToken();

    return {
      loading,
      verified,
      error,
      resending,
      verificationStatus,
      resendVerification,
      goToLogin,
      goToHome
    };
  }
};
