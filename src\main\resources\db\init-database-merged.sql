-- ========================================
-- AI辩论赛平台数据库完整初始化脚本
-- ========================================
-- 版本: 1.0
-- 创建日期: 2025-05-25
-- 说明: 合并版本 - 包含完整表结构、索引优化和示例数据
-- 请在MySQL命令行或MySQL Workbench中执行此脚本

-- 设置字符集和排序规则
SET NAMES utf8mb4;
SET character_set_client = utf8mb4;

-- 创建数据库
CREATE DATABASE IF NOT EXISTS debate_tournament
DEFAULT CHARACTER SET utf8mb4
DEFAULT COLLATE utf8mb4_unicode_ci;

-- 创建开发环境数据库（可选）
-- CREATE DATABASE IF NOT EXISTS debate_tournament_dev
-- DEFAULT CHARACTER SET utf8mb4
-- DEFAULT COLLATE utf8mb4_unicode_ci;

-- 创建生产环境数据库（可选）
-- CREATE DATABASE IF NOT EXISTS debate_tournament_prod
-- DEFAULT CHARACTER SET utf8mb4
-- DEFAULT COLLATE utf8mb4_unicode_ci;

-- 使用主数据库
USE debate_tournament;

-- ========================================
-- 用户模块核心数据表
-- ========================================

-- 创建用户主表
CREATE TABLE IF NOT EXISTS users (
    id bigint NOT NULL AUTO_INCREMENT COMMENT '用户主键ID',
    username varchar(50) NOT NULL UNIQUE COMMENT '用户名（唯一标识）',
    email varchar(100) NOT NULL UNIQUE COMMENT '邮箱地址（唯一标识）',
    password varchar(255) NOT NULL COMMENT '加密后的用户密码',
    display_name varchar(100) DEFAULT NULL COMMENT '显示名称（可以和用户名不同）',
    avatar_url varchar(255) DEFAULT NULL COMMENT '头像URL地址',
    bio text DEFAULT NULL COMMENT '用户个人简介',
    email_verified boolean NOT NULL DEFAULT false COMMENT '邮箱是否已验证（true=已验证，false=未验证）',
    status varchar(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '用户账号状态（ACTIVE=活跃，INACTIVE=非活跃，LOCKED=锁定，BANNED=封禁）',
    login_attempts int NOT NULL DEFAULT 0 COMMENT '登录失败尝试次数',
    login_count int NOT NULL DEFAULT 0 COMMENT '总登录次数',
    last_login_attempt datetime DEFAULT NULL COMMENT '最后一次登录尝试时间',
    locked_until datetime DEFAULT NULL COMMENT '账号锁定截止时间',
    last_login_ip varchar(45) DEFAULT NULL COMMENT '最后登录IP地址（支持IPv6）',
    last_login_time datetime DEFAULT NULL COMMENT '最后成功登录时间',
    created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '账号创建时间',
    updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '账号信息最后更新时间',
    PRIMARY KEY (id),
    UNIQUE KEY uk_username (username) COMMENT '用户名唯一约束',
    UNIQUE KEY uk_email (email) COMMENT '邮箱地址唯一约束',
    KEY idx_status (status) COMMENT '用户状态索引',
    KEY idx_last_login_time (last_login_time) COMMENT '最后登录时间索引',
    KEY idx_email_verified (email_verified) COMMENT '邮箱验证状态索引',
    KEY idx_created_at (created_at) COMMENT '创建时间索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户基础信息表';

-- 创建用户偏好设置表
CREATE TABLE IF NOT EXISTS user_preferences (
    id bigint NOT NULL AUTO_INCREMENT COMMENT '偏好设置主键ID',
    user_id bigint NOT NULL COMMENT '关联的用户ID',
    default_debate_role varchar(20) DEFAULT NULL COMMENT '默认辩论角色（正方/反方）',
    language_preference varchar(10) NOT NULL DEFAULT 'zh_CN' COMMENT '语言偏好设置',
    debate_style varchar(20) DEFAULT NULL COMMENT '辩论风格偏好',
    ai_model_preference varchar(50) DEFAULT NULL COMMENT '首选AI模型',
    theme_mode varchar(10) NOT NULL DEFAULT 'light' COMMENT '界面主题模式（light=明亮，dark=暗黑）',
    font_size int NOT NULL DEFAULT 14 COMMENT '界面字体大小设置',
    enable_animations boolean NOT NULL DEFAULT true COMMENT '是否启用界面动画效果',
    created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '偏好设置创建时间',
    updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '偏好设置最后更新时间',
    PRIMARY KEY (id),
    UNIQUE KEY uk_user_id (user_id) COMMENT '用户ID唯一约束',
    KEY idx_language_preference (language_preference) COMMENT '语言偏好索引',
    KEY idx_theme_mode (theme_mode) COMMENT '主题模式索引',
    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE COMMENT '用户外键约束'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户偏好设置表';

-- 创建通知设置表
CREATE TABLE IF NOT EXISTS notification_settings (
    id bigint NOT NULL AUTO_INCREMENT COMMENT '通知设置主键ID',
    user_id bigint NOT NULL COMMENT '关联的用户ID',
    email_notifications boolean NOT NULL DEFAULT true COMMENT '是否启用邮件通知',
    debate_reminders boolean NOT NULL DEFAULT true COMMENT '是否接收辩论提醒通知',
    debate_start_notifications boolean NOT NULL DEFAULT true COMMENT '是否接收辩论开始通知',
    debate_end_notifications boolean NOT NULL DEFAULT true COMMENT '是否接收辩论结束通知',
    system_notifications boolean NOT NULL DEFAULT true COMMENT '是否接收系统通知',
    achievement_notifications boolean NOT NULL DEFAULT true COMMENT '是否接收成就通知',
    marketing_notifications boolean NOT NULL DEFAULT false COMMENT '是否接收营销推广通知',
    weekly_digest boolean NOT NULL DEFAULT false COMMENT '是否启用周报摘要',
    notification_frequency varchar(20) DEFAULT 'IMMEDIATE' COMMENT '通知频率（IMMEDIATE=立即，DAILY=每日，WEEKLY=每周）',
    quiet_hours_start time DEFAULT NULL COMMENT '免打扰时间开始',
    quiet_hours_end time DEFAULT NULL COMMENT '免打扰时间结束',
    created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '通知设置创建时间',
    updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '通知设置最后更新时间',
    PRIMARY KEY (id),
    UNIQUE KEY uk_user_id (user_id) COMMENT '用户ID唯一约束',
    KEY idx_email_notifications (email_notifications) COMMENT '邮件通知状态索引',
    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE COMMENT '用户外键约束'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户通知设置表';

-- 创建安全设置表
CREATE TABLE IF NOT EXISTS security_settings (
    id bigint NOT NULL AUTO_INCREMENT COMMENT '安全设置主键ID',
    user_id bigint NOT NULL COMMENT '关联的用户ID',
    two_factor_enabled boolean NOT NULL DEFAULT false COMMENT '是否启用双因素认证',
    two_factor_secret varchar(255) DEFAULT NULL COMMENT '双因素认证密钥',
    backup_codes text DEFAULT NULL COMMENT '双因素认证备用恢复代码',
    login_notifications boolean NOT NULL DEFAULT true COMMENT '是否启用登录通知',
    suspicious_activity_notifications boolean NOT NULL DEFAULT true COMMENT '是否启用可疑活动通知',
    session_timeout int NOT NULL DEFAULT 1440 COMMENT '会话超时时间（分钟）',
    password_last_changed datetime DEFAULT NULL COMMENT '密码最后修改时间',
    security_questions_set boolean NOT NULL DEFAULT false COMMENT '是否设置了安全问题',
    created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '安全设置创建时间',
    updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '安全设置最后更新时间',
    PRIMARY KEY (id),
    UNIQUE KEY uk_user_id (user_id) COMMENT '用户ID唯一约束',
    KEY idx_two_factor_enabled (two_factor_enabled) COMMENT '双因素认证状态索引',
    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE COMMENT '用户外键约束'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户安全设置表';

-- 创建用户设备管理表
CREATE TABLE IF NOT EXISTS user_devices (
    id bigint NOT NULL AUTO_INCREMENT COMMENT '设备记录主键ID',
    user_id bigint NOT NULL COMMENT '关联的用户ID',
    device_id varchar(255) NOT NULL COMMENT '设备唯一标识符',
    device_name varchar(100) DEFAULT NULL COMMENT '设备名称（用户自定义）',
    device_type varchar(20) DEFAULT NULL COMMENT '设备类型（DESKTOP=桌面，MOBILE=移动设备，TABLET=平板）',
    browser_info varchar(255) DEFAULT NULL COMMENT '浏览器信息',
    os_info varchar(100) DEFAULT NULL COMMENT '操作系统信息',
    ip_address varchar(45) DEFAULT NULL COMMENT '设备IP地址（支持IPv6）',
    location varchar(100) DEFAULT NULL COMMENT '设备地理位置',
    is_trusted boolean NOT NULL DEFAULT false COMMENT '是否为受信任设备',
    last_active datetime DEFAULT NULL COMMENT '设备最后活跃时间',
    created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '设备记录创建时间',
    updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '设备记录最后更新时间',
    PRIMARY KEY (id),
    UNIQUE KEY uk_user_device (user_id, device_id) COMMENT '用户设备组合唯一约束',
    KEY idx_device_type (device_type) COMMENT '设备类型索引',
    KEY idx_last_active (last_active) COMMENT '最后活跃时间索引',
    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE COMMENT '用户外键约束'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户设备管理表';

-- ========================================
-- 用户等级和资料信息表
-- ========================================

-- 创建用户等级表
CREATE TABLE IF NOT EXISTS user_levels (
    id bigint NOT NULL AUTO_INCREMENT COMMENT '等级记录主键ID',
    user_id bigint NOT NULL COMMENT '关联的用户ID',
    current_level int NOT NULL DEFAULT 1 COMMENT '当前用户等级（1-100）',
    current_experience int NOT NULL DEFAULT 0 COMMENT '当前等级内的经验值',
    total_experience bigint NOT NULL DEFAULT 0 COMMENT '累计总经验值',
    next_level_experience int NOT NULL DEFAULT 100 COMMENT '升级到下一等级所需经验值',
    level_experience bigint NOT NULL DEFAULT 0 COMMENT '当前等级内的经验值',
    debates_participated int NOT NULL DEFAULT 0 COMMENT '参与辩论总次数',
    debates_won int NOT NULL DEFAULT 0 COMMENT '辩论胜利次数',
    debates_lost int NOT NULL DEFAULT 0 COMMENT '辩论失败次数',
    debates_draws int NOT NULL DEFAULT 0 COMMENT '辩论平局次数',
    win_rate decimal(5,2) NOT NULL DEFAULT 0.00 COMMENT '胜率百分比',
    avg_score decimal(5,2) NOT NULL DEFAULT 0.00 COMMENT '平均辩论得分',
    best_score decimal(5,2) NOT NULL DEFAULT 0.00 COMMENT '最佳辩论得分',
    total_debate_time int NOT NULL DEFAULT 0 COMMENT '总辩论时间（分钟）',
    current_streak int NOT NULL DEFAULT 0 COMMENT '当前连胜/连败次数（正数为连胜，负数为连败）',
    streak_wins int NOT NULL DEFAULT 0 COMMENT '当前连胜次数',
    max_streak_wins int NOT NULL DEFAULT 0 COMMENT '最大连胜次数',
    best_win_streak int NOT NULL DEFAULT 0 COMMENT '最佳连胜记录',
    achievements json DEFAULT NULL COMMENT '用户成就JSON数据',
    titles json DEFAULT NULL COMMENT '用户称号JSON数据',
    last_debate_time datetime DEFAULT NULL COMMENT '最后一次参与辩论的时间',
    level_up_time datetime DEFAULT NULL COMMENT '最后一次升级的时间',
    created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
    updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录最后更新时间',
    PRIMARY KEY (id),
    UNIQUE KEY uk_user_id (user_id) COMMENT '用户ID唯一约束',
    KEY idx_current_level (current_level) COMMENT '当前等级索引',
    KEY idx_current_experience (current_experience) COMMENT '当前经验值索引',
    KEY idx_total_experience (total_experience) COMMENT '总经验值索引',
    KEY idx_debates_won (debate_wins) COMMENT '胜利次数索引',
    KEY idx_win_rate (win_rate) COMMENT '胜率索引',
    KEY idx_best_win_streak (best_win_streak) COMMENT '最佳连胜索引',
    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE COMMENT '用户外键约束'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户等级和统计信息表';

-- 创建用户详细资料表
CREATE TABLE IF NOT EXISTS user_profiles (
    id bigint NOT NULL AUTO_INCREMENT COMMENT '资料记录主键ID',
    user_id bigint NOT NULL COMMENT '关联的用户ID',
    display_name varchar(100) DEFAULT NULL COMMENT '显示名称（可以和用户名不同）',
    first_name varchar(50) DEFAULT NULL COMMENT '名字',
    last_name varchar(50) DEFAULT NULL COMMENT '姓氏',
    phone varchar(20) DEFAULT NULL COMMENT '电话号码',
    bio varchar(500) DEFAULT NULL COMMENT '个人简介或座右铭',
    birth_date date DEFAULT NULL COMMENT '出生日期',
    gender varchar(20) DEFAULT NULL COMMENT '性别（MALE=男性，FEMALE=女性，OTHER=其他，PREFER_NOT_TO_SAY=不愿透露）',
    country varchar(50) DEFAULT NULL COMMENT '国家',
    city varchar(50) DEFAULT NULL COMMENT '城市',
    location varchar(100) DEFAULT NULL COMMENT '所在地区或城市',
    school varchar(100) DEFAULT NULL COMMENT '学校名称',
    school_or_organization varchar(150) DEFAULT NULL COMMENT '学校或组织名称',
    major varchar(100) DEFAULT NULL COMMENT '专业',
    major_or_field varchar(100) DEFAULT NULL COMMENT '专业或研究领域',
    occupation varchar(100) DEFAULT NULL COMMENT '职业',
    interests text DEFAULT NULL COMMENT '兴趣爱好',
    social_links json DEFAULT NULL COMMENT '社交媒体链接JSON数据',
    avatar_filename varchar(255) DEFAULT NULL COMMENT '头像文件名',
    avatar_url varchar(500) DEFAULT NULL COMMENT '头像文件URL路径',
    avatar_file_name varchar(255) DEFAULT NULL COMMENT '头像文件原始名称',
    avatar_file_size bigint DEFAULT NULL COMMENT '头像文件大小（字节）',
    avatar_content_type varchar(100) DEFAULT NULL COMMENT '头像文件MIME类型',
    avatar_upload_time datetime DEFAULT NULL COMMENT '头像上传时间',
    profile_visibility varchar(20) NOT NULL DEFAULT 'PUBLIC' COMMENT '资料可见性（PUBLIC=公开，PRIVATE=私有）',
    is_profile_public boolean NOT NULL DEFAULT true COMMENT '资料是否公开可见',
    show_email boolean NOT NULL DEFAULT false COMMENT '是否公开显示邮箱地址',
    show_phone boolean NOT NULL DEFAULT false COMMENT '是否公开显示电话号码',
    show_real_name boolean NOT NULL DEFAULT false COMMENT '是否公开显示真实姓名',
    created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '资料创建时间',
    updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '资料最后更新时间',
    PRIMARY KEY (id),
    UNIQUE KEY uk_user_id (user_id) COMMENT '用户ID唯一约束',
    KEY idx_display_name (display_name) COMMENT '显示名称索引',
    KEY idx_gender (gender) COMMENT '性别索引',
    KEY idx_country (country) COMMENT '国家索引',
    KEY idx_city (city) COMMENT '城市索引',
    KEY idx_location (location) COMMENT '地区索引',
    KEY idx_school (school) COMMENT '学校索引',
    KEY idx_school_or_organization (school_or_organization) COMMENT '学校或组织索引',
    KEY idx_profile_visibility (profile_visibility) COMMENT '可见性索引',
    KEY idx_is_profile_public (is_profile_public) COMMENT '公开状态索引',
    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE COMMENT '用户外键约束'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户详细资料信息表';

-- ========================================
-- 认证和安全相关数据表
-- ========================================

-- 创建验证码表
CREATE TABLE IF NOT EXISTS captcha (
    id varchar(255) NOT NULL COMMENT '验证码会话ID',
    code varchar(10) NOT NULL COMMENT '验证码内容',
    used_count int NOT NULL DEFAULT 0 COMMENT '验证码使用次数',
    max_attempts int NOT NULL DEFAULT 3 COMMENT '最大尝试次数',
    created_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '验证码创建时间',
    expires_time datetime NOT NULL COMMENT '验证码过期时间',
    PRIMARY KEY (id),
    KEY idx_expires_time (expires_time) COMMENT '过期时间索引',
    KEY idx_created_time (created_time) COMMENT '创建时间索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='图形验证码存储表';

-- 创建邮箱验证表
CREATE TABLE IF NOT EXISTS email_verification (
    id bigint NOT NULL AUTO_INCREMENT COMMENT '邮箱验证记录主键ID',
    email varchar(100) NOT NULL COMMENT '待验证的邮箱地址',
    verification_code varchar(255) NOT NULL COMMENT '邮箱验证码',
    verification_type varchar(20) NOT NULL COMMENT '验证类型（REGISTER=注册验证，RESET_PASSWORD=密码重置，CHANGE_EMAIL=邮箱变更）',
    is_used boolean NOT NULL DEFAULT false COMMENT '验证码是否已被使用（true=已使用，false=未使用）',
    attempt_count int NOT NULL DEFAULT 0 COMMENT '验证尝试次数',
    max_attempts int NOT NULL DEFAULT 5 COMMENT '最大尝试次数',
    user_id bigint DEFAULT NULL COMMENT '关联的用户ID（可选）',
    created_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '验证码创建时间',
    expires_time datetime NOT NULL COMMENT '验证码过期时间',
    used_time datetime DEFAULT NULL COMMENT '验证码使用时间',
    PRIMARY KEY (id),
    KEY idx_email (email) COMMENT '邮箱地址索引',
    KEY idx_verification_code (verification_code) COMMENT '验证码索引',
    KEY idx_verification_type (verification_type) COMMENT '验证类型索引',
    KEY idx_expires_time (expires_time) COMMENT '过期时间索引',
    KEY idx_created_time (created_time) COMMENT '创建时间索引',
    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE COMMENT '用户外键约束'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='邮箱验证码记录表';

-- 创建密码重置令牌表
CREATE TABLE IF NOT EXISTS password_reset_token (
    id bigint NOT NULL AUTO_INCREMENT COMMENT '密码重置令牌主键ID',
    user_id bigint NOT NULL COMMENT '关联的用户ID',
    token varchar(255) NOT NULL UNIQUE COMMENT '密码重置令牌（唯一标识）',
    is_used boolean NOT NULL DEFAULT false COMMENT '令牌是否已被使用（true=已使用，false=未使用）',
    ip_address varchar(45) DEFAULT NULL COMMENT '请求重置的IP地址',
    user_agent varchar(500) DEFAULT NULL COMMENT '请求重置的用户代理信息',
    created_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '令牌创建时间',
    expires_time datetime NOT NULL COMMENT '令牌过期时间',
    used_time datetime DEFAULT NULL COMMENT '令牌使用时间',
    PRIMARY KEY (id),
    UNIQUE KEY uk_token (token) COMMENT '令牌唯一约束',
    KEY idx_user_id (user_id) COMMENT '用户ID索引',
    KEY idx_expires_time (expires_time) COMMENT '过期时间索引',
    KEY idx_created_time (created_time) COMMENT '创建时间索引',
    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE COMMENT '用户外键约束'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='密码重置令牌表';

-- 创建JWT令牌黑名单表（用于登出和安全控制）
CREATE TABLE IF NOT EXISTS jwt_blacklist (
    id bigint NOT NULL AUTO_INCREMENT COMMENT '黑名单记录主键ID',
    token_jti varchar(255) NOT NULL COMMENT 'JWT令牌的JTI（JWT ID）',
    user_id bigint NOT NULL COMMENT '令牌所属的用户ID',
    token_type varchar(20) NOT NULL COMMENT '令牌类型（ACCESS=访问令牌，REFRESH=刷新令牌）',
    reason varchar(50) DEFAULT NULL COMMENT '加入黑名单的原因（LOGOUT=用户登出，SECURITY=安全原因）',
    created_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '加入黑名单时间',
    expires_time datetime NOT NULL COMMENT '令牌原始过期时间',
    PRIMARY KEY (id),
    UNIQUE KEY uk_token_jti (token_jti) COMMENT 'JWT ID唯一约束',
    KEY idx_user_id (user_id) COMMENT '用户ID索引',
    KEY idx_expires_time (expires_time) COMMENT '过期时间索引',
    KEY idx_created_time (created_time) COMMENT '创建时间索引',
    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE COMMENT '用户外键约束'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='JWT令牌黑名单表';

-- ========================================
-- 插入示例数据
-- ========================================

-- 插入示例用户数据（密码为 'password123'）
INSERT IGNORE INTO users (username, email, password, display_name, status, email_verified) VALUES
('admin', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKKx6kLCdF4C9WKK8oUhHklVD1VO', '系统管理员', 'ACTIVE', true),
('testuser', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKKx6kLCdF4C9WKK8oUhHklVD1VO', '测试用户', 'ACTIVE', true),
('debater1', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKKx6kLCdF4C9WKK8oUhHklVD1VO', '辩手一号', 'ACTIVE', false),
('debater2', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKKx6kLCdF4C9WKK8oUhHklVD1VO', '辩手二号', 'ACTIVE', false);

-- 为示例用户创建偏好设置
INSERT IGNORE INTO user_preferences (user_id, language_preference, theme_mode, default_debate_role, ai_model_preference)
SELECT id, 'zh_CN', 'light', 'POSITIVE', 'gpt-3.5-turbo' FROM users WHERE username = 'admin'
UNION ALL
SELECT id, 'zh_CN', 'dark', 'NEGATIVE', 'claude-3-sonnet' FROM users WHERE username = 'testuser'
UNION ALL
SELECT id, 'zh_CN', 'light', 'POSITIVE', 'gpt-4' FROM users WHERE username = 'debater1'
UNION ALL
SELECT id, 'zh_CN', 'light', 'NEGATIVE', 'claude-3-haiku' FROM users WHERE username = 'debater2';

-- 为示例用户创建通知设置
INSERT IGNORE INTO notification_settings (user_id, email_notifications, debate_reminders, system_notifications, achievement_notifications, weekly_digest)
SELECT id, true, true, true, true, false FROM users WHERE username IN ('admin', 'testuser', 'debater1', 'debater2');

-- 为示例用户创建安全设置
INSERT IGNORE INTO security_settings (user_id, two_factor_enabled, login_notifications, suspicious_activity_notifications, session_timeout)
SELECT id, false, true, true, 1440 FROM users WHERE username IN ('admin', 'testuser', 'debater1', 'debater2');

-- 为示例用户创建等级信息
INSERT IGNORE INTO user_levels (user_id, current_level, current_experience, total_experience, next_level_experience, debates_participated, debates_won, debates_lost, win_rate)
SELECT id, 5, 80, 480, 100, 10, 7, 3, 70.00 FROM users WHERE username = 'admin'
UNION ALL
SELECT id, 3, 50, 250, 100, 5, 3, 2, 60.00 FROM users WHERE username = 'testuser'
UNION ALL
SELECT id, 1, 0, 0, 100, 0, 0, 0, 0.00 FROM users WHERE username = 'debater1'
UNION ALL
SELECT id, 1, 0, 0, 100, 0, 0, 0, 0.00 FROM users WHERE username = 'debater2';

-- 为示例用户创建详细资料
INSERT IGNORE INTO user_profiles (user_id, display_name, bio, gender, country, city, school, major, profile_visibility, show_email, show_real_name)
SELECT id, '系统管理员', '负责平台运营和管理的超级管理员账户', 'PREFER_NOT_TO_SAY', '中国', '北京', '清华大学', '计算机科学与技术', 'PUBLIC', false, false FROM users WHERE username = 'admin'
UNION ALL
SELECT id, '测试用户', '用于系统测试的普通用户账户', 'OTHER', '中国', '上海', '复旦大学', '软件工程', 'PUBLIC', false, false FROM users WHERE username = 'testuser'
UNION ALL
SELECT id, '辩手一号', '热爱辩论的新手用户', 'MALE', '中国', '广州', '中山大学', '法学', 'PUBLIC', false, false FROM users WHERE username = 'debater1'
UNION ALL
SELECT id, '辩手二号', '积极参与辩论活动的用户', 'FEMALE', '中国', '深圳', '南方科技大学', '哲学', 'PUBLIC', false, false FROM users WHERE username = 'debater2';

-- ========================================
-- 显示创建结果和统计信息
-- ========================================

-- 显示数据库列表
SHOW DATABASES LIKE 'debate_tournament%';

-- 显示当前数据库的所有表
SHOW TABLES;

-- 显示表数量统计
SELECT
    'Tables Created' as Info,
    COUNT(*) as Count
FROM information_schema.tables
WHERE table_schema = 'debate_tournament';

-- 显示用户数据统计
SELECT
    'Sample Users Created' as Info,
    COUNT(*) as Count
FROM users;

-- 显示主要表的记录数统计
SELECT
    'users' as table_name, COUNT(*) as record_count FROM users
UNION ALL
SELECT
    'user_preferences' as table_name, COUNT(*) as record_count FROM user_preferences
UNION ALL
SELECT
    'notification_settings' as table_name, COUNT(*) as record_count FROM notification_settings
UNION ALL
SELECT
    'security_settings' as table_name, COUNT(*) as record_count FROM security_settings
UNION ALL
SELECT
    'user_levels' as table_name, COUNT(*) as record_count FROM user_levels
UNION ALL
SELECT
    'user_profiles' as table_name, COUNT(*) as record_count FROM user_profiles;

-- ========================================
-- 数据库初始化完成
-- ========================================
--
-- 初始化完成说明：
-- 1. 数据库 'debate_tournament' 已成功创建
-- 2. 所有必需的数据表已创建完成，包括索引优化
-- 3. 示例用户数据已插入，包含4个测试账户
-- 4. 所有用户关联的设置表已初始化
--
-- 测试账户信息：
-- - admin/<EMAIL> (系统管理员，等级5)
-- - testuser/<EMAIL> (测试用户，等级3)
-- - debater1/<EMAIL> (新手辩手，等级1)
-- - debater2/<EMAIL> (新手辩手，等级1)
--
-- 所有账户的默认密码为: password123
--
-- 注意：生产环境部署前请务必：
-- 1. 修改默认密码
-- 2. 删除测试账户
-- 3. 配置适当的数据库权限
-- 4. 启用必要的安全设置
-- ========================================
