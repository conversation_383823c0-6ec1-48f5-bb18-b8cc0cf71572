package com.debate_ournament.users.entity;

import java.time.LocalDateTime;

import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;

/**
 * 用户通知设置实体类
 */
@Entity
@Table(name = "notification_settings")
@EntityListeners(AuditingEntityListener.class)
public class NotificationSettings {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false, unique = true)
    private User user;

    @Column(name = "email_notifications")
    private Boolean emailNotifications = true;

    @Column(name = "debate_invitations")
    private Boolean debateInvitations = true;

    @Column(name = "debate_updates")
    private Boolean debateUpdates = true;

    @Column(name = "mentions")
    private Boolean mentions = true;

    @Column(name = "system_announcements")
    private Boolean systemAnnouncements = true;

    @Column(name = "email_frequency", length = 20)
    private String emailFrequency = "immediate"; // immediate, daily, weekly

    @Column(name = "quiet_hours_start")
    private Integer quietHoursStart;

    @Column(name = "quiet_hours_end")
    private Integer quietHoursEnd;

    @Column(name = "notification_sound")
    private Boolean notificationSound = true;

    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @LastModifiedDate
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    // Getters and Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }

    public User getUser() { return user; }
    public void setUser(User user) { this.user = user; }

    public Boolean getEmailNotifications() { return emailNotifications; }
    public void setEmailNotifications(Boolean emailNotifications) { this.emailNotifications = emailNotifications; }

    public Boolean getDebateInvitations() { return debateInvitations; }
    public void setDebateInvitations(Boolean debateInvitations) { this.debateInvitations = debateInvitations; }

    public Boolean getDebateUpdates() { return debateUpdates; }
    public void setDebateUpdates(Boolean debateUpdates) { this.debateUpdates = debateUpdates; }

    public Boolean getMentions() { return mentions; }
    public void setMentions(Boolean mentions) { this.mentions = mentions; }

    public Boolean getSystemAnnouncements() { return systemAnnouncements; }
    public void setSystemAnnouncements(Boolean systemAnnouncements) { this.systemAnnouncements = systemAnnouncements; }

    public String getEmailFrequency() { return emailFrequency; }
    public void setEmailFrequency(String emailFrequency) { this.emailFrequency = emailFrequency; }

    public Integer getQuietHoursStart() { return quietHoursStart; }
    public void setQuietHoursStart(Integer quietHoursStart) { this.quietHoursStart = quietHoursStart; }

    public Integer getQuietHoursEnd() { return quietHoursEnd; }
    public void setQuietHoursEnd(Integer quietHoursEnd) { this.quietHoursEnd = quietHoursEnd; }

    public Boolean getNotificationSound() { return notificationSound; }
    public void setNotificationSound(Boolean notificationSound) { this.notificationSound = notificationSound; }

    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }

    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }
}
