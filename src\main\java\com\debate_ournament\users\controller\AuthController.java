package com.debate_ournament.users.controller;

import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.debate_ournament.dto.auth.LoginRequest;
import com.debate_ournament.dto.auth.RegisterRequest;
import com.debate_ournament.dto.common.ApiResponse;
import com.debate_ournament.users.service.AuthService;
import com.debate_ournament.users.service.EncryptionService;
import com.debate_ournament.util.JwtUtil;
import com.fasterxml.jackson.databind.ObjectMapper;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;

/**
 * 认证控制器
 */
@RestController
@RequestMapping("/api/auth")
public class AuthController {

    private static final Logger logger = LoggerFactory.getLogger(AuthController.class);

    private final AuthService authService;
    private final EncryptionService encryptionService;
    private final JwtUtil jwtUtil;
    private final ObjectMapper objectMapper;

    @Autowired
    public AuthController(AuthService authService, EncryptionService encryptionService,
                         JwtUtil jwtUtil, ObjectMapper objectMapper) {
        this.authService = authService;
        this.encryptionService = encryptionService;
        this.jwtUtil = jwtUtil;
        this.objectMapper = objectMapper;
    }

    /**
     * 用户登录（支持加密和明文两种方式）
     */
    @PostMapping("/login")
    public ResponseEntity<ApiResponse<Map<String, Object>>> login(
            @Valid @RequestBody Object request,
            HttpServletRequest httpRequest) {

        try {
            String clientIp = getClientIp(httpRequest);
            LoginRequest loginRequest;
            String keyId = null;
            String sessionKey = null;

            // 检查是否为加密请求
            if (request instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> requestMap = (Map<String, Object>) request;

                if (encryptionService.isEncryptedRequest(requestMap)) {
                    // 处理加密请求
                    logger.info("处理加密登录请求");

                    EncryptionService.DecryptResult decryptResult =
                        encryptionService.decryptRequestWithDetails(requestMap);

                    loginRequest = objectMapper.convertValue(decryptResult.getData(), LoginRequest.class);
                    keyId = decryptResult.getKeyId();
                    sessionKey = decryptResult.getSessionKey();
                } else {
                    // 处理明文请求
                    loginRequest = objectMapper.convertValue(request, LoginRequest.class);
                }
            } else if (request instanceof LoginRequest) {
                loginRequest = (LoginRequest) request;
            } else {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("请求格式错误"));
            }

            // 执行登录逻辑
            Map<String, Object> result = authService.login(loginRequest, clientIp);

            // 根据请求类型返回相应格式的响应
            if (keyId != null && sessionKey != null) {
                // 加密响应
                Map<String, Object> encryptedResponse = encryptionService.encryptResponse(result, keyId, sessionKey);
                return ResponseEntity.ok(ApiResponse.success("登录成功", encryptedResponse));
            } else {
                // 明文响应
                return ResponseEntity.ok(ApiResponse.success("登录成功", result));
            }

        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        } catch (IllegalStateException e) {
            return ResponseEntity.status(423) // Locked
                    .body(ApiResponse.error(e.getMessage()));
        } catch (SecurityException e) {
            logger.warn("安全验证失败: {}", e.getMessage());
            return ResponseEntity.status(400)
                    .body(ApiResponse.error("请求验证失败"));
        } catch (Exception e) {
            logger.error("登录异常", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("登录失败，请稍后重试"));
        }
    }

    /**
     * 用户注册（支持加密和明文两种方式）
     */
    @PostMapping("/register")
    public ResponseEntity<ApiResponse<Map<String, Object>>> register(
            @Valid @RequestBody Object request,
            HttpServletRequest httpRequest) {

        try {
            String clientIp = getClientIp(httpRequest);
            RegisterRequest registerRequest;
            String keyId = null;
            String sessionKey = null;

            // 检查是否为加密请求
            if (request instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> requestMap = (Map<String, Object>) request;

                if (encryptionService.isEncryptedRequest(requestMap)) {
                    // 处理加密请求
                    logger.info("处理加密注册请求");

                    EncryptionService.DecryptResult decryptResult =
                        encryptionService.decryptRequestWithDetails(requestMap);

                    registerRequest = objectMapper.convertValue(decryptResult.getData(), RegisterRequest.class);
                    keyId = decryptResult.getKeyId();
                    sessionKey = decryptResult.getSessionKey();
                } else {
                    // 处理明文请求
                    registerRequest = objectMapper.convertValue(request, RegisterRequest.class);
                }
            } else if (request instanceof RegisterRequest) {
                registerRequest = (RegisterRequest) request;
            } else {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("请求格式错误"));
            }

            // 执行注册逻辑
            Map<String, Object> result = authService.register(registerRequest, clientIp);

            // 根据请求类型返回相应格式的响应
            if (keyId != null && sessionKey != null) {
                // 加密响应
                Map<String, Object> encryptedResponse = encryptionService.encryptResponse(result, keyId, sessionKey);
                return ResponseEntity.ok(ApiResponse.success("注册成功", encryptedResponse));
            } else {
                // 明文响应
                return ResponseEntity.ok(ApiResponse.success("注册成功", result));
            }

        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        } catch (SecurityException e) {
            logger.warn("安全验证失败: {}", e.getMessage());
            return ResponseEntity.status(400)
                    .body(ApiResponse.error("请求验证失败"));
        } catch (Exception e) {
            logger.error("注册异常", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("注册失败，请稍后重试"));
        }
    }

    /**
     * 刷新令牌
     */
    @PostMapping("/refresh")
    public ResponseEntity<ApiResponse<Map<String, Object>>> refreshToken(
            @RequestHeader("Authorization") String authHeader) {

        try {
            String refreshToken = jwtUtil.extractTokenFromHeader(authHeader);
            if (refreshToken == null) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("刷新令牌不能为空"));
            }

            Map<String, Object> result = authService.refreshToken(refreshToken);

            return ResponseEntity.ok(ApiResponse.success("令牌刷新成功", result));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        } catch (Exception e) {
            logger.error("刷新令牌异常", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("令牌刷新失败"));
        }
    }

    /**
     * 用户登出
     */
    @PostMapping("/logout")
    public ResponseEntity<ApiResponse<Void>> logout(
            @RequestHeader("Authorization") String authHeader) {

        try {
            String accessToken = jwtUtil.extractTokenFromHeader(authHeader);
            if (accessToken == null) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("访问令牌不能为空"));
            }

            authService.logout(accessToken);

            return ResponseEntity.ok(ApiResponse.success("登出成功"));
        } catch (Exception e) {
            logger.error("登出异常", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("登出失败"));
        }
    }

    /**
     * 验证令牌
     */
    @GetMapping("/validate")
    public ResponseEntity<ApiResponse<Map<String, Object>>> validateToken(
            @RequestHeader("Authorization") String authHeader) {

        try {
            String accessToken = jwtUtil.extractTokenFromHeader(authHeader);
            if (accessToken == null) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("访问令牌不能为空"));
            }

            Map<String, Object> result = authService.validateToken(accessToken);

            return ResponseEntity.ok(ApiResponse.success("令牌有效", result));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.status(401)
                    .body(ApiResponse.error(e.getMessage()));
        } catch (IllegalStateException e) {
            return ResponseEntity.status(403)
                    .body(ApiResponse.error(e.getMessage()));
        } catch (Exception e) {
            logger.error("令牌验证异常", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("令牌验证失败"));
        }
    }

    /**
     * 获取当前用户信息
     */
    @GetMapping("/me")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getCurrentUser(
            @RequestHeader("Authorization") String authHeader) {

        try {
            String accessToken = jwtUtil.extractTokenFromHeader(authHeader);
            if (accessToken == null) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("访问令牌不能为空"));
            }

            Map<String, Object> result = authService.getCurrentUser(accessToken);

            return ResponseEntity.ok(ApiResponse.success("获取用户信息成功", result));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.status(401)
                    .body(ApiResponse.error(e.getMessage()));
        } catch (Exception e) {
            logger.error("获取用户信息异常", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("获取用户信息失败"));
        }
    }

    /**
     * 修改密码
     */
    @PostMapping("/change-password")
    public ResponseEntity<ApiResponse<Void>> changePassword(
            @RequestHeader("Authorization") String authHeader,
            @RequestParam String oldPassword,
            @RequestParam String newPassword) {

        try {
            String accessToken = jwtUtil.extractTokenFromHeader(authHeader);
            if (accessToken == null) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("访问令牌不能为空"));
            }

            authService.changePassword(accessToken, oldPassword, newPassword);

            return ResponseEntity.ok(ApiResponse.success("密码修改成功"));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        } catch (Exception e) {
            logger.error("修改密码异常", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("修改密码失败"));
        }
    }

    /**
     * 检查用户名是否可用
     */
    @GetMapping("/check-username")
    public ResponseEntity<ApiResponse<Boolean>> checkUsername(@RequestParam String username) {
        try {
            boolean available = authService.isUsernameAvailable(username);
            String message = available ? "用户名可用" : "用户名已被使用";

            return ResponseEntity.ok(ApiResponse.success(message, available));
        } catch (Exception e) {
            logger.error("检查用户名异常", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("检查用户名失败"));
        }
    }

    /**
     * 检查邮箱是否可用
     */
    @GetMapping("/check-email")
    public ResponseEntity<ApiResponse<Boolean>> checkEmail(@RequestParam String email) {
        try {
            boolean available = authService.isEmailAvailable(email);
            String message = available ? "邮箱可用" : "邮箱已被使用";

            return ResponseEntity.ok(ApiResponse.success(message, available));
        } catch (Exception e) {
            logger.error("检查邮箱异常", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("检查邮箱失败"));
        }
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIp(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }

        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }

        return request.getRemoteAddr();
    }
}
