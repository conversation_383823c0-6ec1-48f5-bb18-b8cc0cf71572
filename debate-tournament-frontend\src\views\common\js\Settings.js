import { ref, watch } from 'vue';

export default {
  name: 'SettingsPage',
  setup() {
    const activeTab = ref('theme');
    const theme = ref(localStorage.getItem('theme') || 'auto');
    const language = ref(localStorage.getItem('language') || 'zh-CN');
    const notifyDebate = ref(JSON.parse(localStorage.getItem('notifyDebate') || 'true'));
    const notifySystem = ref(JSON.parse(localStorage.getItem('notifySystem') || 'true'));

    // 主题切换
    function changeTheme(val) {
      localStorage.setItem('theme', val);
      // 这里可以触发全局主题切换逻辑
      document.documentElement.setAttribute('data-theme', val);
    }

    // 语言切换
    function changeLanguage(val) {
      localStorage.setItem('language', val);
      // 这里可以触发全局语言切换逻辑
      // 如：i18n.global.locale = val
    }

    // 通知偏好
    watch(notifyDebate, (val) => {
      localStorage.setItem('notifyDebate', val);
    });
    watch(notifySystem, (val) => {
      localStorage.setItem('notifySystem', val);
    });

    return {
      activeTab,
      theme,
      language,
      notifyDebate,
      notifySystem,
      changeTheme,
      changeLanguage
    };
  }
};
