package com.debate_ournament.users.entity;

import java.time.LocalDateTime;

import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToOne;
import jakarta.persistence.PrePersist;
import jakarta.persistence.PreUpdate;
import jakarta.persistence.Table;

/**
 * 用户偏好设置实体类
 * 对应数据库表：user_preferences
 */
@Entity
@Table(name = "user_preferences")
@EntityListeners(AuditingEntityListener.class)
public class UserPreferences {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false, columnDefinition = "bigint COMMENT '偏好设置主键ID'")
    private Long id;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false, unique = true,
               columnDefinition = "bigint COMMENT '关联的用户ID'")
    private User user;

    @Enumerated(EnumType.STRING)
    @Column(name = "default_debate_role", length = 20,
            columnDefinition = "varchar(20) COMMENT '默认辩论角色（正方/反方）'")
    private DebateRole defaultDebateRole;

    @Column(name = "language_preference", length = 10, nullable = false,
            columnDefinition = "varchar(10) DEFAULT 'zh_CN' COMMENT '语言偏好设置'")
    private String languagePreference = "zh_CN";

    @Enumerated(EnumType.STRING)
    @Column(name = "debate_style", length = 20,
            columnDefinition = "varchar(20) COMMENT '辩论风格偏好'")
    private DebateStyle debateStyle;

    @Column(name = "ai_model_preference", length = 50,
            columnDefinition = "varchar(50) COMMENT '首选AI模型'")
    private String aiModelPreference;

    @Enumerated(EnumType.STRING)
    @Column(name = "theme_mode", length = 10, nullable = false,
            columnDefinition = "varchar(10) DEFAULT 'light' COMMENT '界面主题模式（light=明亮，dark=暗黑）'")
    private ThemeMode themeMode = ThemeMode.LIGHT;

    @Column(name = "font_size", nullable = false,
            columnDefinition = "int DEFAULT 14 COMMENT '界面字体大小设置'")
    private Integer fontSize = 14;

    @Column(name = "enable_animations", nullable = false,
            columnDefinition = "boolean DEFAULT true COMMENT '是否启用界面动画效果'")
    private Boolean enableAnimations = true;

    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false,
            columnDefinition = "datetime DEFAULT CURRENT_TIMESTAMP COMMENT '偏好设置创建时间'")
    private LocalDateTime createdAt;

    @LastModifiedDate
    @Column(name = "updated_at", nullable = false,
            columnDefinition = "datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '偏好设置最后更新时间'")
    private LocalDateTime updatedAt;

    /**
     * 辩论角色枚举
     */
    public enum DebateRole {
        POSITIVE("正方"),
        NEGATIVE("反方"),
        NEUTRAL("中立");

        private final String description;

        DebateRole(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 辩论风格枚举
     */
    public enum DebateStyle {
        ACADEMIC("学术风格"),
        CASUAL("随意风格"),
        FORMAL("正式风格"),
        AGGRESSIVE("激进风格"),
        MODERATE("温和风格");

        private final String description;

        DebateStyle(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 主题模式枚举
     */
    public enum ThemeMode {
        LIGHT("明亮模式"),
        DARK("暗黑模式"),
        AUTO("自动模式");

        private final String description;

        ThemeMode(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    @PrePersist
    protected void onCreate() {
        LocalDateTime now = LocalDateTime.now();
        if (createdAt == null) {
            createdAt = now;
        }
        if (updatedAt == null) {
            updatedAt = now;
        }
        // 设置默认值
        if (languagePreference == null) {
            languagePreference = "zh_CN";
        }
        if (themeMode == null) {
            themeMode = ThemeMode.LIGHT;
        }
        if (fontSize == null) {
            fontSize = 14;
        }
        if (enableAnimations == null) {
            enableAnimations = true;
        }
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }

    // 业务方法

    /**
     * 重置为默认偏好设置
     */
    public void resetToDefaults() {
        this.defaultDebateRole = null;
        this.languagePreference = "zh_CN";
        this.debateStyle = null;
        this.aiModelPreference = null;
        this.themeMode = ThemeMode.LIGHT;
        this.fontSize = 14;
        this.enableAnimations = true;
    }

    /**
     * 检查是否使用暗黑主题
     */
    public boolean isDarkTheme() {
        return themeMode == ThemeMode.DARK;
    }

    /**
     * 检查是否启用动画
     */
    public boolean isAnimationsEnabled() {
        return enableAnimations != null && enableAnimations;
    }

    /**
     * 获取有效的字体大小（限制在合理范围内）
     */
    public int getEffectiveFontSize() {
        if (fontSize == null) {
            return 14;
        }
        // 限制字体大小在10-24之间
        return Math.max(10, Math.min(24, fontSize));
    }

    // Getters and Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }

    public User getUser() { return user; }
    public void setUser(User user) { this.user = user; }

    public DebateRole getDefaultDebateRole() { return defaultDebateRole; }
    public void setDefaultDebateRole(DebateRole defaultDebateRole) { this.defaultDebateRole = defaultDebateRole; }

    public String getLanguagePreference() { return languagePreference; }
    public void setLanguagePreference(String languagePreference) { this.languagePreference = languagePreference; }

    public DebateStyle getDebateStyle() { return debateStyle; }
    public void setDebateStyle(DebateStyle debateStyle) { this.debateStyle = debateStyle; }

    public String getAiModelPreference() { return aiModelPreference; }
    public void setAiModelPreference(String aiModelPreference) { this.aiModelPreference = aiModelPreference; }

    public ThemeMode getThemeMode() { return themeMode; }
    public void setThemeMode(ThemeMode themeMode) { this.themeMode = themeMode; }

    public Integer getFontSize() { return fontSize; }
    public void setFontSize(Integer fontSize) { this.fontSize = fontSize; }

    public Boolean getEnableAnimations() { return enableAnimations; }
    public void setEnableAnimations(Boolean enableAnimations) { this.enableAnimations = enableAnimations; }

    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }

    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }

    @Override
    public String toString() {
        return "UserPreferences{" +
                "id=" + id +
                ", defaultDebateRole=" + defaultDebateRole +
                ", languagePreference='" + languagePreference + '\'' +
                ", debateStyle=" + debateStyle +
                ", themeMode=" + themeMode +
                ", fontSize=" + fontSize +
                ", enableAnimations=" + enableAnimations +
                '}';
    }
}
