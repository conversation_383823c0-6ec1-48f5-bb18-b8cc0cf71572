@use '@/styles/mixins';
@use '@/styles/variables.scss';

.debate-hall {
  padding: var(--spacing-lg) 0;

  &__header {
    margin-bottom: var(--spacing-lg);
  }

  &__title {
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: var(--spacing-md);
    color: var(--color-primary);
  }

  &__filters {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
    align-items: center;

    @media (max-width: 768px) {
      flex-direction: column;
      align-items: flex-start;
    }
  }

  &__filter-group {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
  }

  &__search {
    flex: 1;
    max-width: 400px;
    position: relative;

    @media (max-width: 768px) {
      max-width: 100%;
      width: 100%;
    }

    .search-icon {
      position: absolute;
      left: var(--spacing-sm);
      top: 50%;
      transform: translateY(-50%);
      color: rgba(0, 0, 0, 0.5);
    }

    input {
      width: 100%;
      padding: var(--spacing-sm) var(--spacing-sm) var(--spacing-sm) calc(var(--spacing-lg) + var(--spacing-xs));
      border-radius: var(--shape-corner-small);
      border: 1px solid rgba(0, 0, 0, 0.2);

      &:focus {
        outline: none;
        border-color: var(--color-primary);
        box-shadow: 0 0 0 2px rgba(103, 80, 164, 0.2);
      }
    }
  }

  &__create-btn {
    margin-left: auto;

    @media (max-width: 768px) {
      margin-left: 0;
      width: 100%;
    }
  }

  &__content {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);

    @media (max-width: 576px) {
      grid-template-columns: 1fr;
    }
  }

  &__pagination {
    display: flex;
    justify-content: center;
    margin-top: var(--spacing-lg);
  }

  &__empty {
    text-align: center;
    padding: var(--spacing-xl) 0;
    color: rgba(0, 0, 0, 0.5);

    h3 {
      font-size: 1.5rem;
      margin-bottom: var(--spacing-md);
    }

    p {
      margin-bottom: var(--spacing-lg);
    }
  }
}

/* 创建辩论对话框 */
.create-debate-modal {
  &__form {
    margin-top: var(--spacing-md);
  }

  &__actions {
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-md);
    margin-top: var(--spacing-lg);
  }

  .form-group {
    margin-bottom: var(--spacing-md);
  }

  .form-label {
    display: block;
    margin-bottom: var(--spacing-xs);
    font-weight: 500;
  }

  .form-control {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid rgba(0, 0, 0, 0.2);
    border-radius: var(--shape-corner-small);

    &:focus {
      outline: none;
      border-color: var(--color-primary);
      box-shadow: 0 0 0 2px rgba(103, 80, 164, 0.2);
    }
  }

  textarea.form-control {
    min-height: 120px;
    resize: vertical;
  }
}

/* 筛选标签样式 */
.filter-tag {
  display: inline-flex;
  align-items: center;
  padding: var(--spacing-xs) var(--spacing-sm);
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 16px;
  font-size: 0.875rem;
  cursor: pointer;
  transition: background-color 0.2s;

  &:hover {
    background-color: rgba(0, 0, 0, 0.1);
  }

  &.active {
    background-color: var(--color-primary);
    color: white;
  }

  &__close {
    margin-left: var(--spacing-xs);
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;

    &:hover {
      background-color: rgba(0, 0, 0, 0.1);
    }
  }
}

/* 辩论卡片增强样式 */
.debate-card {
  height: 100%;
  display: flex;
  flex-direction: column;

  &__body {
    flex: 1;
  }

  &__participants {
    display: flex;
    gap: var(--spacing-xs);
    margin-top: var(--spacing-sm);
    flex-wrap: wrap;
  }

  &__participant {
    display: flex;
    align-items: center;
    font-size: 0.75rem;
    padding: 2px var(--spacing-xs);
    border-radius: 4px;
    background-color: rgba(0, 0, 0, 0.05);

    &--supporter {
      color: var(--debate-supporter-color);
      background-color: rgba(26, 86, 219, 0.1);
    }

    &--opposer {
      color: var(--debate-opposer-color);
      background-color: rgba(245, 158, 11, 0.1);
    }

    &--judge {
      color: var(--debate-neutral-color);
      background-color: rgba(107, 114, 128, 0.1);
    }

    &--host {
      color: var(--color-primary);
      background-color: rgba(103, 80, 164, 0.1);
    }
  }
}

@media (max-width: map-get($breakpoints, md)) {
  .debate-hall {
    padding: $spacing-md;
  }

  .hall-header {
    h1 {
      font-size: 1.5rem;
    }
  }

  .hall-filters {
    flex-direction: column;
    align-items: stretch;

    .filter-group {
      flex-wrap: wrap;
    }

    .search-box {
      max-width: none;
    }
  }

  .debates-grid {
    grid-template-columns: 1fr;
  }
}
