package com.debate_ournament.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 加密配置类
 *
 * 管理RSA密钥对的存储路径、密钥轮换策略等配置
 *
 * <AUTHOR> Debate Tournament Platform
 * @version 1.0
 * @since 2024
 */
@Configuration
@ConfigurationProperties(prefix = "app.encryption")
public class EncryptionConfig {

    /**
     * 密钥存储路径
     */
    private String keyStorePath = "keys";

    /**
     * 公钥文件名
     */
    private String publicKeyFileName = "public_key.pem";

    /**
     * 私钥文件名
     */
    private String privateKeyFileName = "private_key.pem";

    /**
     * 密钥对ID文件名
     */
    private String keyIdFileName = "key_id.txt";

    /**
     * RSA密钥长度（位）
     */
    private int rsaKeySize = 2048;

    /**
     * 密钥轮换间隔（小时）
     */
    private int keyRotationIntervalHours = 24;

    /**
     * 是否启动时自动生成密钥
     */
    private boolean autoGenerateOnStartup = true;

    /**
     * 是否启用密钥轮换
     */
    private boolean enableKeyRotation = false;

    // Getters and Setters
    public String getKeyStorePath() {
        return keyStorePath;
    }

    public void setKeyStorePath(String keyStorePath) {
        this.keyStorePath = keyStorePath;
    }

    public String getPublicKeyFileName() {
        return publicKeyFileName;
    }

    public void setPublicKeyFileName(String publicKeyFileName) {
        this.publicKeyFileName = publicKeyFileName;
    }

    public String getPrivateKeyFileName() {
        return privateKeyFileName;
    }

    public void setPrivateKeyFileName(String privateKeyFileName) {
        this.privateKeyFileName = privateKeyFileName;
    }

    public String getKeyIdFileName() {
        return keyIdFileName;
    }

    public void setKeyIdFileName(String keyIdFileName) {
        this.keyIdFileName = keyIdFileName;
    }

    public int getRsaKeySize() {
        return rsaKeySize;
    }

    public void setRsaKeySize(int rsaKeySize) {
        this.rsaKeySize = rsaKeySize;
    }

    public int getKeyRotationIntervalHours() {
        return keyRotationIntervalHours;
    }

    public void setKeyRotationIntervalHours(int keyRotationIntervalHours) {
        this.keyRotationIntervalHours = keyRotationIntervalHours;
    }

    public boolean isAutoGenerateOnStartup() {
        return autoGenerateOnStartup;
    }

    public void setAutoGenerateOnStartup(boolean autoGenerateOnStartup) {
        this.autoGenerateOnStartup = autoGenerateOnStartup;
    }

    public boolean isEnableKeyRotation() {
        return enableKeyRotation;
    }

    public void setEnableKeyRotation(boolean enableKeyRotation) {
        this.enableKeyRotation = enableKeyRotation;
    }
}
