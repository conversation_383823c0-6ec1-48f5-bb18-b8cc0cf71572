/**
 * 主样式入口文件
 * 按照依赖关系顺序导入所有样式文件
 */

// 1. 变量和混合宏 - 不会生成实际CSS
@forward './variables';
@forward './mixins';

// 2. 基础样式 - 包括重置、排版、表单等基础元素
@use './base';

// 3. 组件样式 - 所有可重用的UI组件
@use './components';

// 4. 辅助类和工具类
.visually-hidden {
  position: absolute;
  width: 1px;
  height: 1px;
  margin: -1px;
  padding: 0;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

// 5. 动画
@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes slideInUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }

  to {
    transform: translateY(0);
    opacity: 1;
  }
}

// 6. 媒体查询
@media print {
  .no-print {
    display: none !important;
  }
}

// 7. 暗色模式支持
@media (prefers-color-scheme: dark) {
  :root {
    // 可以添加暗色模式变量覆盖
  }
}
