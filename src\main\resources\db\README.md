# AI辩论赛平台 - 数据库初始化系统

## 概述

本系统提供了完整的数据库自动初始化功能，包括表结构创建、示例数据插入和运行时管理。系统会在应用启动时自动检查数据库状态，并根据需要执行初始化操作。

## 文件结构

```
src/main/resources/db/
├── README.md                    # 本说明文档
├── init-database.sql           # 原始完整初始化脚本
├── init-database-clean.sql     # 简化版初始化脚本（含示例数据）
└── init-database-merged.sql    # 合并版完整初始化脚本（推荐使用）
```

## 主要特性

### 1. 自动初始化
- 应用启动时自动检查数据库状态
- 智能判断是否需要执行初始化
- 支持增量初始化和完全重新初始化

### 2. 完整的表结构
- **用户核心表**：users, user_preferences, notification_settings, user_levels, user_profiles
- **安全相关表**：security_settings, user_devices, captcha, email_verification, password_reset_token, jwt_blacklist
- **索引优化**：为查询性能优化的数据库索引

### 3. 示例数据
- 预置管理员账户：admin / <EMAIL>
- 测试用户账户：testuser, debater1, debater2
- 完整的用户设置和配置数据

### 4. 管理接口
- RESTful API 用于数据库状态管理
- 支持健康检查和手动重新初始化

## 配置说明

### application.yml 配置项

```yaml
app:
  database:
    init:
      enabled: true                                    # 是否启用数据库自动初始化
      force: false                                     # 是否强制重新初始化（谨慎使用）
      script-path: db/init-database-merged.sql         # 初始化脚本路径
      check-on-startup: true                           # 启动时检查数据库状态
```

### 环境配置

#### 开发环境 (默认)
```yaml
spring:
  profiles:
    active: default

app:
  database:
    init:
      enabled: true   # 开发环境启用自动初始化
```

#### 测试环境
```yaml
spring:
  profiles:
    active: test

app:
  database:
    init:
      enabled: false  # 测试环境使用 H2 内存数据库
```

#### 生产环境
```yaml
spring:
  profiles:
    active: prod

app:
  database:
    init:
      enabled: false  # 生产环境禁用自动初始化，手动执行
```

## 使用方法

### 1. 首次启动（自动初始化）

```bash
# 确保 MySQL 服务运行
sudo service mysql start

# 创建数据库（如果不存在）
mysql -u root -p -e "CREATE DATABASE IF NOT EXISTS debate_tournament CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

# 启动应用（会自动检查并初始化数据库）
mvn spring-boot:run
```

应用启动日志示例：
```
2025-05-25 21:45:00 [main] INFO  c.d.config.DatabaseInitializationConfig - 开始检查数据库初始化状态...
2025-05-25 21:45:01 [main] INFO  c.d.config.DatabaseInitializationConfig - 检测到表 'users' 不存在，需要初始化数据库
2025-05-25 21:45:01 [main] INFO  c.d.config.DatabaseInitializationConfig - 检测到数据库需要初始化，开始执行初始化脚本...
2025-05-25 21:45:02 [main] INFO  c.d.config.DatabaseInitializationConfig - 数据库初始化脚本执行成功
2025-05-25 21:45:02 [main] INFO  c.d.config.DatabaseInitializationConfig - 数据库中共有 11 个表
2025-05-25 21:45:02 [main] INFO  c.d.config.DatabaseInitializationConfig - 用户表中共有 4 个用户
2025-05-25 21:45:02 [main] INFO  c.d.config.DatabaseInitializationConfig - 示例管理员用户已创建: admin
2025-05-25 21:45:02 [main] INFO  c.d.config.DatabaseInitializationConfig - 数据库初始化完成！
```

### 2. 手动执行初始化脚本

```bash
# 方法1：使用 MySQL 命令行
mysql -u root -p debate_tournament < src/main/resources/db/init-database-merged.sql

# 方法2：使用 MySQL Workbench
# 打开 init-database-merged.sql 文件并执行
```

### 3. 管理接口使用

#### 获取数据库状态
```bash
curl -X GET "http://localhost:8080/api/admin/database/status" \
  -H "Authorization: Bearer YOUR_ADMIN_JWT_TOKEN"
```

响应示例：
```json
{
  "code": 200,
  "message": "数据库状态获取成功",
  "data": {
    "initialized": true,
    "userCount": 4,
    "tableCount": 11,
    "initEnabled": true,
    "error": null
  },
  "timestamp": "2025-05-25T21:45:00"
}
```

#### 数据库健康检查
```bash
curl -X GET "http://localhost:8080/api/admin/database/health" \
  -H "Authorization: Bearer YOUR_ADMIN_JWT_TOKEN"
```

#### 强制重新初始化（谨慎使用）
```bash
curl -X POST "http://localhost:8080/api/admin/database/reinitialize?confirmDangerous=true" \
  -H "Authorization: Bearer YOUR_ADMIN_JWT_TOKEN"
```

## 预置用户账户

| 用户名 | 邮箱 | 密码 | 角色 | 状态 |
|--------|------|------|------|------|
| admin | <EMAIL> | password123 | 管理员 | 已验证 |
| testuser | <EMAIL> | password123 | 普通用户 | 已验证 |
| debater1 | <EMAIL> | password123 | 普通用户 | 未验证 |
| debater2 | <EMAIL> | password123 | 普通用户 | 未验证 |

**注意：生产环境部署前必须修改这些默认密码！**

## 表结构说明

### 核心用户表
- `users` - 用户基础信息
- `user_preferences` - 用户偏好设置
- `notification_settings` - 通知设置
- `user_levels` - 用户等级和统计
- `user_profiles` - 用户详细资料

### 安全相关表
- `security_settings` - 安全设置（双因素认证等）
- `user_devices` - 用户设备管理
- `captcha` - 验证码存储
- `email_verification` - 邮箱验证
- `password_reset_token` - 密码重置令牌
- `jwt_blacklist` - JWT令牌黑名单

## 故障排除

### 常见问题

#### 1. 数据库连接失败
```
错误：Could not create connection to database server
解决：检查 MySQL 服务是否运行，用户名密码是否正确
```

#### 2. 表已存在错误
```
错误：Table 'users' already exists
解决：这是正常情况，脚本使用 CREATE TABLE IF NOT EXISTS
```

#### 3. 权限不足
```
错误：Access denied for user
解决：确保数据库用户有创建表和插入数据的权限
```

#### 4. 字符集问题
```
错误：Incorrect string value
解决：确保数据库和表使用 utf8mb4 字符集
```

### 重置数据库

如果需要完全重置数据库：

```sql
-- 警告：这会删除所有数据！
DROP DATABASE IF EXISTS debate_tournament;
CREATE DATABASE debate_tournament CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE debate_tournament;
SOURCE src/main/resources/db/init-database-merged.sql;
```

### 查看日志

检查应用日志以了解初始化过程：

```bash
# 查看应用日志
tail -f logs/ai-debate-tournament.log

# 或在IDE中查看控制台输出
```

## 最佳实践

### 开发环境
1. 启用自动初始化 (`enabled: true`)
2. 使用合并版脚本 (`init-database-merged.sql`)
3. 定期备份开发数据

### 测试环境
1. 使用 H2 内存数据库
2. 禁用自动初始化 (`enabled: false`)
3. 每次测试使用新的数据库实例

### 生产环境
1. **禁用自动初始化** (`enabled: false`)
2. 手动执行初始化脚本
3. 修改所有默认密码
4. 删除测试用户账户
5. 设置适当的数据库权限
6. 配置定期备份

## 安全注意事项

1. **修改默认密码**：生产环境必须修改所有默认用户密码
2. **删除测试账户**：删除 testuser、debater1、debater2 账户
3. **数据库权限**：为应用创建专用数据库用户，限制权限
4. **环境变量**：使用环境变量存储敏感配置
5. **备份策略**：建立完善的数据库备份和恢复机制

## 维护和监控

### 定期维护
- 监控数据库性能
- 定期清理过期验证码和令牌
- 检查和优化索引使用情况

### 监控指标
- 数据库连接数
- 查询响应时间
- 表空间使用情况
- 用户增长趋势

## 版本历史

- **v1.0** (2025-05-25)
  - 初始版本
  - 完整的用户管理系统表结构
  - 自动初始化功能
  - 管理接口

---

如有问题或建议，请联系开发团队或查看项目文档。
