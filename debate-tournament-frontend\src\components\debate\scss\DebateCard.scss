@use '@/styles/variables' as v;
@use '@/styles/mixins' as m;

.debate-card {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: white;
  border-radius: v.$shape-corner-medium;
  padding: v.$spacing-md;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
  overflow: hidden;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  &__header {
    margin-bottom: v.$spacing-md;
  }

  &__title {
    font-size: 1.25rem;
    margin-bottom: v.$spacing-xs;
    font-weight: 600;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: v.$color-on-surface;
  }

  &__meta {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: v.$spacing-sm;
    margin-bottom: v.$spacing-sm;
  }

  &__category {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    background-color: rgba(v.$color-primary, 0.1);
    color: v.$color-primary;
    border-radius: 1rem;
  }

  &__status {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 1rem;

    &--active {
      background-color: rgba(v.$color-success, 0.1);
      color: v.$color-success;
    }

    &--waiting {
      background-color: rgba(v.$color-warning, 0.1);
      color: v.$color-warning;
    }

    &--completed {
      background-color: rgba(v.$color-secondary, 0.1);
      color: v.$color-secondary;
    }
  }

  &__description {
    color: v.$color-secondary;
    margin-bottom: v.$spacing-md;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    line-height: 1.5;
    flex-grow: 1;
  }

  &__stats {
    display: flex;
    margin-bottom: v.$spacing-md;
    gap: v.$spacing-lg;

    @include m.respond-to(sm) {
      gap: v.$spacing-md;
      flex-wrap: wrap;
    }
  }

  &__stat {
    display: flex;
    align-items: center;
    font-size: 0.875rem;
    color: v.$color-secondary;
  }

  &__icon {
    width: 16px;
    height: 16px;
    margin-right: 4px;
    background-color: v.$color-secondary;
    mask-size: contain;
    mask-position: center;
    mask-repeat: no-repeat;

    &--user {
      mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z'/%3E%3C/svg%3E");
    }

    &--speech {
      mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M20 2H4c-1.1 0-2 .9-2 2v18l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2z'/%3E%3C/svg%3E");
    }

    &--vote {
      mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M7 19h10v3H7v-3zm0-1h10v-1H7v1zm15-13.84v9.68c0 1.62-1.31 2.94-2.94 3.16H4.94C3.31 16.78 2 15.46 2 13.84V4.16C2 2.54 3.31 1.22 4.94 1h14.12C20.69 1.22 22 2.54 22 4.16zM18 4c0-.55-.45-1-1-1H7c-.55 0-1 .45-1 1v8c0 .55.45 1 1 1h10c.55 0 1-.45 1-1V4z'/%3E%3C/svg%3E");
    }

    &--supporters {
      mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M2 20h20v4H2v-4zm2-8h2v7H4v-7zm5 0h2v7H9v-7zm4 0h2v7h-2v-7zm5 0h2v7h-2v-7zM2 7l10-5 10 5v4H2V7zm2 1.236V9h16v-.764l-8-4-8 4zM12 8a1 1 0 1 1 0-2 1 1 0 0 1 0 2z'/%3E%3C/svg%3E");
      background-color: v.$debate-supporter-color;
    }

    &--opposers {
      mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M2 20h20v4H2v-4zm2-8h2v7H4v-7zm5 0h2v7H9v-7zm4 0h2v7h-2v-7zm5 0h2v7h-2v-7zM2 7l10-5 10 5v4H2V7zm2 1.236V9h16v-.764l-8-4-8 4zM12 8a1 1 0 1 1 0-2 1 1 0 0 1 0 2z'/%3E%3C/svg%3E");
      background-color: v.$debate-opposer-color;
    }
  }

  &__sides {
    display: flex;
    align-items: center;
    margin-bottom: v.$spacing-md;

    &-separator {
      margin: 0 v.$spacing-sm;
      color: v.$color-secondary;
      font-weight: 600;
    }

    .side {
      flex: 1;
      text-align: center;
      padding: v.$spacing-xs;
      border-radius: v.$shape-corner-small;

      &--supporter {
        color: v.$debate-supporter-color;
        background-color: rgba(v.$debate-supporter-color, 0.1);
      }

      &--opposer {
        color: v.$debate-opposer-color;
        background-color: rgba(v.$debate-opposer-color, 0.1);
      }

      .count {
        font-size: 1.25rem;
        font-weight: 600;
      }

      .label {
        font-size: 0.85rem;
      }
    }
  }

  &__footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.875rem;
    padding-top: v.$spacing-sm;
    border-top: 1px solid #eee;
    margin-top: auto;
  }

  &__creator {
    display: flex;
    align-items: center;
  }

  &__avatar {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    margin-right: v.$spacing-xs;
    object-fit: cover;
  }

  &__time {
    color: v.$color-secondary;
  }

  &__participants {
    display: flex;
    align-items: center;

    &-count {
      margin-left: v.$spacing-xs;
      font-size: 0.85rem;
      color: v.$color-secondary;
    }

    &-avatars {
      display: flex;

      .avatar {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        border: 2px solid white;
        margin-left: -8px;

        &:first-child {
          margin-left: 0;
        }
      }
    }
  }
}
