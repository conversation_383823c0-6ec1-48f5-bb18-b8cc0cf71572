package com.debate_ournament.ai.service.impl;

import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.Duration;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.debate_ournament.ai.entity.AiConfig;
import com.debate_ournament.ai.entity.ChatMessage;
import com.debate_ournament.ai.service.AiClient;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * DeepSeek客户端实现
 * 支持DeepSeek API调用
 */
@Component
public class DeepSeekClient implements AiClient {

    private static final Logger logger = LoggerFactory.getLogger(DeepSeekClient.class);
    private static final String DEFAULT_BASE_URL = "https://api.deepseek.com/v1";
    private static final String DEFAULT_MODEL = "deepseek-chat";

    private final HttpClient httpClient;
    private final ObjectMapper objectMapper;

    public DeepSeekClient() {
        this.httpClient = HttpClient.newBuilder()
                .connectTimeout(Duration.ofSeconds(30))
                .build();
        this.objectMapper = new ObjectMapper();
    }

    @Override
    public String sendChatMessage(List<ChatMessage> messages, AiConfig config) throws Exception {
        long startTime = System.currentTimeMillis();

        try {
            String baseUrl = config.getBaseUrl() != null ? config.getBaseUrl() : DEFAULT_BASE_URL;
            String url = baseUrl + "/chat/completions";

            // 构建请求体
            Map<String, Object> requestBody = buildRequestBody(messages, config);
            String jsonBody = objectMapper.writeValueAsString(requestBody);

            // 构建HTTP请求
            HttpRequest request = HttpRequest.newBuilder()
                    .uri(URI.create(url))
                    .header("Content-Type", "application/json")
                    .header("Authorization", "Bearer " + config.getApiKey())
                    .POST(HttpRequest.BodyPublishers.ofString(jsonBody))
                    .timeout(Duration.ofSeconds(60))
                    .build();

            // 发送请求
            HttpResponse<String> response = httpClient.send(request,
                    HttpResponse.BodyHandlers.ofString());

            if (response.statusCode() != 200) {
                String errorMsg = "DeepSeek API请求失败: " + response.statusCode() + " - " + response.body();
                logger.error(errorMsg);
                throw new RuntimeException(errorMsg);
            }

            // 解析响应
            JsonNode responseJson = objectMapper.readTree(response.body());
            JsonNode choices = responseJson.get("choices");

            if (choices != null && choices.size() > 0) {
                JsonNode firstChoice = choices.get(0);
                JsonNode messageNode = firstChoice.get("message");
                if (messageNode != null) {
                    String content = messageNode.get("content").asText();

                    long responseTime = System.currentTimeMillis() - startTime;
                    logger.info("DeepSeek响应成功，耗时: {}ms", responseTime);

                    return content;
                }
            }

            throw new RuntimeException("无法解析DeepSeek响应内容");

        } catch (Exception e) {
            logger.error("调用DeepSeek API失败", e);
            throw e;
        }
    }

    @Override
    public void sendStreamChatMessage(List<ChatMessage> messages, AiConfig config, StreamCallback callback)
            throws Exception {
        try {
            String response = sendChatMessage(messages, config);
            callback.onData(response);
            callback.onComplete();
        } catch (Exception e) {
            callback.onError(e.getMessage());
            throw e;
        }
    }

    @Override
    public boolean testConnection(AiConfig config) {
        try {
            List<ChatMessage> testMessages = new ArrayList<>();
            ChatMessage testMessage = new ChatMessage();
            testMessage.setRole("user");
            testMessage.setContent("Hello");
            testMessages.add(testMessage);

            sendChatMessage(testMessages, config);
            return true;

        } catch (Exception e) {
            logger.warn("DeepSeek连接测试失败: {}", e.getMessage());
            return false;
        }
    }

    @Override
    public List<String> getSupportedModels(AiConfig config) {
        List<String> models = new ArrayList<>();
        models.add("deepseek-chat");
        models.add("deepseek-coder");
        models.add("deepseek-math");
        return models;
    }

    @Override
    public int estimateTokenCount(String text) {
        if (text == null || text.isEmpty()) {
            return 0;
        }

        int chineseChars = 0;
        int otherChars = 0;

        for (char c : text.toCharArray()) {
            if (c >= 0x4e00 && c <= 0x9fff) {
                chineseChars++;
            } else {
                otherChars++;
            }
        }

        return (int) Math.ceil(chineseChars / 1.5) + (int) Math.ceil(otherChars / 4.0);
    }

    @Override
    public String getClientName() {
        return "DeepSeek";
    }

    /**
     * 构建DeepSeek API请求体
     */
    private Map<String, Object> buildRequestBody(List<ChatMessage> messages, AiConfig config) {
        Map<String, Object> requestBody = new HashMap<>();

        // 模型名称
        String model = config.getModelName() != null ? config.getModelName() : DEFAULT_MODEL;
        requestBody.put("model", model);

        // 消息列表
        List<Map<String, String>> apiMessages = new ArrayList<>();

        // 添加系统提示词
        if (config.getSystemPrompt() != null && !config.getSystemPrompt().isEmpty()) {
            Map<String, String> systemMessage = new HashMap<>();
            systemMessage.put("role", "system");
            systemMessage.put("content", config.getSystemPrompt());
            apiMessages.add(systemMessage);
        }

        // 添加聊天消息
        for (ChatMessage message : messages) {
            Map<String, String> apiMessage = new HashMap<>();
            apiMessage.put("role", message.getRole());
            apiMessage.put("content", message.getContent());
            apiMessages.add(apiMessage);
        }

        requestBody.put("messages", apiMessages);

        // 参数设置
        if (config.getTemperature() != null) {
            requestBody.put("temperature", config.getTemperature());
        }

        if (config.getMaxTokens() != null) {
            requestBody.put("max_tokens", config.getMaxTokens());
        }

        return requestBody;
    }
}
