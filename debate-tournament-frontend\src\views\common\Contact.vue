<template>
  <div class="legal-page contact-page">
    <div class="container">
      <div class="page-header">
        <h1 class="page-title">联系我们</h1>
        <p class="page-subtitle">我们随时准备为您提供帮助</p>
      </div>

      <div class="contact-container">
        <!-- 左侧表单 -->
        <div class="contact-form-section">
          <h2 class="form-title">发送消息</h2>

          <el-form
            ref="contactFormRef"
            :model="contactForm"
            :rules="rules"
            label-position="top"
            @submit.prevent="submitForm"
          >
            <el-form-item prop="name" label="您的姓名">
              <el-input v-model="contactForm.name" placeholder="请输入您的姓名" />
            </el-form-item>

            <el-form-item prop="email" label="电子邮箱">
              <el-input v-model="contactForm.email" placeholder="请输入您的电子邮箱" />
            </el-form-item>

            <el-form-item prop="type" label="联系类型">
              <el-select v-model="contactForm.type" placeholder="请选择联系类型" style="width: 100%">
                <el-option
                  v-for="item in contactTypes"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>

            <el-form-item prop="subject" label="主题">
              <el-input v-model="contactForm.subject" placeholder="请输入主题" />
            </el-form-item>

            <el-form-item prop="message" label="留言内容">
              <el-input
                v-model="contactForm.message"
                type="textarea"
                :rows="6"
                placeholder="请输入您的留言内容"
              />
            </el-form-item>

            <el-form-item>
              <el-button type="primary" :loading="loading" @click="submitForm">
                {{ loading ? '发送中...' : '发送留言' }}
              </el-button>
              <el-button @click="resetForm">重置</el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- 右侧联系信息 -->
        <div class="contact-info-section">
          <h2 class="info-title">联系方式</h2>

          <div class="info-cards">
            <div v-for="(info, index) in contactInfo" :key="index" class="info-card">
              <div class="info-icon">
                <el-icon><component :is="info.icon" /></el-icon>
              </div>
              <div class="info-content">
                <div class="info-label">{{ info.title }}</div>
                <div class="info-value">{{ info.content }}</div>
              </div>
            </div>
          </div>

          <div class="social-media">
            <h3 class="social-title">关注我们</h3>
            <div class="social-links">
              <a
                v-for="(social, index) in socialMedia"
                :key="index"
                :href="social.link"
                target="_blank"
                class="social-link"
                :title="social.name"
              >
                <el-icon><component :is="social.icon" /></el-icon>
              </a>
            </div>
          </div>

          <div class="common-questions">
            <h3 class="questions-title">常见问题</h3>
            <ul class="questions-list">
              <li v-for="(question, index) in commonQuestions" :key="index" class="question-item">
                <a :href="question.link" class="question-link">
                  <el-icon><ArrowRight /></el-icon>
                  <span>{{ question.question }}</span>
                </a>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script src="./js/Contact.js"></script>

<style lang="scss">
@use './scss/Legal.scss';
</style>
