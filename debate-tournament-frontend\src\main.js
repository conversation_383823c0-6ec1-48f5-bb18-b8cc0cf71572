import { createApp } from 'vue';
import { createPinia } from 'pinia';
import App from './App.vue';
import router from './router';
import pinia from './store';
import axios from 'axios';
import * as mdui from 'mdui';

// 引入ElementPlus
import ElementPlus from 'element-plus';
import 'element-plus/dist/index.css';
// 引入中文语言包
import zhCn from 'element-plus/dist/locale/zh-cn.mjs';
import * as ElementPlusIconsVue from '@element-plus/icons-vue';

// 设置API基础URL
axios.defaults.baseURL = import.meta.env.VITE_API_BASE_URL || '/api';

// 创建应用实例
const app = createApp(App);

// 注册全局属性
app.config.globalProperties.$mdui = mdui;

// 注册所有ElementPlus图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component);
}

// 使用插件
app.use(pinia);
app.use(router);
app.use(ElementPlus, {
  locale: zhCn,
  size: 'default',
  zIndex: 3000
});

// 挂载应用
app.mount('#app');
