package com.debate_ournament.users.repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.debate_ournament.users.entity.SecuritySettings;
import com.debate_ournament.users.entity.User;

/**
 * 安全设置数据访问接口
 */
@Repository
public interface SecuritySettingsRepository extends JpaRepository<SecuritySettings, Long> {

    /**
     * 根据用户查找安全设置
     */
    Optional<SecuritySettings> findByUser(User user);

    /**
     * 根据用户ID查找安全设置
     */
    Optional<SecuritySettings> findByUserId(Long userId);

    /**
     * 查找启用两步验证的用户设置
     */
    List<SecuritySettings> findByTwoFactorEnabledTrue();

    /**
     * 查找启用登录通知的用户设置
     */
    List<SecuritySettings> findByLoginNotificationTrue();

    /**
     * 查找密码过期的用户设置
     */
    @Query("SELECT ss FROM SecuritySettings ss WHERE ss.passwordLastChanged <= :expiryDate")
    List<SecuritySettings> findByPasswordExpired(@Param("expiryDate") LocalDateTime expiryDate);

    /**
     * 查找特定会话超时时间的用户设置
     */
    List<SecuritySettings> findBySessionTimeoutMinutes(Integer timeoutMinutes);

    /**
     * 查找启用IP限制的用户设置
     */
    List<SecuritySettings> findByIpRestrictionEnabledTrue();

    /**
     * 检查用户是否已有安全设置
     */
    boolean existsByUser(User user);

    /**
     * 删除用户的安全设置
     */
    void deleteByUser(User user);

    /**
     * 批量更新用户的两步验证设置
     */
    @Query("UPDATE SecuritySettings ss SET ss.twoFactorEnabled = :enabled WHERE ss.user.id IN :userIds")
    int updateTwoFactorEnabled(@Param("userIds") List<Long> userIds, @Param("enabled") boolean enabled);

    /**
     * 批量更新用户的登录通知设置
     */
    @Query("UPDATE SecuritySettings ss SET ss.loginNotification = :enabled WHERE ss.user.id IN :userIds")
    int updateLoginNotification(@Param("userIds") List<Long> userIds, @Param("enabled") boolean enabled);

    /**
     * 批量更新用户的会话超时时间
     */
    @Query("UPDATE SecuritySettings ss SET ss.sessionTimeoutMinutes = :timeoutMinutes WHERE ss.user.id IN :userIds")
    int updateSessionTimeout(@Param("userIds") List<Long> userIds, @Param("timeoutMinutes") Integer timeoutMinutes);

    /**
     * 查找需要更新密码的用户设置
     */
    @Query("SELECT ss FROM SecuritySettings ss WHERE ss.passwordLastChanged <= :cutoffDate AND ss.user.status = 'ACTIVE'")
    List<SecuritySettings> findUsersNeedingPasswordUpdate(@Param("cutoffDate") LocalDateTime cutoffDate);
}
