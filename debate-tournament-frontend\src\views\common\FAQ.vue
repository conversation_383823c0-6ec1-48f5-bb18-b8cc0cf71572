<template>
  <div class="legal-page faq-page">
    <div class="container">
      <div class="page-header">
        <h1 class="page-title">常见问题</h1>
        <p class="page-subtitle">找到您关心问题的答案</p>
      </div>

      <div class="legal-content">
        <div class="faq-list">
          <div v-for="category in faqCategories" :key="category.id" class="faq-category">
            <h2 class="category-title">{{ category.title }}</h2>

            <div class="faq-items">
              <div v-for="faq in category.faqs" :key="faq.id" class="faq-item">
                <div class="faq-question" @click="toggleFaq(faq.id)">
                  <div class="question-text">{{ faq.question }}</div>
                  <el-icon class="icon" :class="{ expanded: isFaqExpanded(faq.id) }">
                    <ArrowDown />
                  </el-icon>
                </div>

                <div class="faq-answer" :class="{ expanded: isFaqExpanded(faq.id) }">
                  <div class="answer-content">{{ faq.answer }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="legal-footer">
        <div class="footer-links">
          <a href="#" class="footer-link" @click.prevent="goToHelpCenter">查看帮助中心</a>
          <a href="#" class="footer-link" @click.prevent="goToContact">联系我们</a>
        </div>
      </div>
    </div>
  </div>
</template>

<script src="./js/FAQ.js"></script>

<style lang="scss">
@use './scss/Legal.scss';
</style>
