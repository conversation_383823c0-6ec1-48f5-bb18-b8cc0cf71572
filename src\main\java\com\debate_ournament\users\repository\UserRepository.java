package com.debate_ournament.users.repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.debate_ournament.users.entity.User;

/**
 * 用户数据访问接口
 */
@Repository
public interface UserRepository extends JpaRepository<User, Long> {

    /**
     * 根据用户名查找用户
     */
    Optional<User> findByUsername(String username);

    /**
     * 根据邮箱查找用户
     */
    Optional<User> findByEmail(String email);

    /**
     * 根据用户名或邮箱查找用户
     */
    @Query("SELECT u FROM User u WHERE u.username = :usernameOrEmail OR u.email = :usernameOrEmail")
    Optional<User> findByUsernameOrEmail(@Param("usernameOrEmail") String usernameOrEmail);

    /**
     * 检查用户名是否存在
     */
    boolean existsByUsername(String username);

    /**
     * 检查邮箱是否存在
     */
    boolean existsByEmail(String email);

    /**
     * 根据用户状态查找用户
     */
    List<User> findByStatus(User.UserStatus status);

    /**
     * 查找已激活且邮箱已验证的用户
     */
    @Query("SELECT u FROM User u WHERE u.status = :status AND u.emailVerified = true")
    List<User> findActiveVerifiedUsers(@Param("status") User.UserStatus status);

    /**
     * 查找锁定时间已过期的用户
     */
    @Query("SELECT u FROM User u WHERE u.lockedUntil IS NOT NULL AND u.lockedUntil < :currentTime")
    List<User> findUsersWithExpiredLocks(@Param("currentTime") LocalDateTime currentTime);

    /**
     * 查找指定时间之前创建的未验证用户
     */
    @Query("SELECT u FROM User u WHERE u.emailVerified = false AND u.createdAt < :cutoffTime")
    List<User> findUnverifiedUsersBefore(@Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * 查找在指定时间范围内登录的用户
     */
    @Query("SELECT u FROM User u WHERE u.lastLoginTime BETWEEN :startTime AND :endTime")
    List<User> findUsersLoggedInBetween(@Param("startTime") LocalDateTime startTime,
                                       @Param("endTime") LocalDateTime endTime);

    /**
     * 批量更新用户状态
     */
    @Modifying
    @Query("UPDATE User u SET u.status = :newStatus WHERE u.id IN :userIds")
    int updateUserStatus(@Param("userIds") List<Long> userIds, @Param("newStatus") User.UserStatus newStatus);

    /**
     * 解锁过期的用户账号
     */
    @Modifying
    @Query("UPDATE User u SET u.status = :activeStatus, u.lockedUntil = NULL " +
           "WHERE u.lockedUntil IS NOT NULL AND u.lockedUntil < :currentTime")
    int unlockExpiredAccounts(@Param("activeStatus") User.UserStatus activeStatus,
                             @Param("currentTime") LocalDateTime currentTime);

    /**
     * 重置登录尝试次数
     */
    @Modifying
    @Query("UPDATE User u SET u.loginAttempts = 0, u.lastLoginAttempt = NULL " +
           "WHERE u.loginAttempts > 0 AND u.lastLoginAttempt < :cutoffTime")
    int resetLoginAttempts(@Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * 删除长期未验证的用户账号
     */
    @Modifying
    @Query("DELETE FROM User u WHERE u.emailVerified = false AND u.createdAt < :cutoffTime")
    int deleteUnverifiedUsers(@Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * 统计活跃用户数量
     */
    @Query("SELECT COUNT(u) FROM User u WHERE u.status = :status AND u.emailVerified = true")
    long countActiveUsers(@Param("status") User.UserStatus status);

    /**
     * 根据IP地址查找最近登录的用户
     */
    @Query("SELECT u FROM User u WHERE u.lastLoginIp = :ip AND u.lastLoginTime > :cutoffTime")
    List<User> findRecentLoginsByIp(@Param("ip") String ip, @Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * 查找登录失败次数过多的用户
     */
    @Query("SELECT u FROM User u WHERE u.loginAttempts >= :maxAttempts")
    List<User> findUsersWithExcessiveFailedLogins(@Param("maxAttempts") int maxAttempts);
}
