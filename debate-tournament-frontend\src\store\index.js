import { createPinia } from 'pinia';
import { useEncryptionStore } from './encryption';

// 创建 pinia 实例
const pinia = createPinia();

// API请求工具，支持加密通信
export const apiService = {
  /**
   * 发送加密请求
   * @param {string} url - API地址
   * @param {Object} options - 请求选项
   * @param {boolean} options.useEncryption - 是否使用加密通信
   * @param {boolean} options.encryptSensitiveFields - 是否加密敏感字段
   * @returns {Promise<any>} - 解密后的响应数据
   */
  async request(url, options = {}) {
    const encryptionStore = useEncryptionStore(pinia);

    const defaultOptions = {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      },
      useEncryption: true,
      encryptSensitiveFields: true
    };

    const mergedOptions = { ...defaultOptions, ...options };
    const { useEncryption, encryptSensitiveFields, ...fetchOptions } = mergedOptions;

    // 如果不使用加密通信，直接发送请求
    if (!useEncryption) {
      const response = await fetch(url, fetchOptions);
      return await response.json();
    }

    // 使用加密通信
    return await encryptionStore.encryptedFetch(url, {
      ...fetchOptions,
      encryptSensitiveFields
    });
  },

  /**
   * 发送GET请求
   * @param {string} url - API地址
   * @param {Object} options - 请求选项
   * @returns {Promise<any>} - 响应数据
   */
  async get(url, options = {}) {
    return await this.request(url, {
      ...options,
      method: 'GET'
    });
  },

  /**
   * 发送POST请求
   * @param {string} url - API地址
   * @param {Object} data - 请求数据
   * @param {Object} options - 请求选项
   * @returns {Promise<any>} - 响应数据
   */
  async post(url, data, options = {}) {
    return await this.request(url, {
      ...options,
      method: 'POST',
      body: JSON.stringify(data)
    });
  },

  /**
   * 发送PUT请求
   * @param {string} url - API地址
   * @param {Object} data - 请求数据
   * @param {Object} options - 请求选项
   * @returns {Promise<any>} - 响应数据
   */
  async put(url, data, options = {}) {
    return await this.request(url, {
      ...options,
      method: 'PUT',
      body: JSON.stringify(data)
    });
  },

  /**
   * 发送DELETE请求
   * @param {string} url - API地址
   * @param {Object} options - 请求选项
   * @returns {Promise<any>} - 响应数据
   */
  async delete(url, options = {}) {
    return await this.request(url, {
      ...options,
      method: 'DELETE'
    });
  },

  /**
   * 测试加密通信
   * @returns {Promise<any>} - 测试结果
   */
  async testEncryption() {
    const testData = {
      message: "测试加密通信",
      timestamp: Date.now(),
      testValue: Math.random().toString(36).substring(2)
    };

    return await this.post('/api/auth/encryption/test', testData);
  }
};

// 导出 pinia 实例
export default pinia;
