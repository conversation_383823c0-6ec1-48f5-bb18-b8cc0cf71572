技术栈
前端：Vue 3 + SCSS + JavaScript (可选 TypeScript)
构建工具：Vite
UI 框架：Element Plus / Vuetify
后端：SpringBoot 3.2.0
数据库：MySQL + Redis
AI 服务：接入大语言模型 API
语音处理：Web Speech API / 讯飞开放平台
二、前端页面设计
1. 主要页面组成
首页设计
顶部导航栏：Logo、登录/注册、个人中心、辩论大厅
Banner 区域：展示当前热门辩论话题、参与/观看入口
辩论赛事卡片流：进行中/已结束辩论赛事，支持筛选和排序
功能区块：辩题推荐、排行榜、新手教程
底部信息栏：关于我们、联系方式、隐私政策
辩论赛场页面
辩论赛场设计示意图

辩论布局：左右双方辩手区域，中间裁判/主持区域
实时对话区：显示双方 AI 辩手的发言内容
控制面板：开始/暂停辩论、主题修改、节奏控制
观众互动区：实时评论、投票支持
语音控制：音量调节、声音选择、语速控制
个人中心页面
用户信息卡：头像、昵称、辩论积分、等级
历史记录：参与过的辩论、收藏的辩论
数据统计：胜率、热门论点、风格分析
设置选项：账户设置、通知管理、隐私控制
2. UI/UX 设计风格
整体风格：现代简约，强调专业感和智能感
配色方案：
主色：深蓝色 (#1a56db) - 代表理性与智慧
辅助色：橙色 (#f59e0b) - 代表活力与思想碰撞
背景色：浅灰 (#f9fafb) - 提供舒适阅读环境
排版：
标题：Roboto Bold 20-24px
正文：Open Sans 14-16px
按钮/交互元素：加粗处理，明确可点击区域
动效设计：
语音输出时的声波动画效果
辩论切换时的流畅过渡效果
投票/支持的实时反馈动画