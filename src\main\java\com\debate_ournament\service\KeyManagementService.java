package com.debate_ournament.service;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.security.KeyPair;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.UUID;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Service;

import com.debate_ournament.config.EncryptionConfig;
import com.debate_ournament.util.EncryptionUtil;

import jakarta.annotation.PostConstruct;

/**
 * 密钥管理服务
 *
 * 负责RSA密钥对的生成、存储、加载和管理
 * 支持密钥轮换和持久化存储
 *
 * <AUTHOR> Debate Tournament Platform
 * @version 1.0
 * @since 2024
 */
@Service
public class KeyManagementService {

    private static final Logger logger = LoggerFactory.getLogger(KeyManagementService.class);

    private final EncryptionConfig encryptionConfig;
    private final EncryptionUtil encryptionUtil;
    private final ResourceLoader resourceLoader;

    // 当前密钥对缓存
    private volatile KeyPair currentKeyPair;
    private volatile String currentKeyId;
    private volatile LocalDateTime keyGeneratedTime;

    @Autowired
    public KeyManagementService(EncryptionConfig encryptionConfig,
                               EncryptionUtil encryptionUtil,
                               ResourceLoader resourceLoader) {
        this.encryptionConfig = encryptionConfig;
        this.encryptionUtil = encryptionUtil;
        this.resourceLoader = resourceLoader;
    }

    /**
     * 初始化密钥管理服务
     * 在服务启动时自动执行
     */
    @PostConstruct
    public void initializeKeys() {
        try {
            logger.info("开始初始化密钥管理服务");

            // 确保密钥存储目录存在
            ensureKeyDirectoryExists();

            // 尝试加载现有密钥对
            if (loadExistingKeys()) {
                logger.info("成功加载现有密钥对，密钥ID: {}", currentKeyId);
            } else if (encryptionConfig.isAutoGenerateOnStartup()) {
                // 生成新的密钥对
                generateAndSaveKeyPair();
                logger.info("成功生成新密钥对，密钥ID: {}", currentKeyId);
            } else {
                logger.warn("未找到现有密钥且未启用自动生成，请手动生成密钥对");
            }

        } catch (Exception e) {
            logger.error("初始化密钥管理服务失败", e);
            throw new RuntimeException("密钥管理服务初始化失败", e);
        }
    }

    /**
     * 获取当前公钥
     *
     * @return 当前的RSA公钥
     */
    public PublicKey getCurrentPublicKey() {
        if (currentKeyPair == null) {
            throw new IllegalStateException("密钥对未初始化");
        }
        return currentKeyPair.getPublic();
    }

    /**
     * 获取当前私钥
     *
     * @return 当前的RSA私钥
     */
    public PrivateKey getCurrentPrivateKey() {
        if (currentKeyPair == null) {
            throw new IllegalStateException("密钥对未初始化");
        }
        return currentKeyPair.getPrivate();
    }

    /**
     * 获取当前密钥ID
     *
     * @return 当前密钥的唯一标识符
     */
    public String getCurrentKeyId() {
        return currentKeyId;
    }

    /**
     * 获取密钥生成时间
     *
     * @return 密钥生成的时间
     */
    public LocalDateTime getKeyGeneratedTime() {
        return keyGeneratedTime;
    }

    /**
     * 获取公钥的Base64编码字符串
     *
     * @return Base64编码的公钥
     */
    public String getCurrentPublicKeyBase64() {
        return encryptionUtil.publicKeyToBase64(getCurrentPublicKey());
    }

    /**
     * 生成新的密钥对
     * 手动触发密钥生成和轮换
     *
     * @return 新生成的密钥ID
     */
    public String generateNewKeyPair() {
        try {
            logger.info("开始生成新的密钥对");

            // 备份旧密钥（如果存在）
            if (currentKeyPair != null) {
                backupCurrentKeys();
            }

            // 生成并保存新密钥对
            generateAndSaveKeyPair();

            logger.info("成功生成新密钥对，密钥ID: {}", currentKeyId);
            return currentKeyId;

        } catch (Exception e) {
            logger.error("生成新密钥对失败", e);
            throw new RuntimeException("生成新密钥对失败", e);
        }
    }

    /**
     * 检查密钥是否需要轮换
     *
     * @return 是否需要轮换密钥
     */
    public boolean isKeyRotationNeeded() {
        if (!encryptionConfig.isEnableKeyRotation() || keyGeneratedTime == null) {
            return false;
        }

        LocalDateTime rotationTime = keyGeneratedTime.plusHours(
                encryptionConfig.getKeyRotationIntervalHours());

        return LocalDateTime.now().isAfter(rotationTime);
    }

    /**
     * 获取密钥存储路径
     *
     * @return 密钥存储的完整路径
     */
    public Path getKeyStorePath() {
        try {
            // 首先尝试从classpath获取
            ClassPathResource resource = new ClassPathResource(encryptionConfig.getKeyStorePath());
            if (resource.exists()) {
                return Paths.get(resource.getURI());
            }

            // 如果classpath中不存在，则使用resources目录
            String resourcesPath = "src/main/resources/" + encryptionConfig.getKeyStorePath();
            return Paths.get(resourcesPath);

        } catch (Exception e) {
            logger.warn("无法获取classpath路径，使用默认resources路径");
            return Paths.get("src/main/resources/" + encryptionConfig.getKeyStorePath());
        }
    }

    // ============ 私有方法 ============

    /**
     * 确保密钥存储目录存在
     */
    private void ensureKeyDirectoryExists() throws IOException {
        Path keyDir = getKeyStorePath();
        if (!Files.exists(keyDir)) {
            Files.createDirectories(keyDir);
            logger.info("创建密钥存储目录: {}", keyDir.toAbsolutePath());
        }
    }

    /**
     * 加载现有的密钥对
     *
     * @return 是否成功加载现有密钥
     */
    private boolean loadExistingKeys() {
        try {
            Path keyDir = getKeyStorePath();
            Path publicKeyFile = keyDir.resolve(encryptionConfig.getPublicKeyFileName());
            Path privateKeyFile = keyDir.resolve(encryptionConfig.getPrivateKeyFileName());
            Path keyIdFile = keyDir.resolve(encryptionConfig.getKeyIdFileName());

            // 检查所有必需的文件是否存在
            if (!Files.exists(publicKeyFile) || !Files.exists(privateKeyFile) || !Files.exists(keyIdFile)) {
                logger.debug("密钥文件不完整，无法加载现有密钥");
                return false;
            }

            // 读取密钥ID
            currentKeyId = Files.readString(keyIdFile).trim();

            // 读取并解析公钥
            String publicKeyContent = Files.readString(publicKeyFile)
                    .replace("-----BEGIN PUBLIC KEY-----", "")
                    .replace("-----END PUBLIC KEY-----", "")
                    .replaceAll("\\s", "");

            // 读取并解析私钥
            String privateKeyContent = Files.readString(privateKeyFile)
                    .replace("-----BEGIN PRIVATE KEY-----", "")
                    .replace("-----END PRIVATE KEY-----", "")
                    .replaceAll("\\s", "");

            // 恢复密钥对
            PublicKey publicKey = encryptionUtil.base64ToPublicKey(publicKeyContent);
            PrivateKey privateKey = encryptionUtil.base64ToPrivateKey(privateKeyContent);
            currentKeyPair = new KeyPair(publicKey, privateKey);

            // 设置密钥生成时间（从文件修改时间推断）
            keyGeneratedTime = LocalDateTime.ofInstant(
                    Files.getLastModifiedTime(keyIdFile).toInstant(),
                    java.time.ZoneId.systemDefault());

            logger.debug("成功加载现有密钥对");
            return true;

        } catch (Exception e) {
            logger.warn("加载现有密钥失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 生成并保存新的密钥对
     */
    private void generateAndSaveKeyPair() throws IOException {
        // 生成新的密钥对
        currentKeyPair = encryptionUtil.generateRSAKeyPair();
        currentKeyId = generateKeyId();
        keyGeneratedTime = LocalDateTime.now();

        // 保存密钥到文件
        saveKeyPairToFiles();
    }

    /**
     * 保存密钥对到文件
     */
    private void saveKeyPairToFiles() throws IOException {
        Path keyDir = getKeyStorePath();

        // 保存公钥
        String publicKeyPem = formatKeyAsPem(
                encryptionUtil.publicKeyToBase64(currentKeyPair.getPublic()),
                "PUBLIC KEY");
        Path publicKeyFile = keyDir.resolve(encryptionConfig.getPublicKeyFileName());
        Files.writeString(publicKeyFile, publicKeyPem, StandardOpenOption.CREATE, StandardOpenOption.TRUNCATE_EXISTING);

        // 保存私钥
        String privateKeyPem = formatKeyAsPem(
                encryptionUtil.privateKeyToBase64(currentKeyPair.getPrivate()),
                "PRIVATE KEY");
        Path privateKeyFile = keyDir.resolve(encryptionConfig.getPrivateKeyFileName());
        Files.writeString(privateKeyFile, privateKeyPem, StandardOpenOption.CREATE, StandardOpenOption.TRUNCATE_EXISTING);

        // 保存密钥ID
        Path keyIdFile = keyDir.resolve(encryptionConfig.getKeyIdFileName());
        Files.writeString(keyIdFile, currentKeyId, StandardOpenOption.CREATE, StandardOpenOption.TRUNCATE_EXISTING);

        logger.info("密钥对已保存到文件系统，密钥ID: {}", currentKeyId);
    }

    /**
     * 备份当前密钥
     */
    private void backupCurrentKeys() {
        try {
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
            Path keyDir = getKeyStorePath();
            Path backupDir = keyDir.resolve("backup_" + timestamp);
            Files.createDirectories(backupDir);

            // 备份现有密钥文件
            Files.copy(keyDir.resolve(encryptionConfig.getPublicKeyFileName()),
                      backupDir.resolve(encryptionConfig.getPublicKeyFileName()));
            Files.copy(keyDir.resolve(encryptionConfig.getPrivateKeyFileName()),
                      backupDir.resolve(encryptionConfig.getPrivateKeyFileName()));
            Files.copy(keyDir.resolve(encryptionConfig.getKeyIdFileName()),
                      backupDir.resolve(encryptionConfig.getKeyIdFileName()));

            logger.info("旧密钥已备份到: {}", backupDir.toAbsolutePath());

        } catch (Exception e) {
            logger.warn("备份旧密钥失败: {}", e.getMessage());
        }
    }

    /**
     * 生成唯一的密钥ID
     *
     * @return 密钥ID
     */
    private String generateKeyId() {
        return "key_" + System.currentTimeMillis() + "_" + UUID.randomUUID().toString().substring(0, 8);
    }

    /**
     * 将Base64密钥格式化为PEM格式
     *
     * @param base64Key Base64编码的密钥
     * @param keyType 密钥类型（PUBLIC KEY 或 PRIVATE KEY）
     * @return PEM格式的密钥字符串
     */
    private String formatKeyAsPem(String base64Key, String keyType) {
        StringBuilder pem = new StringBuilder();
        pem.append("-----BEGIN ").append(keyType).append("-----\n");

        // 每64个字符换行
        for (int i = 0; i < base64Key.length(); i += 64) {
            int end = Math.min(i + 64, base64Key.length());
            pem.append(base64Key, i, end).append("\n");
        }

        pem.append("-----END ").append(keyType).append("-----\n");
        return pem.toString();
    }
}
