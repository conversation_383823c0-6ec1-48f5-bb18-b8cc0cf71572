<template>
  <div class="custom-ai-model">
    <div class="container">
      <h1 class="page-title">{{ isEditing ? '编辑AI模型' : '添加自定义AI模型' }}</h1>

      <div class="form-container">
        <form @submit.prevent="handleSubmit" class="custom-model-form">
          <div class="form-section">
            <h2 class="section-title">基本信息</h2>

            <div class="form-group">
              <label class="form-label" for="modelName">模型名称</label>
              <input type="text" id="modelName" class="form-control" v-model="modelData.name"
                :class="{ 'is-invalid': errors.name }" required />
              <span v-if="errors.name" class="form-text text-error">{{ errors.name }}</span>
            </div>

            <div class="form-group">
              <label class="form-label" for="modelProvider">提供商</label>
              <input type="text" id="modelProvider" class="form-control" v-model="modelData.provider"
                :class="{ 'is-invalid': errors.provider }" required />
              <span v-if="errors.provider" class="form-text text-error">{{ errors.provider }}</span>
            </div>

            <div class="form-group">
              <label class="form-label" for="modelDescription">描述</label>
              <textarea id="modelDescription" class="form-control" v-model="modelData.description"
                :class="{ 'is-invalid': errors.description }" rows="4" required></textarea>
              <span v-if="errors.description" class="form-text text-error">{{ errors.description }}</span>
            </div>
          </div>

          <div class="form-section">
            <h2 class="section-title">API配置</h2>

            <div class="form-group">
              <label class="form-label" for="modelType">模型类型</label>
              <select id="modelType" class="form-select" v-model="modelData.type" :class="{ 'is-invalid': errors.type }"
                required>
                <option value="">请选择模型类型</option>
                <option value="openai">OpenAI</option>
                <option value="azure">Azure OpenAI</option>
                <option value="anthropic">Anthropic</option>
                <option value="baidu">百度文心</option>
                <option value="xunfei">讯飞星火</option>
                <option value="custom">自定义API</option>
              </select>
              <span v-if="errors.type" class="form-text text-error">{{ errors.type }}</span>
            </div>

            <div class="form-group">
              <label class="form-label" for="modelEndpoint">API端点</label>
              <input type="text" id="modelEndpoint" class="form-control" v-model="modelData.endpoint"
                :class="{ 'is-invalid': errors.endpoint }" required />
              <span v-if="errors.endpoint" class="form-text text-error">{{ errors.endpoint }}</span>
            </div>

            <div class="form-group">
              <label class="form-label" for="modelApiKey">API密钥</label>
              <div class="api-key-input">
                <input :type="showApiKey ? 'text' : 'password'" id="modelApiKey" class="form-control"
                  v-model="modelData.apiKey" :class="{ 'is-invalid': errors.apiKey }"
                  :placeholder="isEditing ? '保持不变请留空' : '输入API密钥'" :required="!isEditing" />
                <button type="button" class="toggle-password-btn" @click="showApiKey = !showApiKey">
                  {{ showApiKey ? '隐藏' : '显示' }}
                </button>
              </div>
              <span v-if="errors.apiKey" class="form-text text-error">{{ errors.apiKey }}</span>
            </div>

            <div class="form-group">
              <label class="form-label" for="modelName">模型名称标识符</label>
              <input type="text" id="modelIdentifier" class="form-control" v-model="modelData.modelIdentifier"
                :class="{ 'is-invalid': errors.modelIdentifier }" placeholder="例如：gpt-4-turbo, claude-3-opus"
                required />
              <span v-if="errors.modelIdentifier" class="form-text text-error">{{ errors.modelIdentifier }}</span>
            </div>

            <div class="form-group">
              <label class="form-label">高级参数</label>
              <div class="advanced-params">
                <div class="param-row">
                  <label for="temperature">温度</label>
                  <div class="param-slider">
                    <input type="range" id="temperature" min="0" max="2" step="0.1"
                      v-model.number="modelData.parameters.temperature" />
                    <span class="param-value">{{ modelData.parameters.temperature }}</span>
                  </div>
                </div>

                <div class="param-row">
                  <label for="maxTokens">最大Token数</label>
                  <input type="number" id="maxTokens" class="form-control form-control--sm"
                    v-model.number="modelData.parameters.maxTokens" min="1" />
                </div>

                <div class="param-row">
                  <label for="topP">Top P</label>
                  <div class="param-slider">
                    <input type="range" id="topP" min="0" max="1" step="0.05"
                      v-model.number="modelData.parameters.topP" />
                    <span class="param-value">{{ modelData.parameters.topP }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="form-section">
            <h2 class="section-title">能力评估</h2>

            <div class="capability-slider">
              <label for="languageAbility">语言能力</label>
              <div class="slider-container">
                <input type="range" id="languageAbility" min="0" max="100" v-model.number="modelData.languageAbility" />
                <span class="slider-value">{{ modelData.languageAbility }}%</span>
              </div>
            </div>

            <div class="capability-slider">
              <label for="logicalReasoning">逻辑推理</label>
              <div class="slider-container">
                <input type="range" id="logicalReasoning" min="0" max="100"
                  v-model.number="modelData.logicalReasoning" />
                <span class="slider-value">{{ modelData.logicalReasoning }}%</span>
              </div>
            </div>

            <div class="capability-slider">
              <label for="debateSkill">辩论技巧</label>
              <div class="slider-container">
                <input type="range" id="debateSkill" min="0" max="100" v-model.number="modelData.debateSkill" />
                <span class="slider-value">{{ modelData.debateSkill }}%</span>
              </div>
            </div>

            <div class="capability-slider">
              <label for="knowledgeBreadth">知识广度</label>
              <div class="slider-container">
                <input type="range" id="knowledgeBreadth" min="0" max="100"
                  v-model.number="modelData.knowledgeBreadth" />
                <span class="slider-value">{{ modelData.knowledgeBreadth }}%</span>
              </div>
            </div>

            <div class="capability-slider">
              <label for="responseSpeed">回应速度</label>
              <div class="slider-container">
                <input type="range" id="responseSpeed" min="0" max="100" v-model.number="modelData.responseSpeed" />
                <span class="slider-value">{{ modelData.responseSpeed }}%</span>
              </div>
            </div>
          </div>

          <div class="form-section">
            <h2 class="section-title">模型特点</h2>

            <div class="features-container">
              <div v-for="(feature, index) in modelData.features" :key="index" class="feature-item">
                <input type="text" class="form-control" v-model="modelData.features[index]" placeholder="输入模型特点" />
                <button type="button" class="btn btn--icon btn--danger" @click="removeFeature(index)">
                  ×
                </button>
              </div>

              <button type="button" class="btn btn--outline btn--sm" @click="addFeature">
                添加特点
              </button>
            </div>
          </div>

          <div class="form-section">
            <h2 class="section-title">测试模型</h2>

            <div class="test-model-container">
              <div class="test-input">
                <textarea class="form-control" v-model="testPrompt" placeholder="输入测试提示词..." rows="3"></textarea>
                <button type="button" class="btn btn--primary" @click="testModel" :disabled="testing || !canTest">
                  {{ testing ? '测试中...' : '测试' }}
                </button>
              </div>

              <div v-if="testResult" class="test-result">
                <h3>测试结果</h3>
                <div class="result-content">
                  {{ testResult }}
                </div>
              </div>
            </div>
          </div>

          <div class="form-actions">
            <button type="button" class="btn btn--outline" @click="$router.push('/ai-model')">
              取消
            </button>
            <button type="submit" class="btn btn--primary" :disabled="loading">
              {{ loading ? '保存中...' : (isEditing ? '更新模型' : '创建模型') }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script src="./js/CustomAIModel.js"></script>

<style lang="scss">
@use './scss/CustomAIModel.scss';
</style>
