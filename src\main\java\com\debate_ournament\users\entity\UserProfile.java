package com.debate_ournament.users.entity;

import java.time.LocalDate;
import java.time.LocalDateTime;

import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;

/**
 * 用户资料实体类
 * 对应数据库表：user_profiles
 */
@Entity
@Table(name = "user_profiles")
@EntityListeners(AuditingEntityListener.class)
public class UserProfile {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false, columnDefinition = "bigint COMMENT '用户资料主键ID'")
    private Long id;

    @OneToOne
    @JoinColumn(name = "user_id", nullable = false, unique = true)
    private User user;

    @Size(max = 50, message = "昵称长度不能超过50个字符")
    @Column(name = "display_name", length = 50,
            columnDefinition = "varchar(50) COMMENT '用户显示昵称'")
    private String displayName;

    @Column(name = "avatar_url", length = 500,
            columnDefinition = "varchar(500) COMMENT '用户头像URL'")
    private String avatarUrl;

    @Size(max = 200, message = "个人简介长度不能超过200个字符")
    @Column(name = "bio", length = 200,
            columnDefinition = "varchar(200) COMMENT '个人简介'")
    private String bio;

    @Column(name = "birth_date",
            columnDefinition = "date COMMENT '出生日期'")
    private LocalDate birthDate;

    @Enumerated(EnumType.STRING)
    @Column(name = "gender", length = 10,
            columnDefinition = "varchar(10) COMMENT '性别（MALE=男性，FEMALE=女性，OTHER=其他，PREFER_NOT_TO_SAY=不愿透露）'")
    private Gender gender;

    @Size(max = 100, message = "所在地长度不能超过100个字符")
    @Column(name = "location", length = 100,
            columnDefinition = "varchar(100) COMMENT '所在地'")
    private String location;

    @Size(max = 100, message = "学校/机构名称长度不能超过100个字符")
    @Column(name = "school_or_organization", length = 100,
            columnDefinition = "varchar(100) COMMENT '学校或机构名称'")
    private String schoolOrOrganization;

    @Size(max = 100, message = "专业/领域长度不能超过100个字符")
    @Column(name = "major_or_field", length = 100,
            columnDefinition = "varchar(100) COMMENT '专业或研究领域'")
    private String majorOrField;

    @Column(name = "is_profile_public", nullable = false,
            columnDefinition = "boolean DEFAULT true COMMENT '资料是否公开（true=公开，false=私密）'")
    private Boolean isProfilePublic = true;

    @Column(name = "show_email", nullable = false,
            columnDefinition = "boolean DEFAULT false COMMENT '是否显示邮箱（true=显示，false=隐藏）'")
    private Boolean showEmail = false;

    @Column(name = "show_real_name", nullable = false,
            columnDefinition = "boolean DEFAULT false COMMENT '是否显示真实姓名（true=显示，false=隐藏）'")
    private Boolean showRealName = false;

    @Column(name = "avatar_file_name", length = 255,
            columnDefinition = "varchar(255) COMMENT '头像文件名'")
    private String avatarFileName;

    @Column(name = "avatar_file_size",
            columnDefinition = "bigint COMMENT '头像文件大小（字节）'")
    private Long avatarFileSize;

    @Column(name = "avatar_content_type", length = 100,
            columnDefinition = "varchar(100) COMMENT '头像文件MIME类型'")
    private String avatarContentType;

    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false,
            columnDefinition = "datetime DEFAULT CURRENT_TIMESTAMP COMMENT '资料创建时间'")
    private LocalDateTime createdAt;

    @LastModifiedDate
    @Column(name = "updated_at", nullable = false,
            columnDefinition = "datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '资料最后更新时间'")
    private LocalDateTime updatedAt;

    /**
     * 性别枚举
     */
    public enum Gender {
        MALE("男性"),
        FEMALE("女性"),
        OTHER("其他"),
        PREFER_NOT_TO_SAY("不愿透露");

        private final String description;

        Gender(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    // 构造函数
    public UserProfile() {}

    public UserProfile(User user) {
        this.user = user;
        this.isProfilePublic = true;
        this.showEmail = false;
        this.showRealName = false;
    }

    // Getter 和 Setter 方法
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public String getDisplayName() {
        return displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    public String getAvatarUrl() {
        return avatarUrl;
    }

    public void setAvatarUrl(String avatarUrl) {
        this.avatarUrl = avatarUrl;
    }

    public String getBio() {
        return bio;
    }

    public void setBio(String bio) {
        this.bio = bio;
    }

    public LocalDate getBirthDate() {
        return birthDate;
    }

    public void setBirthDate(LocalDate birthDate) {
        this.birthDate = birthDate;
    }

    public Gender getGender() {
        return gender;
    }

    public void setGender(Gender gender) {
        this.gender = gender;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getSchoolOrOrganization() {
        return schoolOrOrganization;
    }

    public void setSchoolOrOrganization(String schoolOrOrganization) {
        this.schoolOrOrganization = schoolOrOrganization;
    }

    public String getMajorOrField() {
        return majorOrField;
    }

    public void setMajorOrField(String majorOrField) {
        this.majorOrField = majorOrField;
    }

    public Boolean getIsProfilePublic() {
        return isProfilePublic;
    }

    public void setIsProfilePublic(Boolean isProfilePublic) {
        this.isProfilePublic = isProfilePublic;
    }

    public Boolean getShowEmail() {
        return showEmail;
    }

    public void setShowEmail(Boolean showEmail) {
        this.showEmail = showEmail;
    }

    public Boolean getShowRealName() {
        return showRealName;
    }

    public void setShowRealName(Boolean showRealName) {
        this.showRealName = showRealName;
    }

    public String getAvatarFileName() {
        return avatarFileName;
    }

    public void setAvatarFileName(String avatarFileName) {
        this.avatarFileName = avatarFileName;
    }

    public Long getAvatarFileSize() {
        return avatarFileSize;
    }

    public void setAvatarFileSize(Long avatarFileSize) {
        this.avatarFileSize = avatarFileSize;
    }

    public String getAvatarContentType() {
        return avatarContentType;
    }

    public void setAvatarContentType(String avatarContentType) {
        this.avatarContentType = avatarContentType;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    // 业务方法

    /**
     * 获取显示名称（优先显示昵称，其次用户名）
     * @return 显示名称
     */
    public String getEffectiveDisplayName() {
        if (displayName != null && !displayName.trim().isEmpty()) {
            return displayName;
        }
        return user != null ? user.getUsername() : null;
    }

    /**
     * 检查是否有头像
     * @return 是否有头像
     */
    public boolean hasAvatar() {
        return avatarUrl != null && !avatarUrl.trim().isEmpty();
    }

    /**
     * 获取默认头像URL
     * @return 默认头像URL
     */
    public String getDefaultAvatarUrl() {
        return "/images/default-avatar.png";
    }

    /**
     * 获取有效的头像URL（如果没有自定义头像则返回默认头像）
     * @return 头像URL
     */
    public String getEffectiveAvatarUrl() {
        return hasAvatar() ? avatarUrl : getDefaultAvatarUrl();
    }

    /**
     * 更新头像信息
     * @param url 头像URL
     * @param fileName 文件名
     * @param fileSize 文件大小
     * @param contentType 内容类型
     */
    public void updateAvatar(String url, String fileName, Long fileSize, String contentType) {
        this.avatarUrl = url;
        this.avatarFileName = fileName;
        this.avatarFileSize = fileSize;
        this.avatarContentType = contentType;
    }

    /**
     * 清除头像信息
     */
    public void clearAvatar() {
        this.avatarUrl = null;
        this.avatarFileName = null;
        this.avatarFileSize = null;
        this.avatarContentType = null;
    }

    @Override
    public String toString() {
        return "UserProfile{" +
                "id=" + id +
                ", displayName='" + displayName + '\'' +
                ", bio='" + bio + '\'' +
                ", location='" + location + '\'' +
                ", schoolOrOrganization='" + schoolOrOrganization + '\'' +
                ", majorOrField='" + majorOrField + '\'' +
                ", isProfilePublic=" + isProfilePublic +
                ", hasAvatar=" + hasAvatar() +
                '}';
    }
}
