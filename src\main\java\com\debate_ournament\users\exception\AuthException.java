package com.debate_ournament.users.exception;

/**
 * 认证相关异常的基类
 */
public class AuthException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    public AuthException(String message) {
        super(message);
    }

    public AuthException(String message, Throwable cause) {
        super(message, cause);
    }
}

/**
 * 用户未找到异常
 */
class UserNotFoundException extends AuthException {
    private static final long serialVersionUID = 1L;

    public UserNotFoundException(String message) {
        super(message);
    }
}

/**
 * 用户已存在异常
 */
class UserAlreadyExistsException extends AuthException {
    private static final long serialVersionUID = 1L;

    public UserAlreadyExistsException(String message) {
        super(message);
    }
}

/**
 * 无效凭证异常
 */
class InvalidCredentialsException extends AuthException {
    private static final long serialVersionUID = 1L;

    public InvalidCredentialsException(String message) {
        super(message);
    }
}

/**
 * 账户锁定异常
 */
class AccountLockedException extends AuthException {
    private static final long serialVersionUID = 1L;

    public AccountLockedException(String message) {
        super(message);
    }
}

/**
 * 账户禁用异常
 */
class AccountDisabledException extends AuthException {
    private static final long serialVersionUID = 1L;

    public AccountDisabledException(String message) {
        super(message);
    }
}

/**
 * 邮箱未验证异常
 */
class EmailNotVerifiedException extends AuthException {
    private static final long serialVersionUID = 1L;

    public EmailNotVerifiedException(String message) {
        super(message);
    }
}

/**
 * 设备未授权异常
 */
class UnauthorizedDeviceException extends AuthException {
    private static final long serialVersionUID = 1L;

    public UnauthorizedDeviceException(String message) {
        super(message);
    }
}

/**
 * 两步验证失败异常
 */
class TwoFactorAuthenticationFailedException extends AuthException {
    private static final long serialVersionUID = 1L;

    public TwoFactorAuthenticationFailedException(String message) {
        super(message);
    }
}

/**
 * IP限制异常
 */
class IpRestrictedException extends AuthException {
    private static final long serialVersionUID = 1L;

    public IpRestrictedException(String message) {
        super(message);
    }
}

/**
 * 密码过期异常
 */
class PasswordExpiredException extends AuthException {
    private static final long serialVersionUID = 1L;

    public PasswordExpiredException(String message) {
        super(message);
    }
}

/**
 * 会话超时异常
 */
class SessionTimeoutException extends AuthException {
    private static final long serialVersionUID = 1L;

    public SessionTimeoutException(String message) {
        super(message);
    }
}
