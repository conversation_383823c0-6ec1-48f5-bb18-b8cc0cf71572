<template>
  <div class="auth-container">
    <div class="auth-card">
      <div class="auth-header">
        <h1 class="auth-title">找回密码</h1>
        <p class="auth-subtitle">我们将向您的邮箱发送重置链接</p>
      </div>

      <div class="auth-body">
        <div v-if="errors.general" class="auth-alert auth-alert--error">
          {{ errors.general }}
        </div>

        <div v-if="success" class="auth-alert auth-alert--success">
          {{ success }}
        </div>

        <form v-if="!success" class="auth-form" @submit.prevent="handleSubmit">
          <!-- 邮箱输入 -->
          <div class="form-group">
            <label class="form-label" for="email">邮箱</label>
            <input id="email" v-model="formData.email" type="email" class="form-control"
              :class="{ 'is-invalid': errors.email }" placeholder="请输入您注册时使用的邮箱">
            <span v-if="errors.email" class="form-text text-error">{{ errors.email }}</span>
          </div>

          <!-- 验证码输入 -->
          <div class="form-group">
            <label class="form-label" for="captcha">验证码</label>
            <div class="captcha-container">
              <input id="captcha" v-model="formData.captcha" type="text" class="form-control captcha-input"
                :class="{ 'is-invalid': errors.captcha }" placeholder="请输入验证码" maxlength="4">
              <img :src="captchaUrl" alt="验证码" class="captcha-image" @click="refreshCaptcha">
            </div>
            <span v-if="errors.captcha" class="form-text text-error">{{ errors.captcha }}</span>
          </div>

          <!-- 提交按钮 -->
          <button type="submit" class="btn btn--primary btn--block" :disabled="loading">
            {{ loading ? '提交中...' : '发送重置链接' }}
          </button>
        </form>
      </div>

      <div class="auth-footer">
        <div class="auth-links">
          <router-link to="/login" class="auth-link">返回登录</router-link>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ForgotPasswordJS from './js/ForgotPassword.js';
export default ForgotPasswordJS;
</script>

<style lang="scss">
@use './scss/Auth.scss';
</style>
