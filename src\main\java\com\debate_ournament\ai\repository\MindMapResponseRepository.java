package com.debate_ournament.ai.repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.debate_ournament.ai.entity.MindMapResponse;

/**
 * 思维导图响应数据访问接口
 */
@Repository
public interface MindMapResponseRepository extends JpaRepository<MindMapResponse, Long> {

    /**
     * 根据会话ID查找思维导图
     */
    List<MindMapResponse> findBySessionIdOrderByCreatedAtDesc(String sessionId);

    /**
     * 根据用户ID查找思维导图（分页）
     */
    Page<MindMapResponse> findByUserIdOrderByCreatedAtDesc(Long userId, Pageable pageable);

    /**
     * 根据用户ID和会话ID查找思维导图
     */
    List<MindMapResponse> findByUserIdAndSessionIdOrderByCreatedAtDesc(Long userId, String sessionId);

    /**
     * 根据用户ID查找所有思维导图
     */
    List<MindMapResponse> findByUserIdOrderByCreatedAtDesc(Long userId);

    /**
     * 根据状态查找思维导图
     */
    List<MindMapResponse> findByStatusOrderByCreatedAtDesc(MindMapResponse.ResponseStatus status);

    /**
     * 根据思维导图类型查找
     */
    List<MindMapResponse> findByMindmapTypeOrderByCreatedAtDesc(MindMapResponse.MindMapType mindmapType);

    /**
     * 查找公开的思维导图
     */
    @Query("SELECT m FROM MindMapResponse m WHERE m.isPublic = true AND m.status = 'SUCCESS' ORDER BY m.createdAt DESC")
    List<MindMapResponse> findPublicMindMaps();

    /**
     * 查找公开的思维导图（分页）
     */
    @Query("SELECT m FROM MindMapResponse m WHERE m.isPublic = true AND m.status = 'SUCCESS' ORDER BY m.createdAt DESC")
    Page<MindMapResponse> findPublicMindMaps(Pageable pageable);

    /**
     * 根据主题领域查找思维导图
     */
    List<MindMapResponse> findByTopicDomainContainingIgnoreCaseOrderByCreatedAtDesc(String topicDomain);

    /**
     * 根据标题搜索思维导图
     */
    @Query("SELECT m FROM MindMapResponse m WHERE m.userId = :userId AND LOWER(m.title) LIKE LOWER(CONCAT('%', :title, '%')) ORDER BY m.createdAt DESC")
    List<MindMapResponse> findByUserIdAndTitleContaining(@Param("userId") Long userId, @Param("title") String title);

    /**
     * 根据标签搜索思维导图
     */
    @Query("SELECT m FROM MindMapResponse m WHERE m.userId = :userId AND LOWER(m.tags) LIKE LOWER(CONCAT('%', :tag, '%')) ORDER BY m.createdAt DESC")
    List<MindMapResponse> findByUserIdAndTagsContaining(@Param("userId") Long userId, @Param("tag") String tag);

    /**
     * 统计用户的思维导图数量
     */
    long countByUserId(Long userId);

    /**
     * 统计用户成功生成的思维导图数量
     */
    long countByUserIdAndStatus(Long userId, MindMapResponse.ResponseStatus status);

    /**
     * 查找指定时间范围内创建的思维导图
     */
    List<MindMapResponse> findByCreatedAtBetweenOrderByCreatedAtDesc(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 根据AI提供商统计思维导图数量
     */
    @Query("SELECT m.aiProvider, COUNT(m) FROM MindMapResponse m WHERE m.aiProvider IS NOT NULL GROUP BY m.aiProvider")
    List<Object[]> countMindMapsByAiProvider();

    /**
     * 查找热门思维导图（根据浏览量）
     */
    @Query("SELECT m FROM MindMapResponse m WHERE m.isPublic = true AND m.status = 'SUCCESS' ORDER BY m.viewCount DESC, m.likeCount DESC")
    List<MindMapResponse> findPopularMindMaps(Pageable pageable);

    /**
     * 查找用户收藏的思维导图（根据点赞）
     */
    @Query("SELECT m FROM MindMapResponse m WHERE m.userId = :userId AND m.likeCount > 0 ORDER BY m.likeCount DESC, m.createdAt DESC")
    List<MindMapResponse> findUserFavoriteMindMaps(@Param("userId") Long userId);

    /**
     * 删除指定时间之前的失败记录
     */
    void deleteByStatusAndCreatedAtBefore(MindMapResponse.ResponseStatus status, LocalDateTime cutoffTime);

    /**
     * 查找最近的思维导图
     */
    List<MindMapResponse> findTop10ByUserIdOrderByCreatedAtDesc(Long userId);

    /**
     * 根据节点数范围查找思维导图
     */
    @Query("SELECT m FROM MindMapResponse m WHERE m.nodeCount BETWEEN :minNodes AND :maxNodes ORDER BY m.createdAt DESC")
    List<MindMapResponse> findByNodeCountBetween(@Param("minNodes") Integer minNodes, @Param("maxNodes") Integer maxNodes);

    /**
     * 查找复杂度高的思维导图（节点数多或深度大）
     */
    @Query("SELECT m FROM MindMapResponse m WHERE m.nodeCount > :minNodes OR m.maxDepth > :minDepth ORDER BY m.nodeCount DESC, m.maxDepth DESC")
    List<MindMapResponse> findComplexMindMaps(@Param("minNodes") Integer minNodes, @Param("minDepth") Integer minDepth);

    /**
     * 查找与聊天消息关联的思维导图
     */
    Optional<MindMapResponse> findByChatMessageId(Long chatMessageId);

    /**
     * 查找指定用户在指定会话中的最新思维导图
     */
    @Query("SELECT m FROM MindMapResponse m WHERE m.userId = :userId AND m.sessionId = :sessionId ORDER BY m.createdAt DESC")
    Optional<MindMapResponse> findLatestByUserIdAndSessionId(@Param("userId") Long userId, @Param("sessionId") String sessionId);
}
