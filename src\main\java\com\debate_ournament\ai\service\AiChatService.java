package com.debate_ournament.ai.service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.debate_ournament.ai.entity.AiConfig;
import com.debate_ournament.ai.entity.AiProvider;
import com.debate_ournament.ai.entity.ChatMessage;
import com.debate_ournament.ai.repository.AiConfigRepository;
import com.debate_ournament.ai.repository.ChatMessageRepository;

/**
 * AI聊天服务
 * 核心业务逻辑类，处理AI聊天的所有业务功能
 */
@Service
@Transactional
public class AiChatService {

    private static final Logger logger = LoggerFactory.getLogger(AiChatService.class);

    private final AiConfigRepository aiConfigRepository;
    private final ChatMessageRepository chatMessageRepository;
    private final AiClientFactory aiClientFactory;

    public AiChatService(AiConfigRepository aiConfigRepository,
                        ChatMessageRepository chatMessageRepository,
                        AiClientFactory aiClientFactory) {
        this.aiConfigRepository = aiConfigRepository;
        this.chatMessageRepository = chatMessageRepository;
        this.aiClientFactory = aiClientFactory;
    }

    /**
     * 发送聊天消息
     */
    public ChatMessage sendMessage(Long userId, String sessionId, String content, Long configId) {
        try {
            // 获取或创建会话ID
            if (sessionId == null || sessionId.isEmpty()) {
                sessionId = generateSessionId();
            }

            // 保存用户消息
            ChatMessage userMessage = ChatMessage.createUserMessage(sessionId, userId, content);
            userMessage = chatMessageRepository.save(userMessage);

            // 获取AI配置
            AiConfig config = getAiConfig(configId);
            if (config == null) {
                throw new RuntimeException("未找到可用的AI配置");
            }

            // 获取对话历史
            List<ChatMessage> conversationHistory = getConversationHistory(sessionId, 10);

            // 获取AI客户端
            AiClient aiClient = aiClientFactory.getClient(config.getProvider());

            // 调用AI API
            long startTime = System.currentTimeMillis();
            String aiResponse = aiClient.sendChatMessage(conversationHistory, config);
            long responseTime = System.currentTimeMillis() - startTime;

            // 保存AI回复
            ChatMessage assistantMessage = ChatMessage.createAssistantMessage(sessionId, userId, aiResponse);
            assistantMessage.setAiProvider(config.getProvider());
            assistantMessage.setModelName(config.getModelName());
            assistantMessage.setAiConfigId(config.getId());
            assistantMessage.setResponseTime(responseTime);
            assistantMessage.setTokenCount(aiClient.estimateTokenCount(aiResponse));

            assistantMessage = chatMessageRepository.save(assistantMessage);

            logger.info("AI聊天成功 - 用户: {}, 会话: {}, 提供商: {}, 耗时: {}ms",
                       userId, sessionId, config.getProvider(), responseTime);

            return assistantMessage;

        } catch (Exception e) {
            logger.error("AI聊天失败 - 用户: {}, 会话: {}, 错误: {}", userId, sessionId, e.getMessage(), e);

            // 保存错误消息
            ChatMessage errorMessage = ChatMessage.createAssistantMessage(sessionId, userId, "抱歉，我遇到了一些问题，请稍后再试。");
            errorMessage.setStatus("failed");
            errorMessage.setErrorMessage(e.getMessage());
            chatMessageRepository.save(errorMessage);

            throw new RuntimeException("AI聊天失败: " + e.getMessage(), e);
        }
    }

    /**
     * 流式发送聊天消息
     */
    public void sendStreamMessage(Long userId, String sessionId, String content, Long configId,
                                 AiClient.StreamCallback callback) {
        try {
            // 获取或创建会话ID
            if (sessionId == null || sessionId.isEmpty()) {
                sessionId = generateSessionId();
            }

            // 保存用户消息
            ChatMessage userMessage = ChatMessage.createUserMessage(sessionId, userId, content);
            chatMessageRepository.save(userMessage);

            // 获取AI配置
            AiConfig config = getAiConfig(configId);
            if (config == null) {
                callback.onError("未找到可用的AI配置");
                return;
            }

            // 获取对话历史
            List<ChatMessage> conversationHistory = getConversationHistory(sessionId, 10);

            // 获取AI客户端
            AiClient aiClient = aiClientFactory.getClient(config.getProvider());

            // 流式调用AI API
            aiClient.sendStreamChatMessage(conversationHistory, config, callback);

        } catch (Exception e) {
            logger.error("流式AI聊天失败", e);
            callback.onError("AI聊天失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户的聊天会话列表
     */
    @Transactional(readOnly = true)
    public List<String> getUserSessions(Long userId) {
        return chatMessageRepository.findDistinctSessionIdsByUserIdOrderByLatestMessage(userId);
    }

    /**
     * 获取会话的聊天记录
     */
    @Transactional(readOnly = true)
    public List<ChatMessage> getSessionMessages(String sessionId) {
        return chatMessageRepository.findBySessionIdOrderByCreatedAtAsc(sessionId);
    }

    /**
     * 获取会话的聊天记录（带用户验证）
     */
    @Transactional(readOnly = true)
    public List<ChatMessage> getSessionMessages(String sessionId, Long userId) {
        // 验证会话是否属于当前用户
        List<ChatMessage> messages = chatMessageRepository.findByUserIdAndSessionIdOrderByCreatedAtAsc(userId, sessionId);
        return messages;
    }

    /**
     * 获取用户的聊天记录（分页）
     */
    @Transactional(readOnly = true)
    public Page<ChatMessage> getUserMessages(Long userId, Pageable pageable) {
        return chatMessageRepository.findByUserIdOrderByCreatedAtDesc(userId, pageable);
    }

    /**
     * 删除会话
     */
    public void deleteSession(String sessionId, Long userId) {
        List<ChatMessage> messages = chatMessageRepository.findByUserIdAndSessionIdOrderByCreatedAtAsc(userId, sessionId);
        chatMessageRepository.deleteAll(messages);
        logger.info("删除会话: {} (用户: {})", sessionId, userId);
    }

    /**
     * 清理旧的聊天记录
     */
    public void cleanupOldMessages(int daysToKeep) {
        LocalDateTime cutoffTime = LocalDateTime.now().minusDays(daysToKeep);
        chatMessageRepository.deleteByCreatedAtBefore(cutoffTime);
        logger.info("清理{}天前的聊天记录", daysToKeep);
    }

    /**
     * 获取用户Token消耗统计
     */
    @Transactional(readOnly = true)
    public Long getUserTokenConsumption(Long userId) {
        Long totalTokens = chatMessageRepository.sumTokenCountByUserId(userId);
        return totalTokens != null ? totalTokens : 0L;
    }

    /**
     * 测试AI配置连接
     */
    public boolean testAiConfig(Long configId) {
        try {
            Optional<AiConfig> configOpt = aiConfigRepository.findById(configId);
            if (configOpt.isEmpty()) {
                return false;
            }

            AiConfig config = configOpt.get();
            AiClient aiClient = aiClientFactory.getClient(config.getProvider());

            return aiClient.testConnection(config);

        } catch (Exception e) {
            logger.warn("测试AI配置连接失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 获取AI配置
     */
    private AiConfig getAiConfig(Long configId) {
        if (configId != null) {
            return aiConfigRepository.findById(configId).orElse(null);
        }

        // 使用默认配置
        return aiConfigRepository.findByIsDefaultTrueAndEnabledTrue().orElse(null);
    }

    /**
     * 获取对话历史（限制数量）
     */
    private List<ChatMessage> getConversationHistory(String sessionId, int limit) {
        List<ChatMessage> allMessages = chatMessageRepository.findBySessionIdOrderByCreatedAtAsc(sessionId);

        // 只保留最近的消息
        int startIndex = Math.max(0, allMessages.size() - limit);
        return allMessages.subList(startIndex, allMessages.size());
    }

    /**
     * 生成会话ID
     */
    private String generateSessionId() {
        return "session_" + UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 获取支持的AI提供商列表
     */
    @Transactional(readOnly = true)
    public List<AiProvider> getSupportedProviders() {
        return List.of(aiClientFactory.getSupportedProviders());
    }

    /**
     * 根据提供商获取支持的模型列表
     */
    public List<String> getSupportedModels(AiProvider provider, Long configId) {
        try {
            AiClient aiClient = aiClientFactory.getClient(provider);
            AiConfig config = getAiConfig(configId);
            if (config == null) {
                return List.of();
            }
            return aiClient.getSupportedModels(config);
        } catch (IllegalArgumentException e) {
            return List.of();
        }
    }
}
