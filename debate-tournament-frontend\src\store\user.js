import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { apiService } from './index';

export const useUserStore = defineStore('user', () => {
  // 状态
  const token = ref(localStorage.getItem('token') || '');
  const userInfo = ref(JSON.parse(localStorage.getItem('userInfo') || '{}'));
  const roles = ref([]);
  const permissions = ref([]);

  // 计算属性
  const isLoggedIn = computed(() => !!token.value);
  const username = computed(() => userInfo.value.username || '');
  const avatar = computed(() => userInfo.value.avatar || '');
  const userId = computed(() => userInfo.value.id || '');
  const email = computed(() => userInfo.value.email || '');

  // 设置Token
  function setToken(newToken) {
    token.value = newToken;
    localStorage.setItem('token', newToken);
  }

  // 设置用户信息
  function setUserInfo(info) {
    userInfo.value = info;
    localStorage.setItem('userInfo', JSON.stringify(info));
  }

  // 设置用户角色
  function setRoles(userRoles) {
    roles.value = userRoles;
  }

  // 设置用户权限
  function setPermissions(userPermissions) {
    permissions.value = userPermissions;
  }

  // 登录
  async function login(loginData) {
    try {
      const response = await apiService.post('/api/auth/login', loginData);

      if (response.code === 200) {
        setToken(response.data.token);
        setUserInfo(response.data.userInfo);
        return response.data;
      } else {
        throw new Error(response.message || '登录失败');
      }
    } catch (error) {
      console.error('登录失败:', error);
      throw error;
    }
  }

  // 注册
  async function register(registerData) {
    try {
      const response = await apiService.post('/api/auth/register', registerData);

      if (response.code === 200) {
        return response.data;
      } else {
        throw new Error(response.message || '注册失败');
      }
    } catch (error) {
      console.error('注册失败:', error);
      throw error;
    }
  }

  // 获取用户信息
  async function getUserInfo() {
    try {
      const response = await apiService.get('/api/user/info');

      if (response.code === 200) {
        setUserInfo(response.data);
        setRoles(response.data.roles || []);
        setPermissions(response.data.permissions || []);
        return response.data;
      } else {
        throw new Error(response.message || '获取用户信息失败');
      }
    } catch (error) {
      console.error('获取用户信息失败:', error);
      throw error;
    }
  }

  // 退出登录
  async function logout() {
    try {
      if (token.value) {
        await apiService.post('/api/auth/logout');
      }
    } catch (error) {
      console.error('退出登录失败:', error);
    } finally {
      // 清除本地存储的用户信息
      clearUserState();
    }
  }

  // 清除用户状态
  function clearUserState() {
    token.value = '';
    userInfo.value = {};
    roles.value = [];
    permissions.value = [];
    localStorage.removeItem('token');
    localStorage.removeItem('userInfo');
  }

  // 检查权限
  function hasPermission(permission) {
    return permissions.value.includes(permission);
  }

  // 检查角色
  function hasRole(role) {
    return roles.value.includes(role);
  }

  // 更新用户信息
  async function updateUserInfo(userData) {
    try {
      const response = await apiService.put('/api/user/info', userData);

      if (response.code === 200) {
        // 合并更新后的用户信息
        setUserInfo({
          ...userInfo.value,
          ...response.data
        });
        return response.data;
      } else {
        throw new Error(response.message || '更新用户信息失败');
      }
    } catch (error) {
      console.error('更新用户信息失败:', error);
      throw error;
    }
  }

  // 修改密码
  async function changePassword(passwordData) {
    try {
      const response = await apiService.post('/api/user/change-password', passwordData);

      if (response.code === 200) {
        return response.data;
      } else {
        throw new Error(response.message || '修改密码失败');
      }
    } catch (error) {
      console.error('修改密码失败:', error);
      throw error;
    }
  }

  return {
    token,
    userInfo,
    roles,
    permissions,
    isLoggedIn,
    username,
    avatar,
    userId,
    email,
    setToken,
    setUserInfo,
    setRoles,
    setPermissions,
    login,
    register,
    getUserInfo,
    logout,
    clearUserState,
    hasPermission,
    hasRole,
    updateUserInfo,
    changePassword
  };
});
