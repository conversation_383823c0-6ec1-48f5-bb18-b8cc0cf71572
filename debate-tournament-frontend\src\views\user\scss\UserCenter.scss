@use 'sass:color';
@use 'sass:math';
@use '@/styles/variables' as v;
@use '@/styles/mixins' as m;

// 用户中心样式
.user-center {
  padding: v.$spacing-lg 0;
}

// 左侧用户信息
.user-profile {
  margin-bottom: v.$spacing-lg;

  &__header {
    text-align: center;
    padding: v.$spacing-lg;
    border-bottom: 1px solid color.adjust(black, $alpha: -0.9);
  }

  &__avatar {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    margin: 0 auto v.$spacing-md;
    overflow: hidden;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  &__stats {
    display: flex;
    text-align: center;
    border-bottom: 1px solid color.adjust(black, $alpha: -0.9);

    .stat-item {
      flex: 1;
      padding: v.$spacing-md;

      .stat-value {
        display: block;
        font-size: 1.25rem;
        font-weight: 600;
        margin-bottom: v.$spacing-xs;
      }

      .stat-label {
        color: color.adjust(black, $alpha: -0.4);
        font-size: 0.875rem;
      }
    }
  }
}

// 统计卡片
.statistics-card {
  background: white;
  border-radius: v.$shape-corner-medium;
  padding: v.$spacing-lg;
  box-shadow: 0 2px 4px color.adjust(black, $alpha: -0.9);

  &__title {
    font-size: 1.25rem;
    font-weight: 500;
    margin-bottom: v.$spacing-md;
    padding-bottom: v.$spacing-sm;
    border-bottom: 1px solid color.adjust(black, $alpha: -0.9);
  }

  &__content {
    display: grid;
    gap: v.$spacing-md;
  }
}

// 加载状态
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;

  .spinner {
    width: 40px;
    height: 40px;
    border: 4px solid color.adjust(v.$color-primary, $alpha: -0.8);
    border-top-color: v.$color-primary;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

// 响应式布局
@include m.respond-to(sm) {
  .statistics-container {
    grid-template-columns: 1fr;
  }

  .user-profile {
    &__stats {
      flex-wrap: wrap;

      .stat-item {
        flex: 0 0 50%;
      }
    }
  }
}
