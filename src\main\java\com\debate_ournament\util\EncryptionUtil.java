package com.debate_ournament.util;

import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.SecureRandom;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

import javax.crypto.Cipher;
import javax.crypto.Mac;
import javax.crypto.SecretKey;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.PBEKeySpec;
import javax.crypto.spec.SecretKeySpec;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * 高性能加密工具类
 *
 * 提供RSA、AES混合加密方案，支持以下功能：
 * - RSA 2048位密钥对生成和管理
 * - AES-256-CBC对称加密
 * - HMAC-SHA256消息认证
 * - PBKDF2密钥派生
 * - 安全随机数生成
 * - 密钥缓存优化
 *
 * <AUTHOR> Debate Tournament Platform
 * @version 2.0
 * @since 2024
 */
@Component
public class EncryptionUtil {

    private static final Logger logger = LoggerFactory.getLogger(EncryptionUtil.class);

    // RSA加密相关常量
    private static final String RSA_ALGORITHM = "RSA";
    private static final String RSA_TRANSFORMATION = "RSA/ECB/PKCS1Padding";
    private static final int RSA_KEY_SIZE = 2048;
    private static final int RSA_MAX_ENCRYPT_BLOCK = 245; // RSA 2048位密钥最大加密块大小
    private static final int RSA_MAX_DECRYPT_BLOCK = 256; // RSA 2048位密钥最大解密块大小

    // AES加密相关常量
    private static final String AES_ALGORITHM = "AES";
    private static final String AES_TRANSFORMATION = "AES/CBC/PKCS5Padding";
    private static final int AES_KEY_SIZE = 256;
    private static final int AES_KEY_LENGTH = 32; // 256位 = 32字节
    private static final int IV_SIZE = 16; // AES块大小

    // HMAC相关常量
    private static final String HMAC_ALGORITHM = "HmacSHA256";
    private static final int HMAC_KEY_SIZE = 32;

    // PBKDF2相关常量
    private static final String PBKDF2_ALGORITHM = "PBKDF2WithHmacSHA256";
    private static final int PBKDF2_DEFAULT_ITERATIONS = 100000;
    private static final int PBKDF2_MIN_ITERATIONS = 10000;

    // 安全随机数生成器
    private static final SecureRandom SECURE_RANDOM = new SecureRandom();

    // 密钥工厂缓存
    private static final ConcurrentMap<String, KeyFactory> KEY_FACTORY_CACHE = new ConcurrentHashMap<>();

    /**
     * 生成RSA密钥对
     * 使用2048位密钥长度确保安全性
     *
     * @return RSA密钥对
     * @throws RuntimeException 如果密钥生成失败
     */
    public KeyPair generateRSAKeyPair() {
        try {
            KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance(RSA_ALGORITHM);
            keyPairGenerator.initialize(RSA_KEY_SIZE, SECURE_RANDOM);

            KeyPair keyPair = keyPairGenerator.generateKeyPair();
            logger.debug("成功生成RSA密钥对，密钥长度: {} 位", RSA_KEY_SIZE);

            return keyPair;
        } catch (NoSuchAlgorithmException e) {
            logger.error("不支持的RSA算法", e);
            throw new RuntimeException("生成RSA密钥对失败：不支持的算法", e);
        } catch (Exception e) {
            logger.error("生成RSA密钥对时发生未知错误", e);
            throw new RuntimeException("生成RSA密钥对失败", e);
        }
    }

    /**
     * 将公钥转换为Base64字符串
     *
     * @param publicKey RSA公钥
     * @return Base64编码的公钥字符串
     */
    public String publicKeyToBase64(PublicKey publicKey) {
        if (publicKey == null) {
            throw new IllegalArgumentException("公钥不能为空");
        }
        return Base64.getEncoder().encodeToString(publicKey.getEncoded());
    }

    /**
     * 将私钥转换为Base64字符串
     *
     * @param privateKey RSA私钥
     * @return Base64编码的私钥字符串
     */
    public String privateKeyToBase64(PrivateKey privateKey) {
        if (privateKey == null) {
            throw new IllegalArgumentException("私钥不能为空");
        }
        return Base64.getEncoder().encodeToString(privateKey.getEncoded());
    }

    /**
     * 从Base64字符串恢复公钥
     * 使用缓存的KeyFactory提升性能
     *
     * @param base64PublicKey Base64编码的公钥字符串
     * @return RSA公钥
     * @throws RuntimeException 如果公钥恢复失败
     */
    public PublicKey base64ToPublicKey(String base64PublicKey) {
        if (base64PublicKey == null || base64PublicKey.trim().isEmpty()) {
            throw new IllegalArgumentException("公钥字符串不能为空");
        }

        try {
            byte[] keyBytes = Base64.getDecoder().decode(base64PublicKey.trim());
            X509EncodedKeySpec spec = new X509EncodedKeySpec(keyBytes);
            KeyFactory keyFactory = getKeyFactory(RSA_ALGORITHM);

            PublicKey publicKey = keyFactory.generatePublic(spec);
            logger.debug("成功从Base64字符串恢复公钥");

            return publicKey;
        } catch (IllegalArgumentException e) {
            logger.error("Base64解码失败，无效的公钥格式", e);
            throw new RuntimeException("无效的公钥格式", e);
        } catch (InvalidKeySpecException e) {
            logger.error("无效的公钥规格", e);
            throw new RuntimeException("恢复公钥失败：无效的密钥规格", e);
        } catch (Exception e) {
            logger.error("恢复公钥时发生未知错误", e);
            throw new RuntimeException("恢复公钥失败", e);
        }
    }

    /**
     * 从Base64字符串恢复私钥
     * 使用缓存的KeyFactory提升性能
     *
     * @param base64PrivateKey Base64编码的私钥字符串
     * @return RSA私钥
     * @throws RuntimeException 如果私钥恢复失败
     */
    public PrivateKey base64ToPrivateKey(String base64PrivateKey) {
        if (base64PrivateKey == null || base64PrivateKey.trim().isEmpty()) {
            throw new IllegalArgumentException("私钥字符串不能为空");
        }

        try {
            byte[] keyBytes = Base64.getDecoder().decode(base64PrivateKey.trim());
            PKCS8EncodedKeySpec spec = new PKCS8EncodedKeySpec(keyBytes);
            KeyFactory keyFactory = getKeyFactory(RSA_ALGORITHM);

            PrivateKey privateKey = keyFactory.generatePrivate(spec);
            logger.debug("成功从Base64字符串恢复私钥");

            return privateKey;
        } catch (IllegalArgumentException e) {
            logger.error("Base64解码失败，无效的私钥格式", e);
            throw new RuntimeException("无效的私钥格式", e);
        } catch (InvalidKeySpecException e) {
            logger.error("无效的私钥规格", e);
            throw new RuntimeException("恢复私钥失败：无效的密钥规格", e);
        } catch (Exception e) {
            logger.error("恢复私钥时发生未知错误", e);
            throw new RuntimeException("恢复私钥失败", e);
        }
    }

    /**
     * RSA解密
     * 支持分块解密大数据
     *
     * @param encryptedData Base64编码的加密数据
     * @param privateKey RSA私钥
     * @return 解密后的明文字符串
     * @throws RuntimeException 如果解密失败
     */
    public String rsaDecrypt(String encryptedData, PrivateKey privateKey) {
        if (encryptedData == null || encryptedData.trim().isEmpty()) {
            throw new IllegalArgumentException("加密数据不能为空");
        }
        if (privateKey == null) {
            throw new IllegalArgumentException("私钥不能为空");
        }

        try {
            Cipher cipher = Cipher.getInstance(RSA_TRANSFORMATION);
            cipher.init(Cipher.DECRYPT_MODE, privateKey);

            byte[] encryptedBytes = Base64.getDecoder().decode(encryptedData.trim());

            // 分块解密
            byte[] decryptedBytes = doFinalWithBlocks(cipher, encryptedBytes, RSA_MAX_DECRYPT_BLOCK);
            String result = new String(decryptedBytes, StandardCharsets.UTF_8);

            logger.debug("RSA解密成功，原文长度: {} 字节", decryptedBytes.length);
            return result;
        } catch (Exception e) {
            logger.error("RSA解密失败", e);
            throw new RuntimeException("RSA解密失败", e);
        }
    }

    /**
     * RSA加密
     * 支持分块加密大数据
     *
     * @param data 待加密的明文数据
     * @param publicKey RSA公钥
     * @return Base64编码的加密字符串
     * @throws RuntimeException 如果加密失败
     */
    public String rsaEncrypt(String data, PublicKey publicKey) {
        if (data == null) {
            throw new IllegalArgumentException("待加密数据不能为空");
        }
        if (publicKey == null) {
            throw new IllegalArgumentException("公钥不能为空");
        }

        try {
            Cipher cipher = Cipher.getInstance(RSA_TRANSFORMATION);
            cipher.init(Cipher.ENCRYPT_MODE, publicKey);

            byte[] dataBytes = data.getBytes(StandardCharsets.UTF_8);

            // 分块加密
            byte[] encryptedBytes = doFinalWithBlocks(cipher, dataBytes, RSA_MAX_ENCRYPT_BLOCK);
            String result = Base64.getEncoder().encodeToString(encryptedBytes);

            logger.debug("RSA加密成功，原文长度: {} 字节，密文长度: {} 字节",
                        dataBytes.length, encryptedBytes.length);
            return result;
        } catch (Exception e) {
            logger.error("RSA加密失败", e);
            throw new RuntimeException("RSA加密失败", e);
        }
    }

    /**
     * AES解密
     * 使用CBC模式和PKCS5填充
     *
     * @param encryptedData Base64编码的加密数据（包含IV）
     * @param key AES密钥（32字节）
     * @return 解密后的明文字符串
     * @throws RuntimeException 如果解密失败
     */
    public String aesDecrypt(String encryptedData, String key) {
        if (encryptedData == null || encryptedData.trim().isEmpty()) {
            throw new IllegalArgumentException("加密数据不能为空");
        }
        if (key == null || key.isEmpty()) {
            throw new IllegalArgumentException("密钥不能为空");
        }

        try {
            byte[] encryptedBytes = Base64.getDecoder().decode(encryptedData.trim());

            if (encryptedBytes.length < IV_SIZE) {
                throw new IllegalArgumentException("加密数据格式错误：长度不足");
            }

            // 提取IV
            byte[] iv = new byte[IV_SIZE];
            System.arraycopy(encryptedBytes, 0, iv, 0, IV_SIZE);

            // 提取加密数据
            byte[] cipherText = new byte[encryptedBytes.length - IV_SIZE];
            System.arraycopy(encryptedBytes, IV_SIZE, cipherText, 0, cipherText.length);

            // 生成密钥规格
            SecretKeySpec secretKey = createAESKey(key);

            Cipher cipher = Cipher.getInstance(AES_TRANSFORMATION);
            cipher.init(Cipher.DECRYPT_MODE, secretKey, new IvParameterSpec(iv));

            byte[] decryptedBytes = cipher.doFinal(cipherText);
            String result = new String(decryptedBytes, StandardCharsets.UTF_8);

            logger.debug("AES解密成功，解密数据长度: {} 字节", decryptedBytes.length);
            return result;
        } catch (IllegalArgumentException e) {
            logger.error("AES解密参数错误: {}", e.getMessage());
            throw new RuntimeException("AES解密失败：" + e.getMessage(), e);
        } catch (Exception e) {
            logger.error("AES解密失败", e);
            throw new RuntimeException("AES解密失败", e);
        }
    }

    /**
     * AES加密
     * 使用CBC模式和PKCS5填充，自动生成随机IV
     *
     * @param data 待加密的明文数据
     * @param key AES密钥（32字节）
     * @return Base64编码的加密字符串（包含IV）
     * @throws RuntimeException 如果加密失败
     */
    public String aesEncrypt(String data, String key) {
        if (data == null) {
            throw new IllegalArgumentException("待加密数据不能为空");
        }
        if (key == null || key.isEmpty()) {
            throw new IllegalArgumentException("密钥不能为空");
        }

        try {
            // 生成随机IV
            byte[] iv = new byte[IV_SIZE];
            SECURE_RANDOM.nextBytes(iv);

            // 生成密钥规格
            SecretKeySpec secretKey = createAESKey(key);

            Cipher cipher = Cipher.getInstance(AES_TRANSFORMATION);
            cipher.init(Cipher.ENCRYPT_MODE, secretKey, new IvParameterSpec(iv));

            byte[] dataBytes = data.getBytes(StandardCharsets.UTF_8);
            byte[] encryptedBytes = cipher.doFinal(dataBytes);

            // 将IV和加密数据组合
            byte[] result = new byte[IV_SIZE + encryptedBytes.length];
            System.arraycopy(iv, 0, result, 0, IV_SIZE);
            System.arraycopy(encryptedBytes, 0, result, IV_SIZE, encryptedBytes.length);

            String encodedResult = Base64.getEncoder().encodeToString(result);
            logger.debug("AES加密成功，原文长度: {} 字节，密文长度: {} 字节",
                        dataBytes.length, result.length);

            return encodedResult;
        } catch (IllegalArgumentException e) {
            logger.error("AES加密参数错误: {}", e.getMessage());
            throw new RuntimeException("AES加密失败：" + e.getMessage(), e);
        } catch (Exception e) {
            logger.error("AES加密失败", e);
            throw new RuntimeException("AES加密失败", e);
        }
    }

    /**
     * 生成HMAC签名
     * 使用SHA-256算法
     *
     * @param data 待签名的数据
     * @param key HMAC密钥
     * @return Base64编码的HMAC签名
     * @throws RuntimeException 如果签名生成失败
     */
    public String generateHmacSignature(String data, String key) {
        if (data == null) {
            throw new IllegalArgumentException("待签名数据不能为空");
        }
        if (key == null || key.isEmpty()) {
            throw new IllegalArgumentException("HMAC密钥不能为空");
        }

        try {
            SecretKeySpec secretKey = new SecretKeySpec(
                key.getBytes(StandardCharsets.UTF_8), HMAC_ALGORITHM);
            Mac mac = Mac.getInstance(HMAC_ALGORITHM);
            mac.init(secretKey);

            byte[] hmacBytes = mac.doFinal(data.getBytes(StandardCharsets.UTF_8));
            String result = Base64.getEncoder().encodeToString(hmacBytes);

            logger.debug("生成HMAC签名成功，数据长度: {} 字节", data.length());
            return result;
        } catch (Exception e) {
            logger.error("生成HMAC签名失败", e);
            throw new RuntimeException("生成HMAC签名失败", e);
        }
    }

    /**
     * 验证HMAC签名
     * 使用常数时间比较防止时序攻击
     *
     * @param data 原始数据
     * @param signature 待验证的签名
     * @param key HMAC密钥
     * @return 签名是否有效
     */
    public boolean verifyHmacSignature(String data, String signature, String key) {
        if (data == null || signature == null || key == null) {
            logger.warn("HMAC验证参数包含空值");
            return false;
        }

        try {
            String calculatedSignature = generateHmacSignature(data, key);
            boolean isValid = constantTimeEquals(calculatedSignature, signature);

            logger.debug("HMAC签名验证{}，数据长度: {} 字节",
                        isValid ? "成功" : "失败", data.length());
            return isValid;
        } catch (Exception e) {
            logger.error("验证HMAC签名失败", e);
            return false;
        }
    }

    /**
     * 生成密码学安全的随机字符串
     *
     * @param length 字符串长度
     * @return 随机字符串
     */
    public String generateRandomString(int length) {
        if (length <= 0) {
            throw new IllegalArgumentException("长度必须大于0");
        }

        // 生成随机字节数组
        byte[] bytes = new byte[length * 3 / 4 + 1]; // 确保有足够的字节
        SECURE_RANDOM.nextBytes(bytes);

        // 使用Base64编码并截取指定长度
        String result = Base64.getEncoder().encodeToString(bytes)
                .replaceAll("[+/=]", "") // 移除特殊字符
                .substring(0, Math.min(length,
                    Base64.getEncoder().encodeToString(bytes).length()));

        logger.debug("生成随机字符串，长度: {} 字符", result.length());
        return result;
    }

    /**
     * 生成安全的AES密钥
     *
     * @return Base64编码的256位AES密钥
     */
    public String generateAESKey() {
        byte[] keyBytes = new byte[AES_KEY_LENGTH];
        SECURE_RANDOM.nextBytes(keyBytes);
        String key = Base64.getEncoder().encodeToString(keyBytes);

        logger.debug("生成AES密钥成功，长度: {} 位", AES_KEY_SIZE);
        return key;
    }

    /**
     * 生成安全的HMAC密钥
     *
     * @return Base64编码的HMAC密钥
     */
    public String generateHMACKey() {
        byte[] keyBytes = new byte[HMAC_KEY_SIZE];
        SECURE_RANDOM.nextBytes(keyBytes);
        String key = Base64.getEncoder().encodeToString(keyBytes);

        logger.debug("生成HMAC密钥成功，长度: {} 字节", HMAC_KEY_SIZE);
        return key;
    }

    /**
     * 使用PBKDF2派生密钥
     *
     * @param password 密码
     * @param salt 盐值
     * @param iterations 迭代次数（建议 >= 100,000）
     * @param keyLength 密钥长度（位）
     * @return Base64编码的派生密钥
     * @throws RuntimeException 如果密钥派生失败
     */
    public String deriveKeyFromPassword(String password, String salt, int iterations, int keyLength) {
        if (password == null || password.isEmpty()) {
            throw new IllegalArgumentException("密码不能为空");
        }
        if (salt == null || salt.isEmpty()) {
            throw new IllegalArgumentException("盐值不能为空");
        }
        if (iterations < PBKDF2_MIN_ITERATIONS) {
            throw new IllegalArgumentException("迭代次数不能少于 " + PBKDF2_MIN_ITERATIONS);
        }
        if (keyLength <= 0) {
            throw new IllegalArgumentException("密钥长度必须大于0");
        }

        try {
            PBEKeySpec spec = new PBEKeySpec(
                password.toCharArray(),
                salt.getBytes(StandardCharsets.UTF_8),
                iterations,
                keyLength
            );

            SecretKeyFactory factory = SecretKeyFactory.getInstance(PBKDF2_ALGORITHM);
            SecretKey key = factory.generateSecret(spec);

            // 清除敏感数据
            spec.clearPassword();

            String result = Base64.getEncoder().encodeToString(key.getEncoded());
            logger.debug("PBKDF2密钥派生成功，迭代次数: {}，密钥长度: {} 位", iterations, keyLength);

            return result;
        } catch (Exception e) {
            logger.error("PBKDF2密钥派生失败", e);
            throw new RuntimeException("密钥派生失败", e);
        }
    }

    /**
     * 使用默认参数的PBKDF2密钥派生
     *
     * @param password 密码
     * @param salt 盐值
     * @return Base64编码的256位派生密钥
     */
    public String deriveKeyFromPassword(String password, String salt) {
        return deriveKeyFromPassword(password, salt, PBKDF2_DEFAULT_ITERATIONS, AES_KEY_SIZE);
    }

    // ============ 私有辅助方法 ============

    /**
     * 获取缓存的KeyFactory实例
     */
    private KeyFactory getKeyFactory(String algorithm) throws NoSuchAlgorithmException {
        return KEY_FACTORY_CACHE.computeIfAbsent(algorithm, alg -> {
            try {
                return KeyFactory.getInstance(alg);
            } catch (NoSuchAlgorithmException e) {
                throw new RuntimeException("不支持的算法: " + alg, e);
            }
        });
    }

    /**
     * 创建AES密钥规格
     */
    private SecretKeySpec createAESKey(String key) {
        byte[] keyBytes = key.getBytes(StandardCharsets.UTF_8);

        // 确保密钥长度正确
        if (keyBytes.length < AES_KEY_LENGTH) {
            // 填充密钥
            byte[] paddedKey = new byte[AES_KEY_LENGTH];
            System.arraycopy(keyBytes, 0, paddedKey, 0, keyBytes.length);
            keyBytes = paddedKey;
        } else if (keyBytes.length > AES_KEY_LENGTH) {
            // 截断密钥
            byte[] truncatedKey = new byte[AES_KEY_LENGTH];
            System.arraycopy(keyBytes, 0, truncatedKey, 0, AES_KEY_LENGTH);
            keyBytes = truncatedKey;
        }

        return new SecretKeySpec(keyBytes, AES_ALGORITHM);
    }

    /**
     * 分块处理加密/解密操作
     */
    private byte[] doFinalWithBlocks(Cipher cipher, byte[] data, int blockSize) throws Exception {
        int dataLength = data.length;
        int offset = 0;
        byte[] cache;
        int i = 0;

        // 计算总输出长度
        int outputLength = 0;
        int tempOffset = 0;
        while (tempOffset < dataLength) {
            int currentBlockSize = Math.min(blockSize, dataLength - tempOffset);
            byte[] tempResult = cipher.doFinal(data, tempOffset, currentBlockSize);
            outputLength += tempResult.length;
            tempOffset += blockSize;
        }

        // 执行实际的分块操作
        byte[] result = new byte[outputLength];
        int resultOffset = 0;

        offset = 0;
        while (offset < dataLength) {
            int currentBlockSize = Math.min(blockSize, dataLength - offset);
            cache = cipher.doFinal(data, offset, currentBlockSize);
            System.arraycopy(cache, 0, result, resultOffset, cache.length);
            resultOffset += cache.length;
            offset += blockSize;
        }

        return result;
    }

    /**
     * 常数时间字符串比较，防止时序攻击
     */
    private boolean constantTimeEquals(String a, String b) {
        if (a == null || b == null) {
            return a == b;
        }

        if (a.length() != b.length()) {
            return false;
        }

        int result = 0;
        for (int i = 0; i < a.length(); i++) {
            result |= a.charAt(i) ^ b.charAt(i);
        }

        return result == 0;
    }
}
