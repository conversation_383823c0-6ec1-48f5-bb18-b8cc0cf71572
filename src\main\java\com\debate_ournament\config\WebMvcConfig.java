package com.debate_ournament.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Web MVC 配置类
 * 配置静态资源访问路径
 */
@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

    @Value("${app.upload.path:uploads}")
    private String uploadPath;

    /**
     * 配置静态资源处理器
     * 允许通过URL访问上传的文件
     */
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 配置上传文件的访问路径
        // 访问 /uploads/** 的请求将映射到 file:./uploads/ 目录
        registry.addResourceHandler("/uploads/**")
                .addResourceLocations("file:./" + uploadPath + "/")
                .setCachePeriod(3600); // 缓存1小时

        // 配置头像文件的访问路径
        // 访问 /avatars/** 的请求将映射到 file:./uploads/avatars/ 目录
        registry.addResourceHandler("/avatars/**")
                .addResourceLocations("file:./" + uploadPath + "/avatars/")
                .setCachePeriod(3600); // 缓存1小时

        // 配置默认静态资源
        registry.addResourceHandler("/static/**")
                .addResourceLocations("classpath:/static/")
                .setCachePeriod(31536000); // 缓存1年

        // 配置前端构建文件
        registry.addResourceHandler("/**")
                .addResourceLocations("classpath:/static/")
                .setCachePeriod(3600); // 缓存1小时
    }
}
