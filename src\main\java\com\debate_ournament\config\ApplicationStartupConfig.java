package com.debate_ournament.config;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

/**
 * 应用启动时初始化配置
 * 创建必要的目录结构
 */
@Component
public class ApplicationStartupConfig implements CommandLineRunner {

    private static final Logger logger = LoggerFactory.getLogger(ApplicationStartupConfig.class);

    @Value("${app.upload.path:uploads}")
    private String uploadPath;

    @Override
    public void run(String... args) throws Exception {
        initializeDirectories();
        createDefaultFiles();
    }

    /**
     * 初始化必要的目录结构
     */
    private void initializeDirectories() {
        try {
            // 创建主上传目录
            Path mainUploadDir = Paths.get(uploadPath);
            createDirectoryIfNotExists(mainUploadDir, "主上传目录");

            // 创建头像上传目录
            Path avatarDir = mainUploadDir.resolve("avatars");
            createDirectoryIfNotExists(avatarDir, "头像上传目录");

            // 创建临时文件目录
            Path tempDir = mainUploadDir.resolve("temp");
            createDirectoryIfNotExists(tempDir, "临时文件目录");

            // 创建备份目录
            Path backupDir = mainUploadDir.resolve("backup");
            createDirectoryIfNotExists(backupDir, "备份目录");

            logger.info("文件上传目录结构初始化完成");

        } catch (Exception e) {
            logger.error("初始化目录结构失败", e);
            throw new RuntimeException("应用启动失败：无法创建必要的目录结构", e);
        }
    }

    /**
     * 创建默认文件
     */
    private void createDefaultFiles() {
        try {
            // 创建默认头像文件的符号链接或说明文件
            Path defaultAvatarInfo = Paths.get(uploadPath, "avatars", "README.txt");
            if (!Files.exists(defaultAvatarInfo)) {
                String readme = "# 头像上传目录\n" +
                        "此目录用于存储用户上传的头像文件。\n" +
                        "支持的格式：JPG, JPEG, PNG, GIF, WebP\n" +
                        "最大文件大小：2MB\n" +
                        "文件命名规则：user_{userId}_{uuid}.{extension}\n";
                Files.write(defaultAvatarInfo, readme.getBytes());
                logger.info("创建头像目录说明文件：{}", defaultAvatarInfo);
            }

            // 创建 .gitkeep 文件确保目录被 Git 跟踪
            createGitKeepFile(Paths.get(uploadPath, "avatars"));
            createGitKeepFile(Paths.get(uploadPath, "temp"));
            createGitKeepFile(Paths.get(uploadPath, "backup"));

        } catch (IOException e) {
            logger.warn("创建默认文件时出现警告", e);
        }
    }

    /**
     * 创建目录（如果不存在）
     * @param directory 目录路径
     * @param description 目录描述
     * @throws IOException IO异常
     */
    private void createDirectoryIfNotExists(Path directory, String description) throws IOException {
        if (!Files.exists(directory)) {
            Files.createDirectories(directory);
            logger.info("创建{}：{}", description, directory.toAbsolutePath());
        } else {
            logger.debug("{}已存在：{}", description, directory.toAbsolutePath());
        }
    }

    /**
     * 创建 .gitkeep 文件
     * @param directory 目录路径
     * @throws IOException IO异常
     */
    private void createGitKeepFile(Path directory) throws IOException {
        Path gitKeepFile = directory.resolve(".gitkeep");
        if (!Files.exists(gitKeepFile)) {
            Files.createFile(gitKeepFile);
            logger.debug("创建 .gitkeep 文件：{}", gitKeepFile);
        }
    }
}
