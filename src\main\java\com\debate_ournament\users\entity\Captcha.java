package com.debate_ournament.users.entity;

import java.time.LocalDateTime;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.PrePersist;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * 验证码实体类
 */
@Entity
@Table(name = "captchas")
public class Captcha {

    @Id
    @Size(max = 100, message = "会话ID长度不能超过100个字符")
    @Column(name = "session_id", length = 100)
    private String sessionId;

    @NotBlank(message = "验证码不能为空")
    @Size(max = 10, message = "验证码长度不能超过10个字符")
    @Column(nullable = false, length = 10)
    private String code;

    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;

    @Column(name = "expires_at", nullable = false)
    private LocalDateTime expiresAt;

    @Column(name = "attempt_count", nullable = false)
    private Integer attemptCount = 0;

    @Column(name = "client_ip", length = 45)
    private String clientIp;

    @Column(name = "used", nullable = false)
    private Boolean used = false;

    // 默认构造函数
    public Captcha() {}

    // 构造函数
    public Captcha(String sessionId, String code, int expirationMinutes) {
        this.sessionId = sessionId;
        this.code = code;
        this.createdAt = LocalDateTime.now();
        this.expiresAt = this.createdAt.plusMinutes(expirationMinutes);
        this.attemptCount = 0;
        this.used = false;
    }

    @PrePersist
    protected void onCreate() {
        if (createdAt == null) {
            createdAt = LocalDateTime.now();
        }
        if (expiresAt == null) {
            expiresAt = createdAt.plusMinutes(5); // 默认5分钟过期
        }
    }

    // 业务方法
    public boolean isExpired() {
        return LocalDateTime.now().isAfter(expiresAt);
    }

    public boolean isValid() {
        return !used && !isExpired();
    }

    public void markAsUsed() {
        this.used = true;
    }

    public void incrementAttemptCount() {
        this.attemptCount++;
    }

    public boolean exceedsMaxAttempts(int maxAttempts) {
        return this.attemptCount >= maxAttempts;
    }

    // Getters and Setters
    public String getSessionId() { return sessionId; }
    public void setSessionId(String sessionId) { this.sessionId = sessionId; }

    public String getCode() { return code; }
    public void setCode(String code) { this.code = code; }

    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }

    public LocalDateTime getExpiresAt() { return expiresAt; }
    public void setExpiresAt(LocalDateTime expiresAt) { this.expiresAt = expiresAt; }

    public Integer getAttemptCount() { return attemptCount; }
    public void setAttemptCount(Integer attemptCount) { this.attemptCount = attemptCount; }

    public String getClientIp() { return clientIp; }
    public void setClientIp(String clientIp) { this.clientIp = clientIp; }

    public Boolean getUsed() { return used; }
    public void setUsed(Boolean used) { this.used = used; }
}
