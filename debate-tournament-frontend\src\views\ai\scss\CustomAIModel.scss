@use '@/styles/variables' as v;
@use '@/styles/mixins' as m;

.custom-model {
  padding: v.$spacing-lg;
  max-width: 800px;
  margin: 0 auto;
}

.page-title {
  font-size: 2rem;
  color: v.$color-primary;
  margin-bottom: v.$spacing-xl;
  text-align: center;
}

.form-section {
  background-color: v.$color-surface;
  border-radius: v.$border-radius-lg;
  padding: v.$spacing-lg;
  margin-bottom: v.$spacing-lg;
  box-shadow: v.$shadow-sm;

  h2 {
    font-size: 1.5rem;
    color: v.$color-text-primary;
    margin-bottom: v.$spacing-lg;
    padding-bottom: v.$spacing-sm;
    border-bottom: 1px solid v.$color-border;
  }
}

.form-group {
  margin-bottom: v.$spacing-lg;

  label {
    display: block;
    margin-bottom: v.$spacing-xs;
    color: v.$color-text-primary;
    font-weight: 500;
  }

  input,
  textarea,
  select {
    width: 100%;
    padding: v.$spacing-sm;
    border: 1px solid v.$color-border;
    border-radius: v.$border-radius-sm;
    font-size: 1rem;
    transition: border-color 0.3s ease;

    &:focus {
      outline: none;
      border-color: v.$color-primary;
    }

    &.error {
      border-color: v.$color-error;
    }
  }

  textarea {
    min-height: 120px;
    resize: vertical;
  }

  .helper-text {
    font-size: 0.875rem;
    color: v.$color-text-secondary;
    margin-top: v.$spacing-xs;
  }

  .error-message {
    color: v.$color-error;
    font-size: 0.875rem;
    margin-top: v.$spacing-xs;
  }
}

.parameters-list {
  .parameter-item {
    display: grid;
    grid-template-columns: 1fr auto;
    gap: v.$spacing-md;
    align-items: start;
    padding: v.$spacing-md;
    background-color: rgba(v.$color-primary, 0.05);
    border-radius: v.$border-radius-sm;
    margin-bottom: v.$spacing-md;

    .parameter-fields {
      display: grid;
      gap: v.$spacing-sm;
    }

    .remove-parameter {
      background-color: transparent;
      border: none;
      color: v.$color-error;
      cursor: pointer;
      padding: v.$spacing-xs;
      border-radius: v.$border-radius-sm;
      transition: background-color 0.3s ease;

      &:hover {
        background-color: rgba(v.$color-error, 0.1);
      }
    }
  }
}

.add-parameter {
  width: 100%;
  padding: v.$spacing-sm;
  background-color: transparent;
  border: 2px dashed v.$color-border;
  border-radius: v.$border-radius-sm;
  color: v.$color-text-secondary;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    border-color: v.$color-primary;
    color: v.$color-primary;
  }
}

.test-section {
  margin-top: v.$spacing-xl;

  .test-input {
    margin-bottom: v.$spacing-lg;
  }

  .test-output {
    background-color: v.$color-surface-variant;
    border-radius: v.$border-radius-sm;
    padding: v.$spacing-md;
    min-height: 100px;
    white-space: pre-wrap;
    font-family: monospace;
  }
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: v.$spacing-md;
  margin-top: v.$spacing-xl;

  button {
    padding: v.$spacing-sm v.$spacing-lg;
    border-radius: v.$border-radius-sm;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;

    &.secondary {
      background-color: transparent;
      border: 1px solid v.$color-border;
      color: v.$color-text-secondary;

      &:hover {
        background-color: rgba(v.$color-primary, 0.05);
        border-color: v.$color-primary;
        color: v.$color-primary;
      }
    }

    &.primary {
      background-color: v.$color-primary;
      border: none;
      color: v.$color-surface;

      &:hover {
        background-color: darken(v.$color-primary, 10%);
      }

      &:disabled {
        background-color: v.$color-disabled;
        cursor: not-allowed;
      }
    }
  }
}

@media (max-width: v.$breakpoint-md) {
  .custom-model {
    padding: v.$spacing-md;
  }

  .page-title {
    font-size: 1.5rem;
  }

  .form-section {
    padding: v.$spacing-md;

    h2 {
      font-size: 1.25rem;
    }
  }

  .parameters-list {
    .parameter-item {
      grid-template-columns: 1fr;
      gap: v.$spacing-sm;

      .remove-parameter {
        justify-self: end;
      }
    }
  }

  .form-actions {
    flex-direction: column;

    button {
      width: 100%;
    }
  }
}
