package com.debate_ournament.users.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.debate_ournament.dto.common.ApiResponse;
import com.debate_ournament.users.entity.UserLevel;
import com.debate_ournament.users.service.UserLevelService;
import com.debate_ournament.users.service.UserService;

/**
 * 用户等级控制器
 */
@RestController
@RequestMapping("/api/users/level")
public class UserLevelController {

    @Autowired
    private UserLevelService userLevelService;

    @Autowired
    private UserService userService;

    /**
     * 获取当前用户的等级信息
     */
    @GetMapping("/my")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<UserLevel>> getMyLevel(Authentication authentication) {
        try {
            String username = authentication.getName();
            UserLevel userLevel = userLevelService.getUserLevelByUsername(username)
                    .orElseThrow(() -> new RuntimeException("用户等级信息不存在"));

            return ResponseEntity.ok(ApiResponse.success(userLevel));
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("获取用户等级信息失败: " + e.getMessage()));
        }
    }

    /**
     * 根据用户名获取等级信息
     */
    @GetMapping("/user/{username}")
    public ResponseEntity<ApiResponse<UserLevel>> getUserLevel(@PathVariable String username) {
        try {
            UserLevel userLevel = userLevelService.getUserLevelByUsername(username)
                    .orElseThrow(() -> new RuntimeException("用户等级信息不存在"));

            return ResponseEntity.ok(ApiResponse.success(userLevel));
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("获取用户等级信息失败: " + e.getMessage()));
        }
    }

    /**
     * 获取等级排行榜
     */
    @GetMapping("/leaderboard")
    public ResponseEntity<ApiResponse<List<UserLevel>>> getLevelLeaderboard() {
        try {
            List<UserLevel> leaderboard = userLevelService.getLevelLeaderboard();
            return ResponseEntity.ok(ApiResponse.success(leaderboard));
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("获取等级排行榜失败: " + e.getMessage()));
        }
    }

    /**
     * 获取胜率排行榜
     */
    @GetMapping("/leaderboard/win-rate")
    public ResponseEntity<ApiResponse<List<UserLevel>>> getWinRateLeaderboard(
            @RequestParam(defaultValue = "5") int minDebates) {
        try {
            List<UserLevel> leaderboard = userLevelService.getWinRateLeaderboard(minDebates);
            return ResponseEntity.ok(ApiResponse.success(leaderboard));
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("获取胜率排行榜失败: " + e.getMessage()));
        }
    }

    /**
     * 获取连胜排行榜
     */
    @GetMapping("/leaderboard/streak")
    public ResponseEntity<ApiResponse<List<UserLevel>>> getCurrentStreakLeaderboard() {
        try {
            List<UserLevel> leaderboard = userLevelService.getCurrentStreakLeaderboard();
            return ResponseEntity.ok(ApiResponse.success(leaderboard));
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("获取连胜排行榜失败: " + e.getMessage()));
        }
    }

    /**
     * 获取当前用户的排名
     */
    @GetMapping("/my/rank")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Long>> getMyRank(Authentication authentication) {
        try {
            String username = authentication.getName();
            Long userId = userService.findByUsername(username)
                    .orElseThrow(() -> new RuntimeException("用户不存在"))
                    .getId();

            long rank = userLevelService.getUserRank(userId);
            return ResponseEntity.ok(ApiResponse.success(rank));
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("获取用户排名失败: " + e.getMessage()));
        }
    }

    /**
     * 获取等级统计信息
     */
    @GetMapping("/statistics")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getLevelStatistics() {
        try {
            Map<String, Object> statistics = new HashMap<>();

            // 获取各等级区间的用户数量
            statistics.put("beginner", userLevelService.getUserCountByLevelRange(1, 5));     // 新手
            statistics.put("intermediate", userLevelService.getUserCountByLevelRange(6, 15)); // 初级
            statistics.put("advanced", userLevelService.getUserCountByLevelRange(16, 30));    // 进阶
            statistics.put("expert", userLevelService.getUserCountByLevelRange(31, 50));      // 专家
            statistics.put("master", userLevelService.getUserCountByLevelRange(51, 100));     // 大师

            // 获取平均等级和最高等级
            statistics.put("averageLevel", userLevelService.getAverageLevel());
            statistics.put("maxLevel", userLevelService.getMaxLevel());

            return ResponseEntity.ok(ApiResponse.success(statistics));
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("获取等级统计信息失败: " + e.getMessage()));
        }
    }

    /**
     * 初始化用户等级信息（管理员功能）
     */
    @PostMapping("/initialize/{userId}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<UserLevel>> initializeUserLevel(@PathVariable Long userId) {
        try {
            UserLevel userLevel = userLevelService.initializeUserLevel(userId);
            return ResponseEntity.ok(ApiResponse.success(userLevel));
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("初始化用户等级失败: " + e.getMessage()));
        }
    }

    /**
     * 手动为用户添加经验值（管理员功能）
     */
    @PostMapping("/add-experience/{userId}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> addExperience(
            @PathVariable Long userId,
            @RequestParam long experience) {
        try {
            if (experience <= 0) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("经验值必须大于0"));
            }

            boolean leveledUp = userLevelService.addExperience(userId, experience);

            Map<String, Object> result = new HashMap<>();
            result.put("leveledUp", leveledUp);
            result.put("experienceAdded", experience);

            return ResponseEntity.ok(ApiResponse.success(result));
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("添加经验值失败: " + e.getMessage()));
        }
    }

    /**
     * 计算辩论经验值
     */
    @GetMapping("/calculate-experience")
    public ResponseEntity<ApiResponse<Long>> calculateExperience(
            @RequestParam boolean isWin,
            @RequestParam(defaultValue = "1") int opponentLevel,
            @RequestParam(defaultValue = "30") int debateDuration) {
        try {
            long experience = userLevelService.calculateExperience(isWin, opponentLevel, debateDuration);
            return ResponseEntity.ok(ApiResponse.success(experience));
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("计算经验值失败: " + e.getMessage()));
        }
    }

    /**
     * 记录辩论结果
     */
    @PostMapping("/record-debate")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> recordDebateResult(
            @RequestParam boolean isWin,
            @RequestParam(defaultValue = "1") int opponentLevel,
            @RequestParam(defaultValue = "30") int debateDuration,
            Authentication authentication) {
        try {
            String username = authentication.getName();
            Long userId = userService.findByUsername(username)
                    .orElseThrow(() -> new RuntimeException("用户不存在"))
                    .getId();

            // 计算经验值
            long experienceGained = userLevelService.calculateExperience(isWin, opponentLevel, debateDuration);

            // 记录辩论结果
            userLevelService.recordDebateResult(userId, isWin, experienceGained);

            // 获取更新后的用户等级信息
            UserLevel updatedLevel = userLevelService.getUserLevel(userId)
                    .orElseThrow(() -> new RuntimeException("用户等级信息不存在"));

            Map<String, Object> result = new HashMap<>();
            result.put("experienceGained", experienceGained);
            result.put("userLevel", updatedLevel);

            return ResponseEntity.ok(ApiResponse.success(result));
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("记录辩论结果失败: " + e.getMessage()));
        }
    }
}
