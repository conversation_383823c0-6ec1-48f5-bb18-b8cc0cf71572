package com.debate_ournament.users.service;

import java.io.IOException;
import java.nio.file.DirectoryStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;
import java.util.concurrent.CompletableFuture;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import com.debate_ournament.users.repository.UserProfileRepository;

/**
 * 文件清理服务
 * 负责清理孤立的上传文件
 */
@Service
public class FileCleanupService {

    private static final Logger logger = LoggerFactory.getLogger(FileCleanupService.class);

    @Value("${app.upload.path:uploads}")
    private String uploadPath;

    @Value("${app.cleanup.orphaned-files.enabled:true}")
    private boolean cleanupEnabled;

    @Value("${app.cleanup.orphaned-files.max-age-days:7}")
    private int maxAgeDays;

    @Autowired
    private UserProfileRepository userProfileRepository;

    /**
     * 定时清理孤立文件
     * 每天凌晨2点执行
     */
    @Scheduled(cron = "0 0 2 * * ?")
    @Async
    public void scheduleOrphanedFileCleanup() {
        if (!cleanupEnabled) {
            logger.info("文件清理功能已禁用");
            return;
        }

        logger.info("开始执行定时文件清理任务");
        try {
            CompletableFuture<Integer> result = cleanupOrphanedFiles();
            int deletedCount = result.get();
            logger.info("定时文件清理任务完成，删除了 {} 个孤立文件", deletedCount);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            logger.error("定时文件清理任务被中断", e);
        } catch (Exception e) {
            logger.error("定时文件清理任务执行失败", e);
        }
    }

    /**
     * 手动执行文件清理
     * @return 删除的文件数量
     */
    @Async
    public CompletableFuture<Integer> cleanupOrphanedFiles() {
        logger.info("开始清理孤立的上传文件");
        int deletedCount = 0;

        try {
            // 清理头像文件
            deletedCount += cleanupOrphanedAvatars();

            // 清理临时文件
            deletedCount += cleanupTempFiles();

            logger.info("文件清理完成，共删除 {} 个文件", deletedCount);

        } catch (RuntimeException e) {
            logger.error("文件清理过程中发生运行时错误", e);
        }

        return CompletableFuture.completedFuture(deletedCount);
    }

    /**
     * 清理孤立的头像文件
     * @return 删除的文件数量
     */
    private int cleanupOrphanedAvatars() {
        int deletedCount = 0;
        Path avatarDir = Paths.get(uploadPath, "avatars");

        if (!Files.exists(avatarDir)) {
            logger.debug("头像目录不存在：{}", avatarDir);
            return 0;
        }

        try (DirectoryStream<Path> stream = Files.newDirectoryStream(avatarDir)) {
            // 获取数据库中所有的头像文件名
            List<String> referencedFiles = userProfileRepository.findAllAvatarFileNames();

            for (Path file : stream) {
                if (Files.isRegularFile(file)) {
                    String fileName = file.getFileName().toString();

                    // 跳过说明文件和 .gitkeep 文件
                    if (fileName.equals("README.txt") || fileName.equals(".gitkeep")) {
                        continue;
                    }

                    // 检查文件是否被数据库引用
                    boolean isReferenced = referencedFiles.stream()
                            .anyMatch(ref -> ref != null && ref.contains(fileName));

                    if (!isReferenced) {
                        // 检查文件年龄
                        LocalDateTime fileTime = LocalDateTime.ofInstant(
                                Files.getLastModifiedTime(file).toInstant(),
                                ZoneId.systemDefault()
                        );

                        if (fileTime.isBefore(LocalDateTime.now().minusDays(maxAgeDays))) {
                            try {
                                Files.delete(file);
                                deletedCount++;
                                logger.debug("删除孤立的头像文件：{}", fileName);
                            } catch (IOException e) {
                                logger.warn("删除文件失败：{}", fileName, e);
                            }
                        }
                    }
                }
            }
        } catch (IOException e) {
            logger.error("清理头像文件时发生错误", e);
        }

        return deletedCount;
    }

    /**
     * 清理临时文件
     * @return 删除的文件数量
     */
    private int cleanupTempFiles() {
        int deletedCount = 0;
        Path tempDir = Paths.get(uploadPath, "temp");

        if (!Files.exists(tempDir)) {
            logger.debug("临时文件目录不存在：{}", tempDir);
            return 0;
        }

        try (DirectoryStream<Path> stream = Files.newDirectoryStream(tempDir)) {
            LocalDateTime cutoffTime = LocalDateTime.now().minusHours(24); // 删除24小时前的临时文件

            for (Path file : stream) {
                if (Files.isRegularFile(file)) {
                    LocalDateTime fileTime = LocalDateTime.ofInstant(
                            Files.getLastModifiedTime(file).toInstant(),
                            ZoneId.systemDefault()
                    );

                    if (fileTime.isBefore(cutoffTime)) {
                        try {
                            Files.delete(file);
                            deletedCount++;
                            logger.debug("删除过期的临时文件：{}", file.getFileName());
                        } catch (IOException e) {
                            logger.warn("删除临时文件失败：{}", file.getFileName(), e);
                        }
                    }
                }
            }
        } catch (IOException e) {
            logger.error("清理临时文件时发生错误", e);
        }

        return deletedCount;
    }

    /**
     * 获取目录磁盘使用情况
     * @return 使用情况信息
     */
    public FileUsageInfo getFileUsageInfo() {
        FileUsageInfo info = new FileUsageInfo();
        Path uploadDir = Paths.get(uploadPath);

        if (Files.exists(uploadDir)) {
            try {
                info.setTotalSize(calculateDirectorySize(uploadDir));
                info.setAvatarCount(countFilesInDirectory(Paths.get(uploadPath, "avatars")));
                info.setTempFileCount(countFilesInDirectory(Paths.get(uploadPath, "temp")));
            } catch (IOException e) {
                logger.error("计算文件使用情况时发生错误", e);
            }
        }

        return info;
    }

    /**
     * 计算目录大小
     * @param directory 目录路径
     * @return 目录大小（字节）
     * @throws IOException IO异常
     */
    private long calculateDirectorySize(Path directory) throws IOException {
        if (!Files.exists(directory)) {
            return 0;
        }

        return Files.walk(directory)
                .filter(Files::isRegularFile)
                .mapToLong(file -> {
                    try {
                        return Files.size(file);
                    } catch (IOException e) {
                        logger.warn("获取文件大小失败：{}", file, e);
                        return 0;
                    }
                })
                .sum();
    }

    /**
     * 计算目录中的文件数量
     * @param directory 目录路径
     * @return 文件数量
     * @throws IOException IO异常
     */
    private int countFilesInDirectory(Path directory) throws IOException {
        if (!Files.exists(directory)) {
            return 0;
        }

        try (DirectoryStream<Path> stream = Files.newDirectoryStream(directory)) {
            int count = 0;
            for (Path file : stream) {
                if (Files.isRegularFile(file)) {
                    count++;
                }
            }
            return count;
        }
    }

    /**
     * 文件使用情况信息类
     */
    public static class FileUsageInfo {
        private long totalSize;
        private int avatarCount;
        private int tempFileCount;

        public long getTotalSize() {
            return totalSize;
        }

        public void setTotalSize(long totalSize) {
            this.totalSize = totalSize;
        }

        public int getAvatarCount() {
            return avatarCount;
        }

        public void setAvatarCount(int avatarCount) {
            this.avatarCount = avatarCount;
        }

        public int getTempFileCount() {
            return tempFileCount;
        }

        public void setTempFileCount(int tempFileCount) {
            this.tempFileCount = tempFileCount;
        }

        public String getTotalSizeMB() {
            return String.format("%.2f MB", totalSize / 1024.0 / 1024.0);
        }
    }
}
