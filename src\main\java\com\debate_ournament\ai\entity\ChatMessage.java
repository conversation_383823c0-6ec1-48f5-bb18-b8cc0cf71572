package com.debate_ournament.ai.entity;

import java.time.LocalDateTime;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * AI聊天消息实体类
 * 存储用户与AI的对话记录
 */
@Entity
@Table(name = "chat_messages")
public class ChatMessage {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 会话ID，用于关联同一次对话的多条消息
     */
    @NotBlank(message = "会话ID不能为空")
    @Column(name = "session_id", nullable = false, length = 100)
    private String sessionId;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    @Column(name = "user_id", nullable = false)
    private Long userId;

    /**
     * 消息角色：user(用户) / assistant(AI助手) / system(系统)
     */
    @NotBlank(message = "消息角色不能为空")
    @Column(name = "role", nullable = false, length = 20)
    private String role;

    /**
     * 消息内容
     */
    @NotBlank(message = "消息内容不能为空")
    @Column(name = "content", nullable = false, columnDefinition = "TEXT")
    private String content;

    /**
     * 使用的AI提供商
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "ai_provider", length = 50)
    private AiProvider aiProvider;

    /**
     * 使用的模型名称
     */
    @Column(name = "model_name", length = 100)
    private String modelName;

    /**
     * token消耗数量
     */
    @Column(name = "token_count")
    private Integer tokenCount;

    /**
     * AI配置ID
     */
    @Column(name = "ai_config_id")
    private Long aiConfigId;

    /**
     * 响应时间（毫秒）
     */
    @Column(name = "response_time")
    private Long responseTime;

    /**
     * 创建时间
     */
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;

    /**
     * 是否为系统消息
     */
    @Column(name = "is_system_message", nullable = false)
    private Boolean isSystemMessage = false;

    /**
     * 消息状态：pending, success, failed
     */
    @Column(name = "status", length = 20)
    private String status = "success";

    /**
     * 错误信息（如果有）
     */
    @Column(name = "error_message", length = 500)
    private String errorMessage;

    // 默认构造函数
    public ChatMessage() {
        this.createdAt = LocalDateTime.now();
    }

    // 构造函数
    public ChatMessage(String sessionId, Long userId, String role, String content) {
        this();
        this.sessionId = sessionId;
        this.userId = userId;
        this.role = role;
        this.content = content;
    }

    // 静态工厂方法
    public static ChatMessage createUserMessage(String sessionId, Long userId, String content) {
        return new ChatMessage(sessionId, userId, "user", content);
    }

    public static ChatMessage createAssistantMessage(String sessionId, Long userId, String content) {
        return new ChatMessage(sessionId, userId, "assistant", content);
    }

    public static ChatMessage createSystemMessage(String sessionId, Long userId, String content) {
        ChatMessage message = new ChatMessage(sessionId, userId, "system", content);
        message.setIsSystemMessage(true);
        return message;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getRole() {
        return role;
    }

    public void setRole(String role) {
        this.role = role;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public AiProvider getAiProvider() {
        return aiProvider;
    }

    public void setAiProvider(AiProvider aiProvider) {
        this.aiProvider = aiProvider;
    }

    public String getModelName() {
        return modelName;
    }

    public void setModelName(String modelName) {
        this.modelName = modelName;
    }

    public Integer getTokenCount() {
        return tokenCount;
    }

    public void setTokenCount(Integer tokenCount) {
        this.tokenCount = tokenCount;
    }

    public Long getAiConfigId() {
        return aiConfigId;
    }

    public void setAiConfigId(Long aiConfigId) {
        this.aiConfigId = aiConfigId;
    }

    public Long getResponseTime() {
        return responseTime;
    }

    public void setResponseTime(Long responseTime) {
        this.responseTime = responseTime;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public Boolean getIsSystemMessage() {
        return isSystemMessage;
    }

    public void setIsSystemMessage(Boolean isSystemMessage) {
        this.isSystemMessage = isSystemMessage;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    @Override
    public String toString() {
        return "ChatMessage{" +
                "id=" + id +
                ", sessionId='" + sessionId + '\'' +
                ", userId=" + userId +
                ", role='" + role + '\'' +
                ", aiProvider=" + aiProvider +
                ", modelName='" + modelName + '\'' +
                ", tokenCount=" + tokenCount +
                ", status='" + status + '\'' +
                ", createdAt=" + createdAt +
                '}';
    }
}
