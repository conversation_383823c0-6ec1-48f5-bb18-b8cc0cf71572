import request from './index'

/**
 * 用户等级系统相关API
 */

// 获取用户等级信息
export function getUserLevel(userId) {
  return request({
    url: `/api/user-level/${userId}`,
    method: 'get'
  })
}

// 获取当前用户等级信息
export function getCurrentUserLevel() {
  return request({
    url: '/api/user-level/current',
    method: 'get'
  })
}

// 添加经验值
export function addExperience(experienceData) {
  return request({
    url: '/api/user-level/experience',
    method: 'post',
    data: experienceData
  })
}

// 更新辩论统计
export function updateDebateStats(statsData) {
  return request({
    url: '/api/user-level/debate-stats',
    method: 'put',
    data: statsData
  })
}

// 获取用户排行榜
export function getLeaderboard(type = 'level', page = 0, size = 20) {
  return request({
    url: '/api/user-level/leaderboard',
    method: 'get',
    params: {
      type,
      page,
      size
    }
  })
}

// 获取成就列表
export function getAchievements(userId) {
  return request({
    url: `/api/user-level/${userId}/achievements`,
    method: 'get'
  })
}

// 解锁成就
export function unlockAchievement(achievementData) {
  return request({
    url: '/api/user-level/achievement',
    method: 'post',
    data: achievementData
  })
}

// 获取称号列表
export function getTitles(userId) {
  return request({
    url: `/api/user-level/${userId}/titles`,
    method: 'get'
  })
}

// 设置活跃称号
export function setActiveTitle(titleId) {
  return request({
    url: `/api/user-level/title/${titleId}/activate`,
    method: 'post'
  })
}

// 获取等级经验配置
export function getLevelConfig() {
  return request({
    url: '/api/user-level/config',
    method: 'get'
  })
}
