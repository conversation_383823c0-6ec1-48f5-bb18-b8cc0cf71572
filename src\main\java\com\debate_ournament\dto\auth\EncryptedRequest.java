package com.debate_ournament.dto.auth;

import jakarta.validation.constraints.NotBlank;

/**
 * 加密请求DTO
 */
public class EncryptedRequest {

    @NotBlank(message = "加密数据不能为空")
    private String data;

    @NotBlank(message = "签名不能为空")
    private String signature;

    @NotBlank(message = "密钥ID不能为空")
    private String keyId;

    private String sessionKey; // 可选，新会话时需要

    public EncryptedRequest() {}

    public EncryptedRequest(String data, String signature, String keyId, String sessionKey) {
        this.data = data;
        this.signature = signature;
        this.keyId = keyId;
        this.sessionKey = sessionKey;
    }

    // Getters and Setters
    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public String getSignature() {
        return signature;
    }

    public void setSignature(String signature) {
        this.signature = signature;
    }

    public String getKeyId() {
        return keyId;
    }

    public void setKeyId(String keyId) {
        this.keyId = keyId;
    }

    public String getSessionKey() {
        return sessionKey;
    }

    public void setSessionKey(String sessionKey) {
        this.sessionKey = sessionKey;
    }

    @Override
    public String toString() {
        return "EncryptedRequest{" +
                "keyId='" + keyId + '\'' +
                ", hasData=" + (data != null) +
                ", hasSignature=" + (signature != null) +
                ", hasSessionKey=" + (sessionKey != null) +
                '}';
    }
}
