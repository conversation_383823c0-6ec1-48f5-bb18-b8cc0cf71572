package com.debate_ournament.users.controller;

import java.util.HashMap;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.debate_ournament.dto.common.ApiResponse;
import com.debate_ournament.users.service.EncryptionService;
import com.debate_ournament.users.service.EncryptionService.DecryptResult;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;

/**
 * 加密相关控制器
 * 提供RSA/AES混合加密通信、密钥管理和加密状态监控功能
 *
 * <AUTHOR> Debate Tournament Platform
 * @version 1.0
 * @since 2024
 */
@RestController
@RequestMapping("/auth/encryption")
@Tag(name = "加密服务", description = "提供加密通信、密钥管理和监控功能")
public class EncryptionController {

    private static final Logger logger = LoggerFactory.getLogger(EncryptionController.class);

    private final EncryptionService encryptionService;

    @Autowired
    public EncryptionController(EncryptionService encryptionService) {
        this.encryptionService = encryptionService;
    }

    /**
     * 获取公钥
     * 客户端使用此接口获取RSA公钥进行加密通信
     *
     * @return 包含公钥信息的响应
     */
    @GetMapping("/key")
    @Operation(summary = "获取RSA公钥", description = "获取用于加密通信的RSA公钥信息")

    public ResponseEntity<ApiResponse<Map<String, Object>>> getPublicKey() {
        try {
            logger.debug("收到获取公钥请求");
            Map<String, Object> publicKeyInfo = encryptionService.getPublicKey();
            logger.info("成功返回公钥信息，密钥ID: {}", publicKeyInfo.get("keyId"));
            return ResponseEntity.ok(ApiResponse.success("获取公钥成功", publicKeyInfo));
        } catch (Exception e) {
            logger.error("获取公钥失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("获取公钥失败，请稍后重试"));
        }
    }

    /**
     * 获取加密缓存统计信息
     * 用于系统监控和性能分析
     *
     * @return 包含缓存统计信息的响应
     */
    @GetMapping("/stats")
    @Operation(summary = "获取加密缓存统计", description = "获取加密服务的缓存使用统计信息，用于性能监控")

    public ResponseEntity<ApiResponse<Map<String, Object>>> getCacheStats() {
        try {
            logger.debug("收到获取缓存统计请求");
            Map<String, Object> stats = encryptionService.getCacheStats();
            logger.debug("成功返回缓存统计信息: {}", stats);
            return ResponseEntity.ok(ApiResponse.success("获取统计信息成功", stats));
        } catch (Exception e) {
            logger.error("获取统计信息失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("获取统计信息失败，请稍后重试"));
        }
    }

    /**
     * 测试加密通信
     * 验证端到端的加密解密流程是否正常工作
     *
     * @param requestBody 加密的请求体
     * @return 加密的响应数据
     */
    @PostMapping("/test")
    @Operation(summary = "测试加密通信", description = "测试端到端的加密解密通信流程")

    public ResponseEntity<?> testEncryption(
            @Parameter(description = "加密的测试请求数据", required = true)
            @RequestBody Map<String, Object> requestBody) {

        return handleEncryptedRequest(requestBody, decryptedData -> {
            logger.info("收到加密测试请求，解密数据: {}", decryptedData);

            // 验证解密数据的完整性
            if (decryptedData == null || decryptedData.isEmpty()) {
                throw new IllegalArgumentException("解密后的数据为空");
            }

            // 构造测试响应
            Map<String, Object> responseData = new HashMap<>();
            responseData.put("status", "success");
            responseData.put("message", "加密通信测试成功");
            responseData.put("receivedData", decryptedData);
            responseData.put("serverTime", System.currentTimeMillis());
            responseData.put("testId", generateTestId());

            logger.info("加密测试成功，生成响应数据");
            return responseData;
        });
    }

    /**
     * 处理加密请求的通用方法
     * 提供标准化的加密请求处理流程，包括验证、解密、处理和加密响应
     *
     * @param requestBody 加密请求体
     * @param processor 解密后的处理函数
     * @return 加密响应
     */
    protected ResponseEntity<?> handleEncryptedRequest(
            Map<String, Object> requestBody,
            EncryptedRequestProcessor processor) {
        try {
            // 输入验证
            if (requestBody == null || requestBody.isEmpty()) {
                logger.warn("收到空的请求体");
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("请求体不能为空"));
            }

            // 检查是否为加密请求
            if (!encryptionService.isEncryptedRequest(requestBody)) {
                logger.warn("收到未加密的请求: {}", requestBody.keySet());
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("请求必须使用加密格式"));
            }

            logger.debug("开始处理加密请求");

            // 解密请求
            DecryptResult decryptResult = encryptionService.decryptRequestWithDetails(requestBody);
            Map<String, Object> decryptedData = decryptResult.getData();

            logger.debug("请求解密成功，keyId: {}", decryptResult.getKeyId());

            // 处理解密后的数据
            Map<String, Object> responseData = processor.process(decryptedData);

            if (responseData == null) {
                logger.warn("处理器返回了空的响应数据");
                return ResponseEntity.internalServerError()
                        .body(ApiResponse.error("处理请求时发生错误"));
            }

            // 加密响应
            Map<String, Object> encryptedResponse = encryptionService.encryptResponse(
                    responseData,
                    decryptResult.getKeyId(),
                    decryptResult.getSessionKey());

            logger.debug("响应加密成功，返回给客户端");
            return ResponseEntity.ok(encryptedResponse);

        } catch (IllegalArgumentException e) {
            logger.warn("请求参数错误: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("请求参数错误: " + e.getMessage()));
        } catch (SecurityException e) {
            logger.error("安全验证失败: {}", e.getMessage());
            return ResponseEntity.status(403)
                    .body(ApiResponse.error("安全验证失败"));
        } catch (Exception e) {
            logger.error("处理加密请求失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("处理请求失败，请稍后重试"));
        }
    }

    /**
     * 生成测试ID
     *
     * @return 唯一的测试标识符
     */
    private String generateTestId() {
        return "test_" + System.currentTimeMillis() + "_" +
               Integer.toHexString((int)(Math.random() * 0xFFFF));
    }

    /**
     * 加密请求处理器接口
     * 用于处理解密后的业务逻辑
     */
    @FunctionalInterface
    protected interface EncryptedRequestProcessor {
        /**
         * 处理解密后的数据
         *
         * @param decryptedData 解密后的请求数据
         * @return 处理结果，将被加密后返回给客户端
         * @throws Exception 处理过程中的任何异常
         */
        Map<String, Object> process(Map<String, Object> decryptedData) throws Exception;
    }
}
