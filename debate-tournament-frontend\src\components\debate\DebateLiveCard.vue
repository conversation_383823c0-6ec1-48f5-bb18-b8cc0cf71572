<template>
  <div class="debate-live-card">
    <div class="debate-live-card__status" :class="{ 'is-live': debate.status === 'live' }">
      <span class="status-indicator"></span>
      {{ debate.status === 'live' ? '直播中' : '已结束' }}
    </div>

    <h3 class="debate-live-card__title">{{ debate.title }}</h3>

    <div class="debate-live-card__stats">
      <div class="vote-stats">
        <div class="vote-bar">
          <div
            class="vote-bar__support"
            :style="{ width: `${debate.supportPercent}%` }"
          >
            {{ debate.supportPercent }}%
          </div>
          <div
            class="vote-bar__oppose"
            :style="{ width: `${debate.opposePercent}%` }"
          >
            {{ debate.opposePercent }}%
          </div>
        </div>
        <div class="vote-labels">
          <span class="vote-label vote-label--support">支持</span>
          <span class="vote-label vote-label--oppose">反对</span>
        </div>
      </div>

      <div class="debate-live-card__info">
        <div class="info-item">
          <el-icon><View /></el-icon>
          <span>{{ formatNumber(debate.viewers) }}</span>
        </div>
        <div class="info-item">
          <el-icon><Timer /></el-icon>
          <span>{{ debate.timeRemaining }}</span>
        </div>
      </div>
    </div>

    <div class="debate-live-card__action">
      <el-button type="primary" @click="watchDebate">观看直播</el-button>
    </div>
  </div>
</template>

<script>
import { defineComponent } from 'vue';

export default defineComponent({
  name: 'DebateLiveCard',

  props: {
    debate: {
      type: Object,
      required: true
    }
  },

  setup(props, { emit }) {
    /**
     * 格式化数字，添加千位分隔符
     * @param {number} num - 要格式化的数字
     * @returns {string} 格式化后的字符串
     */
    const formatNumber = (num) => {
      return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    };

    /**
     * 观看辩论直播
     */
    const watchDebate = () => {
      emit('watch', props.debate.id);
    };

    return {
      formatNumber,
      watchDebate
    };
  }
});
</script>

<style lang="scss" scoped>
.debate-live-card {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  }

  &__status {
    display: inline-flex;
    align-items: center;
    padding: 4px 8px;
    background-color: #f1f1f1;
    border-radius: 4px;
    font-size: 12px;
    margin-bottom: 12px;

    .status-indicator {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background-color: #999;
      margin-right: 6px;
    }

    &.is-live {
      background-color: rgba(220, 53, 69, 0.1);
      color: #dc3545;

      .status-indicator {
        background-color: #dc3545;
        animation: pulse 1.5s infinite;
      }
    }
  }

  &__title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 16px;
    color: #333;
  }

  &__stats {
    margin-bottom: 16px;
  }

  &__info {
    display: flex;
    justify-content: space-between;
    margin-top: 12px;

    .info-item {
      display: flex;
      align-items: center;
      color: #666;

      .el-icon {
        margin-right: 4px;
        font-size: 16px;
      }
    }
  }

  &__action {
    margin-top: 16px;
    text-align: center;
  }
}

.vote-stats {
  .vote-bar {
    display: flex;
    height: 24px;
    border-radius: 4px;
    overflow: hidden;

    &__support {
      background-color: #4caf50;
      color: white;
      text-align: center;
      line-height: 24px;
      font-size: 12px;
      font-weight: 600;
      min-width: 24px;
    }

    &__oppose {
      background-color: #f44336;
      color: white;
      text-align: center;
      line-height: 24px;
      font-size: 12px;
      font-weight: 600;
      min-width: 24px;
    }
  }

  .vote-labels {
    display: flex;
    justify-content: space-between;
    margin-top: 4px;
    font-size: 12px;

    .vote-label {
      &--support {
        color: #4caf50;
      }

      &--oppose {
        color: #f44336;
      }
    }
  }
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}
</style>
