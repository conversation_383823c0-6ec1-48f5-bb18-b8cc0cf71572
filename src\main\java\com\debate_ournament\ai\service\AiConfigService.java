package com.debate_ournament.ai.service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.debate_ournament.ai.controller.AiConfigController.ConfigUpdateRequest;
import com.debate_ournament.ai.entity.AiConfig;
import com.debate_ournament.ai.entity.AiProvider;
import com.debate_ournament.ai.repository.AiConfigRepository;

/**
 * AI配置服务
 * 处理AI配置的业务逻辑
 */
@Service
@Transactional
public class AiConfigService {

    private static final Logger logger = LoggerFactory.getLogger(AiConfigService.class);

    private final AiConfigRepository aiConfigRepository;
    private final AiChatService aiChatService;

    public AiConfigService(AiConfigRepository aiConfigRepository, AiChatService aiChatService) {
        this.aiConfigRepository = aiConfigRepository;
        this.aiChatService = aiChatService;
    }

    /**
     * 创建AI配置
     */
    public AiConfig createConfig(AiConfig config) {
        // 设置创建时间
        config.setCreatedAt(LocalDateTime.now());
        config.setUpdatedAt(LocalDateTime.now());

        // 如果设置为默认配置，需要清除其他默认配置
        if (Boolean.TRUE.equals(config.getIsDefault())) {
            clearDefaultForProvider(config.getProvider());
        }

        AiConfig savedConfig = aiConfigRepository.save(config);
        logger.info("创建AI配置成功: {} - {}", savedConfig.getProvider(), savedConfig.getConfigName());

        return savedConfig;
    }

    /**
     * 更新AI配置
     */
    public AiConfig updateConfig(Long id, ConfigUpdateRequest request) {
        Optional<AiConfig> configOpt = aiConfigRepository.findById(id);
        if (configOpt.isEmpty()) {
            throw new RuntimeException("配置不存在: " + id);
        }

        AiConfig config = configOpt.get();

        // 更新字段
        if (request.getConfigName() != null) {
            config.setConfigName(request.getConfigName());
        }
        if (request.getApiKey() != null) {
            config.setApiKey(request.getApiKey());
        }
        if (request.getBaseUrl() != null) {
            config.setBaseUrl(request.getBaseUrl());
        }
        if (request.getModelName() != null) {
            config.setModelName(request.getModelName());
        }
        if (request.getSystemPrompt() != null) {
            config.setSystemPrompt(request.getSystemPrompt());
        }
        if (request.getTemperature() != null) {
            config.setTemperature(request.getTemperature());
        }
        if (request.getMaxTokens() != null) {
            config.setMaxTokens(request.getMaxTokens());
        }
        if (request.getEnabled() != null) {
            config.setEnabled(request.getEnabled());
        }

        // 处理默认配置设置
        if (Boolean.TRUE.equals(request.getIsDefault())) {
            clearDefaultForProvider(config.getProvider());
            config.setIsDefault(true);
        } else if (Boolean.FALSE.equals(request.getIsDefault())) {
            config.setIsDefault(false);
        }

        config.setUpdatedAt(LocalDateTime.now());

        AiConfig updatedConfig = aiConfigRepository.save(config);
        logger.info("更新AI配置成功: {} - {}", updatedConfig.getProvider(), updatedConfig.getConfigName());

        return updatedConfig;
    }

    /**
     * 删除AI配置
     */
    public void deleteConfig(Long id) {
        Optional<AiConfig> configOpt = aiConfigRepository.findById(id);
        if (configOpt.isEmpty()) {
            throw new RuntimeException("配置不存在: " + id);
        }

        AiConfig config = configOpt.get();

        // 如果是默认配置，不允许删除
        if (Boolean.TRUE.equals(config.getIsDefault())) {
            throw new RuntimeException("不能删除默认配置，请先设置其他配置为默认");
        }

        aiConfigRepository.delete(config);
        logger.info("删除AI配置成功: {} - {}", config.getProvider(), config.getConfigName());
    }

    /**
     * 根据ID获取配置
     */
    @Transactional(readOnly = true)
    public AiConfig getConfigById(Long id) {
        return aiConfigRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("配置不存在: " + id));
    }

    /**
     * 获取配置列表（分页）
     */
    @Transactional(readOnly = true)
    public Page<AiConfig> getConfigs(Pageable pageable, AiProvider provider) {
        if (provider != null) {
            return aiConfigRepository.findByProviderAndEnabledTrue(provider, pageable);
        }
        return aiConfigRepository.findByEnabledTrueOrderByProviderAscConfigNameAsc(pageable);
    }

    /**
     * 根据提供商获取配置列表
     */
    @Transactional(readOnly = true)
    public List<AiConfig> getConfigsByProvider(AiProvider provider) {
        return aiConfigRepository.findByProviderAndEnabledTrue(provider);
    }

    /**
     * 获取默认配置
     */
    @Transactional(readOnly = true)
    public AiConfig getDefaultConfig() {
        return aiConfigRepository.findByIsDefaultTrueAndEnabledTrue()
                .orElseThrow(() -> new RuntimeException("未找到默认配置"));
    }

    /**
     * 设置默认配置
     */
    public void setDefaultConfig(Long id) {
        Optional<AiConfig> configOpt = aiConfigRepository.findById(id);
        if (configOpt.isEmpty()) {
            throw new RuntimeException("配置不存在: " + id);
        }

        AiConfig config = configOpt.get();

        // 清除该提供商的其他默认配置
        clearDefaultForProvider(config.getProvider());

        // 设置为默认
        config.setIsDefault(true);
        config.setUpdatedAt(LocalDateTime.now());

        aiConfigRepository.save(config);
        logger.info("设置默认配置成功: {} - {}", config.getProvider(), config.getConfigName());
    }

    /**
     * 切换配置启用状态
     */
    public AiConfig toggleConfig(Long id) {
        Optional<AiConfig> configOpt = aiConfigRepository.findById(id);
        if (configOpt.isEmpty()) {
            throw new RuntimeException("配置不存在: " + id);
        }

        AiConfig config = configOpt.get();

        // 如果是默认配置且要禁用，检查是否有其他配置可用
        if (Boolean.TRUE.equals(config.getIsDefault()) && Boolean.TRUE.equals(config.getEnabled())) {
            List<AiConfig> otherConfigs = aiConfigRepository.findByProviderAndEnabledTrue(config.getProvider());
            if (otherConfigs.size() <= 1) {
                throw new RuntimeException("至少需要保留一个启用的配置");
            }
        }

        config.setEnabled(!config.getEnabled());
        config.setUpdatedAt(LocalDateTime.now());

        AiConfig updatedConfig = aiConfigRepository.save(config);
        logger.info("切换配置状态成功: {} - {} (enabled: {})",
                   updatedConfig.getProvider(), updatedConfig.getConfigName(), updatedConfig.getEnabled());

        return updatedConfig;
    }

    /**
     * 测试配置连接
     */
    public boolean testConfig(Long id) {
        return aiChatService.testAiConfig(id);
    }

    /**
     * 清除指定提供商的默认配置
     */
    private void clearDefaultForProvider(AiProvider provider) {
        aiConfigRepository.clearDefaultForProvider(provider);
    }

    /**
     * 获取启用配置的统计信息
     */
    @Transactional(readOnly = true)
    public long countEnabledConfigs() {
        return aiConfigRepository.countEnabledConfigs();
    }

    /**
     * 按提供商统计配置数量
     */
    @Transactional(readOnly = true)
    public List<Object[]> countConfigsByProvider() {
        return aiConfigRepository.countConfigsByProvider();
    }
}
