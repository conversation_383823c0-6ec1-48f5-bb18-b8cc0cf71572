package com.debate_ournament.ai.entity;

/**
 * AI服务提供商枚举
 */
public enum AiProvider {
    OPENAI("OpenAI", "ChatGPT API"),
    ANTHROPIC("Anthropic", "Claude API"),
    BAIDU("Baidu", "文心一言"),
    ALIBABA("Alibaba", "通义千问"),
    ALIBABA_BAILIAN("AlibabaBailian", "阿里云百炼"),
    ZHIPU("Zhipu", "智谱清言"),
    TENCENT("Tencent", "腾讯混元"),
    MOONSHOT("Moonshot", "月之暗面Kimi"),
    DEEPSEEK("DeepSeek", "深度求索"),
    SILICONFLOW("SiliconFlow", "硅基流动"),
    OPENROUTER("OpenRouter", "OpenRouter");

    private final String code;
    private final String name;

    AiProvider(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    @Override
    public String toString() {
        return code;
    }
}
