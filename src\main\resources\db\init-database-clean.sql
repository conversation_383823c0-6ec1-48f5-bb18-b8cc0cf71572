-- AI Debate Tournament Platform Database Initialization Script
-- 创建数据库初始化脚本

-- 设置字符集和排序规则
SET NAMES utf8mb4;
SET character_set_client = utf8mb4;

-- 创建用户基础信息表
CREATE TABLE IF NOT EXISTS users (
    id bigint NOT NULL AUTO_INCREMENT COMMENT 'User primary key ID',
    username varchar(50) NOT NULL COMMENT 'Username (unique)',
    email varchar(100) NOT NULL COMMENT 'Email address (unique)',
    password varchar(255) NOT NULL COMMENT 'Password hash',
    display_name varchar(100) DEFAULT NULL COMMENT 'Display name',
    avatar_url varchar(255) DEFAULT NULL COMMENT 'Avatar URL',
    bio text DEFAULT NULL COMMENT 'User biography',
    status varchar(20) NOT NULL DEFAULT 'ACTIVE' COMMENT 'User status',
    email_verified boolean NOT NULL DEFAULT false COMMENT 'Email verification status',
    last_login_time datetime DEFAULT NULL COMMENT 'Last login time',
    last_login_ip varchar(45) DEFAULT NULL COMMENT 'Last login IP address',
    login_count int NOT NULL DEFAULT 0 COMMENT 'Total login count',
    created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Account creation time',
    updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Last update time',
    PRIMARY KEY (id),
    UNIQUE KEY uk_username (username),
    UNIQUE KEY uk_email (email),
    KEY idx_status (status),
    KEY idx_last_login_time (last_login_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='User basic information table';

-- 创建用户偏好设置表
CREATE TABLE IF NOT EXISTS user_preferences (
    id bigint NOT NULL AUTO_INCREMENT COMMENT 'Preference settings primary key',
    user_id bigint NOT NULL COMMENT 'Associated user ID',
    default_debate_role varchar(20) DEFAULT NULL COMMENT 'Default debate role',
    language_preference varchar(10) NOT NULL DEFAULT 'zh_CN' COMMENT 'Language preference',
    debate_style varchar(20) DEFAULT NULL COMMENT 'Debate style preference',
    ai_model_preference varchar(50) DEFAULT NULL COMMENT 'Preferred AI model',
    theme_mode varchar(10) NOT NULL DEFAULT 'light' COMMENT 'Theme mode',
    font_size int NOT NULL DEFAULT 14 COMMENT 'Font size setting',
    enable_animations boolean NOT NULL DEFAULT true COMMENT 'Enable animations',
    created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Creation time',
    updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Last update time',
    PRIMARY KEY (id),
    UNIQUE KEY uk_user_id (user_id),
    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='User preference settings table';

-- 创建通知设置表
CREATE TABLE IF NOT EXISTS notification_settings (
    id bigint NOT NULL AUTO_INCREMENT COMMENT 'Notification settings primary key',
    user_id bigint NOT NULL COMMENT 'Associated user ID',
    email_notifications boolean NOT NULL DEFAULT true COMMENT 'Enable email notifications',
    debate_reminders boolean NOT NULL DEFAULT true COMMENT 'Enable debate reminders',
    system_notifications boolean NOT NULL DEFAULT true COMMENT 'Enable system notifications',
    achievement_notifications boolean NOT NULL DEFAULT true COMMENT 'Enable achievement notifications',
    weekly_digest boolean NOT NULL DEFAULT false COMMENT 'Enable weekly digest',
    created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Creation time',
    updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Last update time',
    PRIMARY KEY (id),
    UNIQUE KEY uk_user_id (user_id),
    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='User notification settings table';

-- 创建用户等级表
CREATE TABLE IF NOT EXISTS user_levels (
    id bigint NOT NULL AUTO_INCREMENT COMMENT 'User level primary key',
    user_id bigint NOT NULL COMMENT 'Associated user ID',
    current_level int NOT NULL DEFAULT 1 COMMENT 'Current user level',
    current_experience int NOT NULL DEFAULT 0 COMMENT 'Current experience points',
    total_experience int NOT NULL DEFAULT 0 COMMENT 'Total accumulated experience',
    next_level_experience int NOT NULL DEFAULT 100 COMMENT 'Experience needed for next level',
    debates_participated int NOT NULL DEFAULT 0 COMMENT 'Total debates participated',
    debates_won int NOT NULL DEFAULT 0 COMMENT 'Total debates won',
    debates_lost int NOT NULL DEFAULT 0 COMMENT 'Total debates lost',
    win_rate decimal(5,2) NOT NULL DEFAULT 0.00 COMMENT 'Win rate percentage',
    avg_score decimal(5,2) NOT NULL DEFAULT 0.00 COMMENT 'Average debate score',
    best_score decimal(5,2) NOT NULL DEFAULT 0.00 COMMENT 'Best debate score',
    total_debate_time int NOT NULL DEFAULT 0 COMMENT 'Total debate time in minutes',
    streak_wins int NOT NULL DEFAULT 0 COMMENT 'Current winning streak',
    max_streak_wins int NOT NULL DEFAULT 0 COMMENT 'Maximum winning streak',
    achievements json DEFAULT NULL COMMENT 'User achievements JSON',
    titles json DEFAULT NULL COMMENT 'User titles JSON',
    created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Creation time',
    updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Last update time',
    PRIMARY KEY (id),
    UNIQUE KEY uk_user_id (user_id),
    KEY idx_level (current_level),
    KEY idx_experience (current_experience),
    KEY idx_win_rate (win_rate),
    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='User level and statistics table';

-- 创建用户详细资料表
CREATE TABLE IF NOT EXISTS user_profiles (
    id bigint NOT NULL AUTO_INCREMENT COMMENT 'User profile primary key',
    user_id bigint NOT NULL COMMENT 'Associated user ID',
    first_name varchar(50) DEFAULT NULL COMMENT 'First name',
    last_name varchar(50) DEFAULT NULL COMMENT 'Last name',
    phone varchar(20) DEFAULT NULL COMMENT 'Phone number',
    birth_date date DEFAULT NULL COMMENT 'Birth date',
    gender varchar(10) DEFAULT NULL COMMENT 'Gender',
    country varchar(50) DEFAULT NULL COMMENT 'Country',
    city varchar(50) DEFAULT NULL COMMENT 'City',
    school varchar(100) DEFAULT NULL COMMENT 'School or university',
    major varchar(100) DEFAULT NULL COMMENT 'Major or field of study',
    occupation varchar(100) DEFAULT NULL COMMENT 'Occupation',
    interests text DEFAULT NULL COMMENT 'Interests and hobbies',
    social_links json DEFAULT NULL COMMENT 'Social media links JSON',
    avatar_filename varchar(255) DEFAULT NULL COMMENT 'Avatar filename',
    avatar_url varchar(500) DEFAULT NULL COMMENT 'Avatar URL',
    avatar_upload_time datetime DEFAULT NULL COMMENT 'Avatar upload time',
    profile_visibility varchar(20) NOT NULL DEFAULT 'PUBLIC' COMMENT 'Profile visibility',
    show_email boolean NOT NULL DEFAULT false COMMENT 'Show email in profile',
    show_phone boolean NOT NULL DEFAULT false COMMENT 'Show phone in profile',
    show_real_name boolean NOT NULL DEFAULT false COMMENT 'Show real name in profile',
    created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Creation time',
    updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Last update time',
    PRIMARY KEY (id),
    UNIQUE KEY uk_user_id (user_id),
    KEY idx_visibility (profile_visibility),
    KEY idx_country (country),
    KEY idx_school (school),
    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='User detailed profile table';

-- 插入示例用户数据
INSERT IGNORE INTO users (username, email, password, status) VALUES
('admin', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKKx6kLCdF4C9WKK8oUhHklVD1VO', 'ACTIVE'),
('testuser', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKKx6kLCdF4C9WKK8oUhHklVD1VO', 'ACTIVE');

-- 为示例用户创建偏好设置
INSERT IGNORE INTO user_preferences (user_id, language_preference, theme_mode)
SELECT id, 'zh_CN', 'light' FROM users WHERE username IN ('admin', 'testuser');

-- 为示例用户创建通知设置
INSERT IGNORE INTO notification_settings (user_id)
SELECT id FROM users WHERE username IN ('admin', 'testuser');

-- 为示例用户创建等级信息
INSERT IGNORE INTO user_levels (user_id, current_level, current_experience, total_experience, next_level_experience)
SELECT id, 1, 0, 0, 100 FROM users WHERE username IN ('admin', 'testuser');

-- 为示例用户创建详细资料
INSERT IGNORE INTO user_profiles (user_id, profile_visibility)
SELECT id, 'PUBLIC' FROM users WHERE username IN ('admin', 'testuser');
