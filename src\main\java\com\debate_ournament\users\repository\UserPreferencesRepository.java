package com.debate_ournament.users.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.debate_ournament.users.entity.UserPreferences;

/**
 * 用户偏好设置数据访问接口
 */
@Repository
public interface UserPreferencesRepository extends JpaRepository<UserPreferences, Long> {

    /**
     * 根据用户ID查找用户偏好设置
     */
    Optional<UserPreferences> findByUserId(Long userId);

    /**
     * 根据用户ID删除用户偏好设置
     */
    void deleteByUserId(Long userId);

    /**
     * 查找所有启用动画效果的用户偏好设置
     */
    @Query("SELECT up FROM UserPreferences up WHERE up.enableAnimations = true")
    List<UserPreferences> findAllWithAnimationsEnabled();

    /**
     * 根据默认辩论角色查找用户偏好设置
     */
    @Query("SELECT up FROM UserPreferences up WHERE up.defaultDebateRole = :role")
    List<UserPreferences> findByDefaultDebateRole(@Param("role") UserPreferences.DebateRole role);

    /**
     * 根据语言偏好查找用户偏好设置
     */
    @Query("SELECT up FROM UserPreferences up WHERE up.languagePreference = :language")
    List<UserPreferences> findByLanguage(@Param("language") String language);

    /**
     * 根据主题偏好查找用户偏好设置
     */
    @Query("SELECT up FROM UserPreferences up WHERE up.themeMode = :theme")
    List<UserPreferences> findByTheme(@Param("theme") String theme);

    /**
     * 检查用户是否存在偏好设置
     */
    boolean existsByUserId(Long userId);
}
