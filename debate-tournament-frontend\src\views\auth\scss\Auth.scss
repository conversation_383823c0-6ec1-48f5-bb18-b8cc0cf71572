@use '@/styles/variables' as v;
@use '@/styles/mixins' as m;

/* 所有认证页面的共享样式 */
.login-page,
.register-page,
.forgot-password-page,
.reset-password-page,
.verify-email-page {
  padding: v.$spacing-xl 0;
  min-height: calc(100vh - 150px);
  display: flex;
  align-items: center;
  background-color: var(--color-background);
}

/* 认证页面通用样式 */
.auth-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: calc(100vh - 130px);
  padding: v.$spacing-lg v.$spacing-md;
  background-color: v.$color-surface;
}

.auth-card {
  width: 100%;
  max-width: 480px;
  background-color: white;
  border-radius: v.$shape-corner-medium;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.auth-header {
  padding: v.$spacing-lg;
  text-align: center;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.auth-title {
  font-size: 1.75rem;
  font-weight: 600;
  color: v.$color-primary;
  margin-bottom: v.$spacing-xs;
}

.auth-subtitle {
  color: v.$color-secondary;
  font-size: 0.95rem;
}

.auth-body {
  padding: v.$spacing-lg;
}

.auth-footer {
  padding: v.$spacing-md v.$spacing-lg;
  background-color: rgba(0, 0, 0, 0.02);
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  text-align: center;
}

.auth-form {
  margin-bottom: v.$spacing-md;
}

.auth-alert {
  padding: v.$spacing-sm v.$spacing-md;
  border-radius: v.$shape-corner-small;
  margin-bottom: v.$spacing-md;
  font-size: 0.9rem;
}

.auth-alert--error {
  background-color: rgba(244, 67, 54, 0.1);
  color: v.$color-error;
  border-left: 4px solid v.$color-error;
}

.auth-alert--success {
  background-color: rgba(76, 175, 80, 0.1);
  color: v.$color-success;
  border-left: 4px solid v.$color-success;
}

.auth-links {
  display: flex;
  justify-content: center;
  align-items: center;
}

.auth-link {
  color: v.$color-primary;
  text-decoration: none;
  font-size: 0.9rem;

  &:hover {
    text-decoration: underline;
  }
}

.auth-divider {
  margin: 0 v.$spacing-sm;
  color: rgba(0, 0, 0, 0.3);
}

/* 表单元素样式 */
.form-group {
  margin-bottom: v.$spacing-md;
}

.form-control {
  width: 100%;
  padding: v.$spacing-sm v.$spacing-md;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: v.$shape-corner-small;
  font-size: 1rem;

  &:focus {
    outline: none;
    border-color: v.$color-primary;
    box-shadow: 0 0 0 2px rgba(103, 80, 164, 0.2);
  }

  &.is-invalid {
    border-color: v.$color-error;
  }
}

.form-label {
  display: block;
  margin-bottom: v.$spacing-xs;
  font-weight: 500;
}

.form-text {
  display: block;
  margin-top: v.$spacing-xs;
  font-size: 0.8rem;

  &.text-error {
    color: v.$color-error;
  }
}

.form-check {
  display: flex;
  align-items: center;
}

.form-check-input {
  margin-right: v.$spacing-xs;
}

.form-check-label {
  font-size: 0.9rem;
}

/* 密码输入框 */
.password-input-container {
  position: relative;
}

.password-toggle-btn {
  position: absolute;
  right: v.$spacing-sm;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: v.$color-secondary;
  font-size: 0.8rem;
  cursor: pointer;
}

/* 验证码 */
.captcha-container {
  display: flex;
  align-items: center;
}

.captcha-input {
  flex: 1;
  margin-right: v.$spacing-sm;
}

.captcha-image {
  height: 40px;
  border-radius: v.$shape-corner-small;
  cursor: pointer;
}

/* 按钮 */
.btn--block {
  display: block;
  width: 100%;
}

/* 响应式调整 */
@media (max-width: 576px) {
  .auth-card {
    box-shadow: none;
  }
}

/* 验证邮箱页面特定样式 */
.verification-status {
  text-align: center;
  padding: v.$spacing-lg 0;

  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid v.$color-secondary;
    border-top-color: v.$color-primary;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto v.$spacing-md;
  }

  &.success {
    color: v.$color-success;
  }

  &.error {
    color: v.$color-error;
  }

  i {
    font-size: 48px;
    margin-bottom: v.$spacing-md;
  }

  p {
    margin-bottom: v.$spacing-lg;
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 注册页面特定样式 */
.agree-terms {
  display: flex;
  align-items: center;
  gap: v.$spacing-xs;

  input {
    margin-right: v.$spacing-xs;
  }

  a {
    color: v.$color-primary;
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }
}

@media (max-width: map-get(v.$breakpoints, 'sm')) {
  .auth-card {
    padding: v.$spacing-lg;
  }

  .auth-header {
    h1 {
      font-size: 1.5rem;
    }
  }
}
