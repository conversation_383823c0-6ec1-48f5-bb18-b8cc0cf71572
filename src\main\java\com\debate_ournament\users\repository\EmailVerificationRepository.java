package com.debate_ournament.users.repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.debate_ournament.users.entity.EmailVerification;

/**
 * 邮箱验证数据访问接口
 */
@Repository
public interface EmailVerificationRepository extends JpaRepository<EmailVerification, Long> {

    /**
     * 根据用户ID查找最新的邮箱验证记录
     */
    @Query("SELECT ev FROM EmailVerification ev WHERE ev.user.id = :userId ORDER BY ev.createdAt DESC")
    List<EmailVerification> findByUserIdOrderByCreatedAtDesc(@Param("userId") Long userId);

    /**
     * 根据用户ID和验证码查找记录
     */
    @Query("SELECT ev FROM EmailVerification ev WHERE ev.user.id = :userId AND ev.verificationCode = :code")
    Optional<EmailVerification> findByUserIdAndVerificationCode(@Param("userId") Long userId, @Param("code") String code);

    /**
     * 查找有效的验证码（未使用且未过期）
     */
    @Query("SELECT ev FROM EmailVerification ev WHERE ev.user.id = :userId AND ev.verificationCode = :code AND ev.used = false AND ev.expiresAt > :currentTime")
    Optional<EmailVerification> findValidVerification(@Param("userId") Long userId, @Param("code") String code, @Param("currentTime") LocalDateTime currentTime);

    /**
     * 查找已过期的验证记录
     */
    @Query("SELECT ev FROM EmailVerification ev WHERE ev.expiresAt < :currentTime")
    List<EmailVerification> findExpiredVerifications(@Param("currentTime") LocalDateTime currentTime);

    /**
     * 查找指定用户的未使用验证记录
     */
    @Query("SELECT ev FROM EmailVerification ev WHERE ev.user.id = :userId AND ev.used = false")
    List<EmailVerification> findUnusedByUserId(@Param("userId") Long userId);

    /**
     * 查找尝试次数过多的验证记录
     */
    @Query("SELECT ev FROM EmailVerification ev WHERE ev.attemptCount >= :maxAttempts")
    List<EmailVerification> findVerificationsWithExcessiveAttempts(@Param("maxAttempts") int maxAttempts);

    /**
     * 查找指定时间之前创建的未验证记录
     */
    @Query("SELECT ev FROM EmailVerification ev WHERE ev.used = false AND ev.createdAt < :cutoffTime")
    List<EmailVerification> findUnusedVerificationsBefore(@Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * 删除过期的验证记录
     */
    @Modifying
    @Query("DELETE FROM EmailVerification ev WHERE ev.expiresAt < :currentTime")
    int deleteExpiredVerifications(@Param("currentTime") LocalDateTime currentTime);

    /**
     * 删除已使用的验证记录
     */
    @Modifying
    @Query("DELETE FROM EmailVerification ev WHERE ev.used = true AND ev.verifiedAt < :cutoffTime")
    int deleteUsedVerifications(@Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * 删除指定用户的所有验证记录
     */
    @Modifying
    @Query("DELETE FROM EmailVerification ev WHERE ev.user.id = :userId")
    int deleteAllByUserId(@Param("userId") Long userId);

    /**
     * 标记验证记录为已使用
     */
    @Modifying
    @Query("UPDATE EmailVerification ev SET ev.used = true, ev.verifiedAt = :verifiedAt WHERE ev.id = :id")
    int markVerificationAsUsed(@Param("id") Long id, @Param("verifiedAt") LocalDateTime verifiedAt);

    /**
     * 增加验证尝试次数
     */
    @Modifying
    @Query("UPDATE EmailVerification ev SET ev.attemptCount = ev.attemptCount + 1 WHERE ev.id = :id")
    int incrementAttemptCount(@Param("id") Long id);

    /**
     * 禁用指定用户的所有未使用验证记录
     */
    @Modifying
    @Query("UPDATE EmailVerification ev SET ev.used = true WHERE ev.user.id = :userId AND ev.used = false")
    int disableAllUnusedByUserId(@Param("userId") Long userId);

    /**
     * 统计指定用户的验证尝试次数
     */
    @Query("SELECT COUNT(ev) FROM EmailVerification ev WHERE ev.user.id = :userId AND ev.createdAt > :cutoffTime")
    long countVerificationAttemptsByUserId(@Param("userId") Long userId, @Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * 查找最近成功验证的记录
     */
    @Query("SELECT ev FROM EmailVerification ev WHERE ev.used = true AND ev.verifiedAt > :cutoffTime ORDER BY ev.verifiedAt DESC")
    List<EmailVerification> findRecentSuccessfulVerifications(@Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * 检查用户是否有待验证的记录
     */
    @Query("SELECT COUNT(ev) > 0 FROM EmailVerification ev WHERE ev.user.id = :userId AND ev.used = false AND ev.expiresAt > :currentTime")
    boolean hasValidPendingVerification(@Param("userId") Long userId, @Param("currentTime") LocalDateTime currentTime);
}
