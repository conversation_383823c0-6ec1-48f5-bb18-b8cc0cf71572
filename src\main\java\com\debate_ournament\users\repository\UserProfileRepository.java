package com.debate_ournament.users.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.debate_ournament.users.entity.UserProfile;

/**
 * 用户资料数据访问接口
 */
@Repository
public interface UserProfileRepository extends JpaRepository<UserProfile, Long> {

    /**
     * 根据用户ID查找用户资料
     * @param userId 用户ID
     * @return 用户资料
     */
    Optional<UserProfile> findByUserId(Long userId);

    /**
     * 根据用户名查找用户资料
     * @param username 用户名
     * @return 用户资料
     */
    @Query("SELECT up FROM UserProfile up JOIN up.user u WHERE u.username = :username")
    Optional<UserProfile> findByUsername(@Param("username") String username);

    /**
     * 检查用户是否已有资料信息
     * @param userId 用户ID
     * @return 是否存在
     */
    boolean existsByUserId(Long userId);

    /**
     * 删除指定用户的资料信息
     * @param userId 用户ID
     */
    @Modifying
    @Query("DELETE FROM UserProfile up WHERE up.user.id = :userId")
    void deleteByUserId(@Param("userId") Long userId);

    /**
     * 查找所有公开的用户资料
     * @return 公开用户资料列表
     */
    @Query("SELECT up FROM UserProfile up JOIN up.user u " +
           "WHERE up.isProfilePublic = true AND u.status = 'ACTIVE' " +
           "ORDER BY u.createdAt DESC")
    java.util.List<UserProfile> findPublicProfiles();

    /**
     * 根据显示名称搜索用户资料（模糊匹配）
     * @param displayName 显示名称
     * @return 用户资料列表
     */
    @Query("SELECT up FROM UserProfile up JOIN up.user u " +
           "WHERE up.isProfilePublic = true " +
           "AND u.status = 'ACTIVE' " +
           "AND (LOWER(up.displayName) LIKE LOWER(CONCAT('%', :displayName, '%')) " +
           "OR LOWER(u.username) LIKE LOWER(CONCAT('%', :displayName, '%'))) " +
           "ORDER BY u.createdAt DESC")
    java.util.List<UserProfile> findByDisplayNameContaining(@Param("displayName") String displayName);

    /**
     * 根据学校或机构搜索用户资料
     * @param schoolOrOrganization 学校或机构
     * @return 用户资料列表
     */
    @Query("SELECT up FROM UserProfile up JOIN up.user u " +
           "WHERE up.isProfilePublic = true " +
           "AND u.status = 'ACTIVE' " +
           "AND LOWER(up.schoolOrOrganization) LIKE LOWER(CONCAT('%', :schoolOrOrganization, '%')) " +
           "ORDER BY u.createdAt DESC")
    java.util.List<UserProfile> findBySchoolOrOrganizationContaining(
            @Param("schoolOrOrganization") String schoolOrOrganization);

    /**
     * 根据所在地搜索用户资料
     * @param location 所在地
     * @return 用户资料列表
     */
    @Query("SELECT up FROM UserProfile up JOIN up.user u " +
           "WHERE up.isProfilePublic = true " +
           "AND u.status = 'ACTIVE' " +
           "AND LOWER(up.location) LIKE LOWER(CONCAT('%', :location, '%')) " +
           "ORDER BY u.createdAt DESC")
    java.util.List<UserProfile> findByLocationContaining(@Param("location") String location);

    /**
     * 统计有头像的用户数量
     * @return 有头像的用户数量
     */
    @Query("SELECT COUNT(up) FROM UserProfile up JOIN up.user u " +
           "WHERE up.avatarUrl IS NOT NULL AND up.avatarUrl != '' " +
           "AND u.status = 'ACTIVE'")
    long countUsersWithAvatar();

    /**
     * 统计公开资料的用户数量
     * @return 公开资料的用户数量
     */
    @Query("SELECT COUNT(up) FROM UserProfile up JOIN up.user u " +
           "WHERE up.isProfilePublic = true AND u.status = 'ACTIVE'")
    long countPublicProfiles();

    /**
     * 查找特定性别的用户资料
     * @param gender 性别
     * @return 用户资料列表
     */
    @Query("SELECT up FROM UserProfile up JOIN up.user u " +
           "WHERE up.isProfilePublic = true " +
           "AND u.status = 'ACTIVE' " +
           "AND up.gender = :gender " +
           "ORDER BY u.createdAt DESC")
    java.util.List<UserProfile> findByGender(@Param("gender") UserProfile.Gender gender);

    /**
     * 批量更新用户资料的公开状态
     * @param userIds 用户ID列表
     * @param isPublic 是否公开
     */
    @Modifying
    @Query("UPDATE UserProfile up SET up.isProfilePublic = :isPublic " +
           "WHERE up.user.id IN :userIds")
    void updateProfileVisibility(@Param("userIds") java.util.List<Long> userIds,
                                @Param("isPublic") boolean isPublic);

    /**
     * 清除指定用户的头像信息
     * @param userId 用户ID
     */
    @Modifying
    @Query("UPDATE UserProfile up SET up.avatarUrl = NULL, " +
           "up.avatarFileName = NULL, up.avatarFileSize = NULL, " +
           "up.avatarContentType = NULL WHERE up.user.id = :userId")
    void clearUserAvatar(@Param("userId") Long userId);

    /**
     * 获取所有头像文件名（用于文件清理）
     * @return 头像文件名列表
     */
    @Query("SELECT up.avatarFileName FROM UserProfile up WHERE up.avatarFileName IS NOT NULL")
    List<String> findAllAvatarFileNames();
}
