# 前后端接口连接说明

## 🌟 概述

本文档说明AI辩论赛平台前后端数据交互的关键API接口。

## 📋 主要API接口

### 1. 用户认证接口

#### 登录
- **接口:** `POST /api/auth/login`
- **前端调用:**
```javascript
import { login } from '@/api/auth'

const loginData = {
  username: 'testuser',
  password: 'password123'
}

login(loginData).then(response => {
  console.log('登录成功:', response.data)
  localStorage.setItem('token', response.data.token)
})
```

#### 注册
- **接口:** `POST /api/auth/register`
- **前端调用:**
```javascript
import { register } from '@/api/auth'

const registerData = {
  username: 'newuser',
  email: '<EMAIL>',
  password: 'password123'
}

register(registerData).then(response => {
  console.log('注册成功:', response.data)
})
```

### 2. 用户资料接口

#### 获取当前用户资料
- **接口:** `GET /api/users/profile/current`
- **前端调用:**
```javascript
import { getCurrentUserProfile } from '@/api/userProfile'

getCurrentUserProfile().then(response => {
  console.log('当前用户资料:', response.data)
})
```

#### 更新用户资料
- **接口:** `PUT /api/users/profile/my`
- **前端调用:**
```javascript
import { updateUserProfile } from '@/api/userProfile'

const profileData = {
  displayName: '新的显示名称',
  bio: '更新后的个人简介',
  isProfilePublic: true
}

updateUserProfile(profileData).then(response => {
  console.log('资料更新成功:', response.data)
})
```

#### 上传头像
- **接口:** `POST /api/users/profile/my/avatar`
- **前端调用:**
```javascript
import { uploadAvatar } from '@/api/userProfile'

const handleAvatarUpload = (file) => {
  uploadAvatar(file).then(response => {
    console.log('头像上传成功:', response.data)
    this.avatarUrl = response.data.avatarUrl
  })
}
```

#### 获取用户列表（管理员）
- **接口:** `GET /api/users/profile/list`
- **前端调用:**
```javascript
import { getUserProfiles } from '@/api/userProfile'

getUserProfiles(0, 10, '').then(response => {
  console.log('用户列表:', response.data)
  // 返回分页数据
})
```

### 3. 用户等级接口

#### 获取用户等级
- **接口:** `GET /api/users/level/my`
- **前端调用:**
```javascript
import { getUserLevel } from '@/api/userLevel'

getUserLevel().then(response => {
  console.log('用户等级:', response.data)
})
```

### 4. 数据库管理接口（管理员）

#### 检查数据库状态
- **接口:** `GET /api/admin/database/status`
- **前端调用:**
```javascript
import request from '@/api/index'

request({
  url: '/api/admin/database/status',
  method: 'get'
}).then(response => {
  console.log('数据库状态:', response.data)
})
```

## 🔧 前端配置

### axios配置
```javascript
// src/api/index.js
import axios from 'axios'

const request = axios.create({
  baseURL: 'http://localhost:8080',
  timeout: 10000
})

// 请求拦截器
request.interceptors.request.use(config => {
  const token = localStorage.getItem('token')
  if (token) {
    config.headers.Authorization = `Bearer ${token}`
  }
  return config
})

// 响应拦截器
request.interceptors.response.use(
  response => response.data,
  error => {
    if (error.response?.status === 401) {
      localStorage.removeItem('token')
      window.location.href = '/login'
    }
    return Promise.reject(error)
  }
)

export default request
```

## 📊 数据格式

### 成功响应
```json
{
  "success": true,
  "message": "操作成功",
  "data": { ... },
  "timestamp": "2025-05-25T21:58:00"
}
```

### 错误响应
```json
{
  "success": false,
  "message": "错误描述",
  "data": null,
  "timestamp": "2025-05-25T21:58:00"
}
```

## 🚀 Vue组件示例

```vue
<template>
  <div class="user-profile">
    <el-card>
      <div slot="header">用户资料</div>

      <el-form :model="profile">
        <el-form-item label="头像">
          <el-avatar :size="100" :src="profile.avatarUrl"></el-avatar>
          <el-upload
            :action="uploadUrl"
            :headers="uploadHeaders"
            :on-success="handleAvatarSuccess"
          >
            <el-button size="small">更换头像</el-button>
          </el-upload>
        </el-form-item>

        <el-form-item label="显示名称">
          <el-input v-model="profile.displayName"></el-input>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="updateProfile">保存</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import { getCurrentUserProfile, updateUserProfile } from '@/api/userProfile'

export default {
  name: 'UserProfile',
  data() {
    return {
      profile: {},
      uploadUrl: 'http://localhost:8080/api/users/profile/my/avatar',
      uploadHeaders: {
        Authorization: 'Bearer ' + localStorage.getItem('token')
      }
    }
  },

  mounted() {
    this.loadProfile()
  },

  methods: {
    async loadProfile() {
      const response = await getCurrentUserProfile()
      this.profile = response.data
    },

    async updateProfile() {
      await updateUserProfile(this.profile)
      this.$message.success('更新成功')
    },

    handleAvatarSuccess(response) {
      if (response.success) {
        this.profile.avatarUrl = response.data.avatarUrl
        this.$message.success('头像更新成功')
      }
    }
  }
}
</script>
```

## 🛡️ 安全注意事项

1. **Token管理**: 自动添加Bearer token到请求头
2. **文件上传**: 验证文件类型和大小
3. **错误处理**: 统一处理401未授权错误
4. **HTTPS**: 生产环境使用HTTPS

---

**更新时间:** 2025-05-25
**版本:** 1.0
