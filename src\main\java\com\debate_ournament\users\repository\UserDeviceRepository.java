package com.debate_ournament.users.repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.debate_ournament.users.entity.User;
import com.debate_ournament.users.entity.UserDevice;

/**
 * 用户设备数据访问接口
 */
@Repository
public interface UserDeviceRepository extends JpaRepository<UserDevice, Long> {

    /**
     * 根据用户查找所有设备
     */
    List<UserDevice> findByUser(User user);

    /**
     * 根据用户ID查找所有设备
     */
    List<UserDevice> findByUserId(Long userId);

    /**
     * 根据设备ID查找设备
     */
    Optional<UserDevice> findByDeviceId(String deviceId);

    /**
     * 根据用户和设备ID查找设备
     */
    Optional<UserDevice> findByUserAndDeviceId(User user, String deviceId);

    /**
     * 查找用户的当前设备
     */
    Optional<UserDevice> findByUserAndIsCurrentTrue(User user);

    /**
     * 查找用户的可信设备
     */
    List<UserDevice> findByUserAndIsTrustedTrue(User user);

    /**
     * 查找特定IP地址的设备
     */
    List<UserDevice> findByIpAddress(String ipAddress);

    /**
     * 查找特定浏览器类型的设备
     */
    List<UserDevice> findByBrowserName(String browserName);

    /**
     * 查找特定操作系统的设备
     */
    List<UserDevice> findByOsName(String osName);

    /**
     * 查找最近登录的设备
     */
    @Query("SELECT ud FROM UserDevice ud WHERE ud.lastLoginAt >= :since ORDER BY ud.lastLoginAt DESC")
    List<UserDevice> findRecentDevices(@Param("since") LocalDateTime since);

    /**
     * 查找长期未使用的设备
     */
    @Query("SELECT ud FROM UserDevice ud WHERE ud.lastLoginAt <= :before")
    List<UserDevice> findInactiveDevices(@Param("before") LocalDateTime before);

    /**
     * 统计用户的设备数量
     */
    long countByUser(User user);

    /**
     * 删除用户的所有设备
     */
    void deleteByUser(User user);

    /**
     * 删除特定设备
     */
    void deleteByUserAndDeviceId(User user, String deviceId);

    /**
     * 删除长期未使用的设备
     */
    @Query("DELETE FROM UserDevice ud WHERE ud.lastLoginAt <= :before")
    int deleteInactiveDevices(@Param("before") LocalDateTime before);

    /**
     * 更新设备的最后登录时间
     */
    @Query("UPDATE UserDevice ud SET ud.lastLoginAt = :loginTime, ud.ipAddress = :ipAddress WHERE ud.id = :deviceId")
    int updateDeviceLastLogin(@Param("deviceId") Long deviceId,
                            @Param("loginTime") LocalDateTime loginTime,
                            @Param("ipAddress") String ipAddress);

    /**
     * 更新设备的信任状态
     */
    @Query("UPDATE UserDevice ud SET ud.isTrusted = :trusted WHERE ud.id = :deviceId")
    int updateDeviceTrustStatus(@Param("deviceId") Long deviceId, @Param("trusted") boolean trusted);
}
