import axios from 'axios';

// 设置基础URL
const API_URL = '/api/debates';

/**
 * 获取辩论列表
 * @param {Object} params - 查询参数
 * @param {string} [params.category] - 分类筛选
 * @param {string} [params.status] - 状态筛选
 * @param {string} [params.search] - 搜索关键词
 * @param {string} [params.sort] - 排序方式
 * @param {number} [params.page] - 页码
 * @param {number} [params.limit] - 每页数量
 * @returns {Promise} - 返回辩论列表
 */
export function getDebates(params) {
  return axios.get(API_URL, { params });
}

/**
 * 获取辩论详情
 * @param {number|string} id - 辩论ID
 * @returns {Promise} - 返回辩论详情
 */
export function getDebateById(id) {
  return axios.get(`${API_URL}/${id}`);
}

/**
 * 创建辩论
 * @param {Object} debateData - 辩论数据
 * @param {string} debateData.title - 辩题
 * @param {string} debateData.description - 描述
 * @param {string} debateData.category - 分类
 * @returns {Promise} - 返回创建结果
 */
export function createDebate(debateData) {
  return axios.post(API_URL, debateData);
}

/**
 * 更新辩论
 * @param {number|string} id - 辩论ID
 * @param {Object} debateData - 辩论数据
 * @returns {Promise} - 返回更新结果
 */
export function updateDebate(id, debateData) {
  return axios.put(`${API_URL}/${id}`, debateData);
}

/**
 * 删除辩论
 * @param {number|string} id - 辩论ID
 * @returns {Promise} - 返回删除结果
 */
export function deleteDebate(id) {
  return axios.delete(`${API_URL}/${id}`);
}

/**
 * 加入辩论
 * @param {number|string} id - 辩论ID
 * @param {Object} joinData - 加入数据
 * @param {string} joinData.side - 加入方（supporter/opposer/judge）
 * @returns {Promise} - 返回加入结果
 */
export function joinDebate(id, joinData) {
  return axios.post(`${API_URL}/${id}/join`, joinData);
}

/**
 * 发表言论
 * @param {number|string} debateId - 辩论ID
 * @param {Object} speechData - 言论数据
 * @param {string} speechData.content - 言论内容
 * @returns {Promise} - 返回发表结果
 */
export function createSpeech(debateId, speechData) {
  return axios.post(`${API_URL}/${debateId}/speeches`, speechData);
}

/**
 * 获取言论列表
 * @param {number|string} debateId - 辩论ID
 * @param {Object} params - 查询参数
 * @param {number} [params.page] - 页码
 * @param {number} [params.limit] - 每页数量
 * @returns {Promise} - 返回言论列表
 */
export function getSpeeches(debateId, params) {
  return axios.get(`${API_URL}/${debateId}/speeches`, { params });
}

/**
 * 投票
 * @param {number|string} debateId - 辩论ID
 * @param {Object} voteData - 投票数据
 * @param {string} voteData.side - 投票方（supporter/opposer）
 * @returns {Promise} - 返回投票结果
 */
export function vote(debateId, voteData) {
  return axios.post(`${API_URL}/${debateId}/votes`, voteData);
}

/**
 * 获取辩论结果
 * @param {number|string} debateId - 辩论ID
 * @returns {Promise} - 返回辩论结果
 */
export function getDebateResult(debateId) {
  return axios.get(`${API_URL}/${debateId}/result`);
} 