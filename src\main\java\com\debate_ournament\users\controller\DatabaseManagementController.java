package com.debate_ournament.users.controller;

import java.util.HashMap;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.debate_ournament.config.DatabaseInitializationConfig;
import com.debate_ournament.dto.common.ApiResponse;

/**
 * 数据库管理控制器
 * 提供数据库初始化状态查询和手动管理功能
 *
 * <AUTHOR> Debate Tournament Platform
 * @version 1.0
 * @since 2025-05-25
 */
@RestController
@RequestMapping("/admin/database")
@PreAuthorize("hasRole('ADMIN')") // 只有管理员可以访问
public class DatabaseManagementController {

    private static final Logger log = LoggerFactory.getLogger(DatabaseManagementController.class);

    private final DatabaseInitializationConfig databaseInitConfig;

    @Autowired
    public DatabaseManagementController(DatabaseInitializationConfig databaseInitConfig) {
        this.databaseInitConfig = databaseInitConfig;
    }

    /**
     * 获取数据库初始化状态
     * GET /admin/database/status
     */
    @GetMapping("/status")
    public ResponseEntity<ApiResponse<DatabaseInitializationConfig.DatabaseInitStatus>> getDatabaseStatus() {
        try {
            log.info("管理员请求获取数据库初始化状态");

            DatabaseInitializationConfig.DatabaseInitStatus status = databaseInitConfig.getDatabaseInitStatus();

            return ResponseEntity.ok(ApiResponse.success("数据库状态获取成功", status));

        } catch (Exception e) {
            log.error("获取数据库状态时发生错误", e);
            return ResponseEntity.internalServerError()
                .body(ApiResponse.error("获取数据库状态失败: " + e.getMessage()));
        }
    }

    /**
     * 手动重新初始化数据库
     * POST /admin/database/reinitialize
     *
     * 警告：此操作可能会清理现有数据，请谨慎使用
     */
    @PostMapping("/reinitialize")
    public ResponseEntity<ApiResponse<Map<String, Object>>> reinitializeDatabase(
            @RequestParam(defaultValue = "false") boolean confirmDangerous) {

        if (!confirmDangerous) {
            return ResponseEntity.badRequest()
                .body(ApiResponse.error("请确认此操作的危险性，设置 confirmDangerous=true 参数"));
        }

        try {
            log.warn("管理员请求手动重新初始化数据库");

            // 获取初始化前的状态
            DatabaseInitializationConfig.DatabaseInitStatus beforeStatus = databaseInitConfig.getDatabaseInitStatus();

            // 执行重新初始化
            databaseInitConfig.forceReinitializeDatabase();

            // 获取初始化后的状态
            DatabaseInitializationConfig.DatabaseInitStatus afterStatus = databaseInitConfig.getDatabaseInitStatus();

            Map<String, Object> result = new HashMap<>();
            result.put("beforeStatus", beforeStatus);
            result.put("afterStatus", afterStatus);
            result.put("reinitializeTime", System.currentTimeMillis());

            log.info("数据库手动重新初始化完成");

            return ResponseEntity.ok(ApiResponse.success("数据库重新初始化成功", result));

        } catch (Exception e) {
            log.error("手动重新初始化数据库时发生错误", e);
            return ResponseEntity.internalServerError()
                .body(ApiResponse.error("数据库重新初始化失败: " + e.getMessage()));
        }
    }

    /**
     * 获取数据库基本信息
     * GET /admin/database/info
     */
    @GetMapping("/info")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getDatabaseInfo() {
        try {
            log.info("管理员请求获取数据库基本信息");

            DatabaseInitializationConfig.DatabaseInitStatus status = databaseInitConfig.getDatabaseInitStatus();

            Map<String, Object> info = new HashMap<>();
            info.put("initialized", status.isInitialized());
            info.put("userCount", status.getUserCount());
            info.put("tableCount", status.getTableCount());
            info.put("initEnabled", status.isInitEnabled());
            info.put("error", status.getError());
            info.put("checkTime", System.currentTimeMillis());

            return ResponseEntity.ok(ApiResponse.success("数据库信息获取成功", info));

        } catch (Exception e) {
            log.error("获取数据库信息时发生错误", e);
            return ResponseEntity.internalServerError()
                .body(ApiResponse.error("获取数据库信息失败: " + e.getMessage()));
        }
    }

    /**
     * 数据库健康检查
     * GET /admin/database/health
     */
    @GetMapping("/health")
    public ResponseEntity<ApiResponse<Map<String, Object>>> checkDatabaseHealth() {
        try {
            log.info("管理员请求数据库健康检查");

            Map<String, Object> health = new HashMap<>();

            // 检查数据库连接
            try {
                DatabaseInitializationConfig.DatabaseInitStatus status = databaseInitConfig.getDatabaseInitStatus();
                health.put("connectionStatus", "OK");
                health.put("tablesExist", status.getTableCount() > 0);
                health.put("hasUsers", status.getUserCount() > 0);
                health.put("overallHealth", "HEALTHY");
            } catch (Exception e) {
                health.put("connectionStatus", "ERROR");
                health.put("error", e.getMessage());
                health.put("overallHealth", "UNHEALTHY");
            }

            health.put("checkTime", System.currentTimeMillis());

            return ResponseEntity.ok(ApiResponse.success("数据库健康检查完成", health));

        } catch (Exception e) {
            log.error("数据库健康检查时发生错误", e);
            return ResponseEntity.internalServerError()
                .body(ApiResponse.error("数据库健康检查失败: " + e.getMessage()));
        }
    }

    /**
     * 获取数据库配置信息
     * GET /admin/database/config
     */
    @GetMapping("/config")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getDatabaseConfig() {
        try {
            log.info("管理员请求获取数据库配置信息");

            Map<String, Object> config = new HashMap<>();
            config.put("initEnabled", databaseInitConfig.getDatabaseInitStatus().isInitEnabled());
            config.put("message", "数据库配置信息");
            config.put("note", "敏感配置信息已隐藏");

            return ResponseEntity.ok(ApiResponse.success("数据库配置信息获取成功", config));

        } catch (Exception e) {
            log.error("获取数据库配置信息时发生错误", e);
            return ResponseEntity.internalServerError()
                .body(ApiResponse.error("获取数据库配置信息失败: " + e.getMessage()));
        }
    }
}
