package com.debate_ournament.users.service;

import java.security.PrivateKey;
import java.security.PublicKey;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.debate_ournament.service.KeyManagementService;
import com.debate_ournament.util.EncryptionUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * 加密服务
 *
 * 提供完整的端到端加密通信服务，包括：
 * - RSA公私钥管理
 * - AES会话密钥生成和管理
 * - 混合加密/解密
 * - HMAC消息认证
 * - 缓存和性能优化
 *
 * <AUTHOR> Debate Tournament Platform
 * @version 2.0
 * @since 2024
 */
@Service
public class EncryptionService {

    private static final Logger logger = LoggerFactory.getLogger(EncryptionService.class);

    private final KeyManagementService keyManagementService;
    private final EncryptionUtil encryptionUtil;
    private final ObjectMapper objectMapper;

    // 会话密钥缓存
    private final Map<String, SessionKeyInfo> sessionKeyCache = new ConcurrentHashMap<>();

    // 统计信息
    private final AtomicLong encryptCount = new AtomicLong(0);
    private final AtomicLong decryptCount = new AtomicLong(0);
    private final AtomicLong cacheHitCount = new AtomicLong(0);
    private final AtomicLong cacheMissCount = new AtomicLong(0);

    @Autowired
    public EncryptionService(KeyManagementService keyManagementService,
                           EncryptionUtil encryptionUtil,
                           ObjectMapper objectMapper) {
        this.keyManagementService = keyManagementService;
        this.encryptionUtil = encryptionUtil;
        this.objectMapper = objectMapper;
    }

    /**
     * 获取公钥信息
     *
     * @return 包含公钥和相关信息的Map
     */
    public Map<String, Object> getPublicKey() {
        try {
            PublicKey publicKey = keyManagementService.getCurrentPublicKey();
            String keyId = keyManagementService.getCurrentKeyId();
            LocalDateTime keyGeneratedTime = keyManagementService.getKeyGeneratedTime();

            Map<String, Object> publicKeyInfo = new HashMap<>();
            publicKeyInfo.put("publicKey", encryptionUtil.publicKeyToBase64(publicKey));
            publicKeyInfo.put("keyId", keyId);
            publicKeyInfo.put("algorithm", "RSA");
            publicKeyInfo.put("keySize", 2048);
            publicKeyInfo.put("timestamp", System.currentTimeMillis());

            if (keyGeneratedTime != null) {
                publicKeyInfo.put("keyGeneratedTime", keyGeneratedTime.toString());
            }

            logger.debug("返回公钥信息，密钥ID: {}", keyId);
            return publicKeyInfo;

        } catch (Exception e) {
            logger.error("获取公钥信息失败", e);
            throw new RuntimeException("获取公钥失败", e);
        }
    }

    /**
     * 检查请求是否为加密格式
     *
     * @param requestBody 请求体
     * @return 是否为加密请求
     */
    public boolean isEncryptedRequest(Map<String, Object> requestBody) {
        if (requestBody == null || requestBody.isEmpty()) {
            return false;
        }

        // 检查必需的加密字段
        boolean hasEncryptedData = requestBody.containsKey("encryptedData");
        boolean hasEncryptedKey = requestBody.containsKey("encryptedKey");
        boolean hasKeyId = requestBody.containsKey("keyId");
        boolean hasSignature = requestBody.containsKey("signature");

        boolean isEncrypted = hasEncryptedData && hasEncryptedKey && hasKeyId && hasSignature;

        logger.debug("加密请求检查 - 加密数据: {}, 加密密钥: {}, 密钥ID: {}, 签名: {}, 结果: {}",
                    hasEncryptedData, hasEncryptedKey, hasKeyId, hasSignature, isEncrypted);

        return isEncrypted;
    }

    /**
     * 解密请求数据
     *
     * @param requestBody 加密的请求体
     * @return 解密后的数据
     */
    public Map<String, Object> decryptRequest(Map<String, Object> requestBody) {
        DecryptResult result = decryptRequestWithDetails(requestBody);
        return result.getData();
    }

    /**
     * 解密请求并返回详细信息
     *
     * @param requestBody 加密的请求体
     * @return 包含解密数据和会话信息的结果
     */
    public DecryptResult decryptRequestWithDetails(Map<String, Object> requestBody) {
        try {
            decryptCount.incrementAndGet();

            // 1. 验证请求格式
            validateEncryptedRequest(requestBody);

            // 2. 提取加密组件
            String encryptedData = (String) requestBody.get("encryptedData");
            String encryptedSessionKey = (String) requestBody.get("encryptedKey");
            String keyId = (String) requestBody.get("keyId");
            String signature = (String) requestBody.get("signature");
            Long timestamp = getLongValue(requestBody.get("timestamp"));

            logger.debug("开始解密请求，密钥ID: {}, 时间戳: {}", keyId, timestamp);

            // 3. 验证密钥ID
            if (!keyId.equals(keyManagementService.getCurrentKeyId())) {
                throw new SecurityException("密钥ID不匹配，可能是密钥已轮换");
            }

            // 4. 解密会话密钥
            PrivateKey privateKey = keyManagementService.getCurrentPrivateKey();
            String sessionKey = encryptionUtil.rsaDecrypt(encryptedSessionKey, privateKey);

            // 5. 验证HMAC签名
            String dataToVerify = encryptedData + encryptedSessionKey + keyId + timestamp;
            if (!encryptionUtil.verifyHmacSignature(dataToVerify, signature, sessionKey)) {
                throw new SecurityException("HMAC签名验证失败");
            }

            // 6. 解密数据
            String decryptedJson = encryptionUtil.aesDecrypt(encryptedData, sessionKey);

            // 7. 解析JSON数据
            @SuppressWarnings("unchecked")
            Map<String, Object> decryptedData = objectMapper.readValue(decryptedJson, Map.class);

            // 8. 缓存会话密钥
            cacheSessionKey(keyId, sessionKey, timestamp);

            logger.debug("请求解密成功，数据大小: {} 字节", decryptedJson.length());

            return new DecryptResult(decryptedData, keyId, sessionKey);

        } catch (JsonProcessingException e) {
            logger.error("JSON解析失败", e);
            throw new IllegalArgumentException("解密数据格式错误", e);
        } catch (SecurityException e) {
            logger.error("安全验证失败: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            logger.error("解密请求失败", e);
            throw new RuntimeException("解密失败", e);
        }
    }

    /**
     * 加密响应数据
     *
     * @param responseData 响应数据
     * @param keyId 密钥ID
     * @param sessionKey 会话密钥
     * @return 加密的响应
     */
    public Map<String, Object> encryptResponse(Map<String, Object> responseData,
                                              String keyId,
                                              String sessionKey) {
        try {
            encryptCount.incrementAndGet();

            // 1. 将响应数据转换为JSON
            String responseJson = objectMapper.writeValueAsString(responseData);

            // 2. 使用AES加密响应数据
            String encryptedData = encryptionUtil.aesEncrypt(responseJson, sessionKey);

            // 3. 生成时间戳
            long timestamp = System.currentTimeMillis();

            // 4. 生成HMAC签名
            String dataToSign = encryptedData + keyId + timestamp;
            String signature = encryptionUtil.generateHmacSignature(dataToSign, sessionKey);

            // 5. 构造加密响应
            Map<String, Object> encryptedResponse = new HashMap<>();
            encryptedResponse.put("encryptedData", encryptedData);
            encryptedResponse.put("keyId", keyId);
            encryptedResponse.put("timestamp", timestamp);
            encryptedResponse.put("signature", signature);

            logger.debug("响应加密成功，密钥ID: {}, 原始大小: {} 字节",
                        keyId, responseJson.length());

            return encryptedResponse;

        } catch (JsonProcessingException e) {
            logger.error("JSON序列化失败", e);
            throw new RuntimeException("响应数据序列化失败", e);
        } catch (Exception e) {
            logger.error("加密响应失败", e);
            throw new RuntimeException("响应加密失败", e);
        }
    }

    /**
     * 获取缓存统计信息
     *
     * @return 缓存和性能统计
     */
    public Map<String, Object> getCacheStats() {
        Map<String, Object> stats = new HashMap<>();

        // 基本统计
        stats.put("encryptCount", encryptCount.get());
        stats.put("decryptCount", decryptCount.get());
        stats.put("cacheHitCount", cacheHitCount.get());
        stats.put("cacheMissCount", cacheMissCount.get());

        // 缓存信息
        stats.put("sessionKeyCacheSize", sessionKeyCache.size());

        // 密钥信息
        stats.put("currentKeyId", keyManagementService.getCurrentKeyId());
        stats.put("keyGeneratedTime", keyManagementService.getKeyGeneratedTime());
        stats.put("keyRotationNeeded", keyManagementService.isKeyRotationNeeded());

        // 计算缓存命中率
        long totalCacheAccess = cacheHitCount.get() + cacheMissCount.get();
        double hitRate = totalCacheAccess > 0 ? (double) cacheHitCount.get() / totalCacheAccess : 0.0;
        stats.put("cacheHitRate", Math.round(hitRate * 10000.0) / 100.0); // 保留两位小数

        return stats;
    }

    /**
     * 清理过期的会话密钥缓存
     */
    public void cleanupExpiredSessionKeys() {
        long currentTime = System.currentTimeMillis();
        long expireThreshold = 30 * 60 * 1000; // 30分钟过期

        sessionKeyCache.entrySet().removeIf(entry -> {
            SessionKeyInfo keyInfo = entry.getValue();
            return (currentTime - keyInfo.getTimestamp()) > expireThreshold;
        });

        logger.debug("清理过期会话密钥，当前缓存大小: {}", sessionKeyCache.size());
    }

    // ============ 私有方法 ============

    /**
     * 验证加密请求的格式
     */
    private void validateEncryptedRequest(Map<String, Object> requestBody) {
        if (requestBody == null || requestBody.isEmpty()) {
            throw new IllegalArgumentException("请求体不能为空");
        }

        String[] requiredFields = {"encryptedData", "encryptedKey", "keyId", "signature", "timestamp"};
        for (String field : requiredFields) {
            if (!requestBody.containsKey(field) || requestBody.get(field) == null) {
                throw new IllegalArgumentException("缺少必需字段: " + field);
            }
        }

        // 验证时间戳
        Long timestamp = getLongValue(requestBody.get("timestamp"));
        if (timestamp == null) {
            throw new IllegalArgumentException("无效的时间戳格式");
        }

        // 验证时间戳不能太旧（防重放攻击）
        long currentTime = System.currentTimeMillis();
        long maxAge = 5 * 60 * 1000; // 5分钟
        if (Math.abs(currentTime - timestamp) > maxAge) {
            throw new SecurityException("请求时间戳过期");
        }
    }

    /**
     * 缓存会话密钥
     */
    private void cacheSessionKey(String keyId, String sessionKey, Long timestamp) {
        SessionKeyInfo keyInfo = new SessionKeyInfo(sessionKey, timestamp);
        sessionKeyCache.put(keyId + "_" + timestamp, keyInfo);

        // 限制缓存大小
        if (sessionKeyCache.size() > 1000) {
            cleanupExpiredSessionKeys();
        }
    }

    /**
     * 安全地获取Long值
     */
    private Long getLongValue(Object value) {
        if (value == null) {
            return null;
        }
        if (value instanceof Long) {
            return (Long) value;
        }
        if (value instanceof Integer) {
            return ((Integer) value).longValue();
        }
        if (value instanceof String) {
            try {
                return Long.parseLong((String) value);
            } catch (NumberFormatException e) {
                return null;
            }
        }
        return null;
    }

    // ============ 内部类 ============

    /**
     * 会话密钥信息
     */
    private static class SessionKeyInfo {
        private final String sessionKey;
        private final long timestamp;

        public SessionKeyInfo(String sessionKey, long timestamp) {
            this.sessionKey = sessionKey;
            this.timestamp = timestamp;
        }

        public String getSessionKey() {
            return sessionKey;
        }

        public long getTimestamp() {
            return timestamp;
        }
    }

    /**
     * 解密结果包装类
     */
    public static class DecryptResult {
        private final Map<String, Object> data;
        private final String keyId;
        private final String sessionKey;

        public DecryptResult(Map<String, Object> data, String keyId, String sessionKey) {
            this.data = data;
            this.keyId = keyId;
            this.sessionKey = sessionKey;
        }

        public Map<String, Object> getData() {
            return data;
        }

        public String getKeyId() {
            return keyId;
        }

        public String getSessionKey() {
            return sessionKey;
        }
    }
}
