import request from './index'

/**
 * 用户资料相关API
 */

// 获取用户资料
export function getUserProfile(userId) {
  return request({
    url: `/api/users/profile/${userId}`,
    method: 'get'
  })
}

// 获取当前用户资料
export function getCurrentUserProfile() {
  return request({
    url: '/api/users/profile/current',
    method: 'get'
  })
}

// 更新用户资料
export function updateUserProfile(profileData) {
  return request({
    url: '/api/users/profile/my',
    method: 'put',
    data: profileData
  })
}

// 上传头像
export function uploadAvatar(file) {
  const formData = new FormData()
  formData.append('file', file)

  return request({
    url: '/api/users/profile/my/avatar',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 删除头像
export function deleteAvatar() {
  return request({
    url: '/api/users/profile/my/avatar',
    method: 'delete'
  })
}

// 获取用户资料列表（管理员功能）
export function getUserProfiles(page = 0, size = 10, search = '') {
  return request({
    url: '/api/users/profile/list',
    method: 'get',
    params: {
      page,
      size,
      search
    }
  })
}

// 更新资料可见性设置
export function updateProfileVisibility(visibilitySettings) {
  return request({
    url: '/api/users/profile/my/visibility',
    method: 'put',
    data: visibilitySettings
  })
}
