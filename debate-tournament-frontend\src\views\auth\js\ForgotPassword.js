import { ref, reactive } from 'vue';
import { useRouter } from 'vue-router';
import { forgotPassword } from '@/api/auth';
import { useEncryptionStore } from '@/store/encryption';

export default {
  name: 'ForgotPassword',
  setup() {
    const router = useRouter()

    const formData = reactive({
      email: '',
      captcha: ''
    })

    const errors = reactive({
      email: '',
      captcha: '',
      general: ''
    })

    const loading = ref(false)
    const captchaUrl = ref('/api/captcha/image?' + Date.now())

    const validateEmail = (email) => {
      const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      return re.test(email)
    }

    const validateForm = () => {
      let isValid = true
      errors.email = ''
      errors.captcha = ''
      errors.general = ''

      if (!formData.email) {
        errors.email = '邮箱不能为空'
        isValid = false
      } else if (!validateEmail(formData.email)) {
        errors.email = '请输入有效的邮箱地址'
        isValid = false
      }

      if (!formData.captcha) {
        errors.captcha = '验证码不能为空'
        isValid = false
      } else if (formData.captcha.length !== 4) {
        errors.captcha = '验证码必须是4位'
        isValid = false
      }

      return isValid
    }

    const handleSubmit = async () => {
      if (!validateForm()) return;

      loading.value = true;
      errors.general = '';

      try {
        const encryptionStore = useEncryptionStore();
        await encryptionStore.initialize();
        const encryptedRequest = encryptionStore.encryptRequest({
          email: formData.email,
          captcha: formData.captcha
        });

        await forgotPassword(encryptedRequest);
        // 显示成功消息
        alert('重置密码链接已发送到您的邮箱,请查收');
        router.push('/login');
      } catch (error) {
        if (error.response?.status === 400) {
          if (error.response.data.field === 'captcha') {
            errors.captcha = error.response.data.message;
            refreshCaptcha();
          } else {
            errors[error.response.data.field] = error.response.data.message;
          }
        } else {
          errors.general = error.response?.data?.message || '发送重置链接失败,请重试';
        }
      } finally {
        loading.value = false;
      }
    };

    const refreshCaptcha = () => {
      captchaUrl.value = '/api/captcha/image?' + Date.now()
      formData.captcha = ''
    }

    const goToLogin = () => {
      router.push('/login')
    }

    return {
      formData,
      errors,
      loading,
      captchaUrl,
      handleSubmit,
      refreshCaptcha,
      goToLogin
    }
  }
}
