package com.debate_ournament.ai.service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.CompletableFuture;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.debate_ournament.ai.entity.AiConfig;
import com.debate_ournament.ai.entity.ChatMessage;
import com.debate_ournament.ai.entity.MediaType;
import com.debate_ournament.ai.entity.MultiModalResponse;
import com.debate_ournament.ai.repository.AiConfigRepository;
import com.debate_ournament.ai.repository.MultiModalResponseRepository;
import com.debate_ournament.ai.service.TextToSpeechService.VoiceConfig;

/**
 * 多模态AI服务
 * 整合文本生成和语音合成功能
 */
@Service
@Transactional
public class MultiModalService {

    private static final Logger logger = LoggerFactory.getLogger(MultiModalService.class);

    private final AiChatService aiChatService;
    private final TextToSpeechFactory textToSpeechFactory;
    private final MultiModalResponseRepository multiModalResponseRepository;
    private final AiConfigRepository aiConfigRepository;

    public MultiModalService(AiChatService aiChatService,
                            TextToSpeechFactory textToSpeechFactory,
                            MultiModalResponseRepository multiModalResponseRepository,
                            AiConfigRepository aiConfigRepository) {
        this.aiChatService = aiChatService;
        this.textToSpeechFactory = textToSpeechFactory;
        this.multiModalResponseRepository = multiModalResponseRepository;
        this.aiConfigRepository = aiConfigRepository;
    }

    /**
     * 多模态响应请求类
     */
    public static class MultiModalRequest {
        private String content;           // 用户输入内容
        private String sessionId;        // 会话ID
        private Long chatConfigId;       // 聊天AI配置ID
        private Long ttsConfigId;        // TTS配置ID
        private MediaType outputType;    // 输出类型
        private VoiceConfig voiceConfig; // 语音配置
        private boolean includeText;     // 是否包含文本
        private boolean includeAudio;    // 是否包含音频

        // Getters and Setters
        public String getContent() { return content; }
        public void setContent(String content) { this.content = content; }

        public String getSessionId() { return sessionId; }
        public void setSessionId(String sessionId) { this.sessionId = sessionId; }

        public Long getChatConfigId() { return chatConfigId; }
        public void setChatConfigId(Long chatConfigId) { this.chatConfigId = chatConfigId; }

        public Long getTtsConfigId() { return ttsConfigId; }
        public void setTtsConfigId(Long ttsConfigId) { this.ttsConfigId = ttsConfigId; }

        public MediaType getOutputType() { return outputType; }
        public void setOutputType(MediaType outputType) { this.outputType = outputType; }

        public VoiceConfig getVoiceConfig() { return voiceConfig; }
        public void setVoiceConfig(VoiceConfig voiceConfig) { this.voiceConfig = voiceConfig; }

        public boolean isIncludeText() { return includeText; }
        public void setIncludeText(boolean includeText) { this.includeText = includeText; }

        public boolean isIncludeAudio() { return includeAudio; }
        public void setIncludeAudio(boolean includeAudio) { this.includeAudio = includeAudio; }
    }

    /**
     * 生成多模态响应
     */
    public MultiModalResponse generateMultiModalResponse(Long userId, MultiModalRequest request) {
        try {
            long startTime = System.currentTimeMillis();

            // 1. 生成文本回复
            ChatMessage chatResponse = aiChatService.sendMessage(
                userId, request.getSessionId(), request.getContent(), request.getChatConfigId());

            // 2. 创建多模态响应记录
            MultiModalResponse response = new MultiModalResponse();
            response.setSessionId(request.getSessionId());
            response.setUserId(userId);
            response.setChatMessageId(chatResponse.getId());
            response.setRequestText(request.getContent());
            response.setTextContent(chatResponse.getContent());
            response.setAiProvider(chatResponse.getAiProvider());
            response.setModelName(chatResponse.getModelName());
            response.setStatus("processing");

            // 根据请求类型设置主要媒体类型
            if (request.isIncludeAudio()) {
                response.setPrimaryMediaType(MediaType.AUDIO);
            } else {
                response.setPrimaryMediaType(MediaType.TEXT);
            }

            response = multiModalResponseRepository.save(response);

            // 3. 生成语音（如果需要）
            if (request.isIncludeAudio()) {
                generateAudioResponse(response, request);
            }

            // 4. 更新处理时间和状态
            long processingTime = System.currentTimeMillis() - startTime;
            response.setProcessingTime(processingTime);
            response.setStatus("completed");
            response.setUpdatedAt(LocalDateTime.now());

            return multiModalResponseRepository.save(response);

        } catch (Exception e) {
            logger.error("生成多模态响应失败 - 用户: {}, 错误: {}", userId, e.getMessage(), e);
            throw new RuntimeException("多模态响应生成失败: " + e.getMessage(), e);
        }
    }

    /**
     * 异步生成多模态响应
     */
    public CompletableFuture<MultiModalResponse> generateMultiModalResponseAsync(Long userId,
                                                                                MultiModalRequest request) {
        return CompletableFuture.supplyAsync(() -> generateMultiModalResponse(userId, request));
    }

    /**
     * 仅生成语音响应
     */
    public MultiModalResponse generateAudioOnlyResponse(Long userId, String text, String sessionId,
                                                       Long ttsConfigId, VoiceConfig voiceConfig) {
        try {
            // 创建响应记录
            MultiModalResponse response = MultiModalResponse.createAudioResponse(sessionId, userId, null, text);
            response.setStatus("processing");
            response = multiModalResponseRepository.save(response);

            // 生成语音
            MultiModalRequest request = new MultiModalRequest();
            request.setTtsConfigId(ttsConfigId);
            request.setVoiceConfig(voiceConfig);
            request.setIncludeAudio(true);

            generateAudioResponse(response, request);

            response.setStatus("completed");
            response.setUpdatedAt(LocalDateTime.now());

            return multiModalResponseRepository.save(response);

        } catch (Exception e) {
            logger.error("生成语音响应失败", e);
            throw new RuntimeException("语音响应生成失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取用户的多模态响应列表
     */
    @Transactional(readOnly = true)
    public Page<MultiModalResponse> getUserResponses(Long userId, Pageable pageable) {
        return multiModalResponseRepository.findByUserIdOrderByCreatedAtDesc(userId, pageable);
    }

    /**
     * 获取会话的多模态响应
     */
    @Transactional(readOnly = true)
    public List<MultiModalResponse> getSessionResponses(String sessionId) {
        return multiModalResponseRepository.findBySessionIdOrderByCreatedAtAsc(sessionId);
    }

    /**
     * 根据媒体类型获取响应
     */
    @Transactional(readOnly = true)
    public List<MultiModalResponse> getResponsesByMediaType(MediaType mediaType) {
        return multiModalResponseRepository.findByPrimaryMediaTypeOrderByCreatedAtDesc(mediaType);
    }

    /**
     * 删除多模态响应
     */
    public void deleteResponse(Long responseId, Long userId) {
        MultiModalResponse response = multiModalResponseRepository.findById(responseId)
                .orElseThrow(() -> new RuntimeException("响应不存在"));

        if (!response.getUserId().equals(userId)) {
            throw new RuntimeException("无权限删除此响应");
        }

        // TODO: 删除关联的音频文件

        multiModalResponseRepository.delete(response);
        logger.info("删除多模态响应: {} (用户: {})", responseId, userId);
    }

    /**
     * 获取统计信息
     */
    @Transactional(readOnly = true)
    public MultiModalStats getMultiModalStats(Long userId) {
        MultiModalStats stats = new MultiModalStats();
        stats.setTotalResponses(multiModalResponseRepository.countByUserId(userId));
        stats.setTextResponses(multiModalResponseRepository.countByUserIdAndPrimaryMediaType(userId, MediaType.TEXT));
        stats.setAudioResponses(multiModalResponseRepository.countByUserIdAndPrimaryMediaType(userId, MediaType.AUDIO));

        Long totalProcessingTime = multiModalResponseRepository.sumProcessingTimeByUserId(userId);
        stats.setTotalProcessingTime(totalProcessingTime != null ? totalProcessingTime : 0L);

        Double totalCost = multiModalResponseRepository.sumCostByUserId(userId);
        stats.setTotalCost(totalCost != null ? totalCost : 0.0);

        return stats;
    }

    /**
     * 生成音频响应
     */
    private void generateAudioResponse(MultiModalResponse response, MultiModalRequest request) {
        try {
            // 获取TTS配置
            AiConfig ttsConfig = getTtsConfig(request.getTtsConfigId());
            if (ttsConfig == null) {
                throw new RuntimeException("未找到TTS配置");
            }

            VoiceConfig voiceConfig = request.getVoiceConfig();
            if (voiceConfig == null) {
                voiceConfig = createDefaultVoiceConfig();
            }

            // 获取TTS服务并生成语音
            TextToSpeechService ttsService = textToSpeechFactory.getTtsClient(ttsConfig);
            String audioUrl = ttsService.synthesizeText(
                response.getTextContent(), voiceConfig, ttsConfig);

            // 获取音频信息
            TextToSpeechService.AudioInfo audioInfo = null;
            // audioInfo = textToSpeechService.getAudioInfo(audioFile); // 需要文件路径

            // 更新响应记录
            response.setAudioUrl(audioUrl);
            response.setAudioFormat(voiceConfig.getFormat());
            if (audioInfo != null) {
                response.setAudioDuration(audioInfo.getDuration());
                response.setFileSize(audioInfo.getFileSize());
                response.setMimeType("audio/" + audioInfo.getFormat());
            }

        } catch (Exception e) {
            logger.error("生成音频失败", e);
            response.setStatus("failed");
            response.setErrorMessage(e.getMessage());
            throw e;
        }
    }

    /**
     * 获取TTS配置
     */
    private AiConfig getTtsConfig(Long configId) {
        if (configId != null) {
            return aiConfigRepository.findById(configId).orElse(null);
        }
        // 查找支持TTS的默认配置
        return aiConfigRepository.findByIsDefaultTrueAndEnabledTrue().orElse(null);
    }

    /**
     * 创建默认语音配置
     */
    private VoiceConfig createDefaultVoiceConfig() {
        VoiceConfig config = new VoiceConfig();
        config.setVoice("alloy");
        config.setLanguage("zh-CN");
        config.setSpeed(1.0);
        config.setPitch(1.0);
        config.setFormat("mp3");
        return config;
    }

    /**
     * 多模态统计信息类
     */
    public static class MultiModalStats {
        private long totalResponses;      // 总响应数
        private long textResponses;       // 文本响应数
        private long audioResponses;      // 音频响应数
        private long totalProcessingTime; // 总处理时间
        private double totalCost;         // 总成本

        // Getters and Setters
        public long getTotalResponses() { return totalResponses; }
        public void setTotalResponses(long totalResponses) { this.totalResponses = totalResponses; }

        public long getTextResponses() { return textResponses; }
        public void setTextResponses(long textResponses) { this.textResponses = textResponses; }

        public long getAudioResponses() { return audioResponses; }
        public void setAudioResponses(long audioResponses) { this.audioResponses = audioResponses; }

        public long getTotalProcessingTime() { return totalProcessingTime; }
        public void setTotalProcessingTime(long totalProcessingTime) { this.totalProcessingTime = totalProcessingTime; }

        public double getTotalCost() { return totalCost; }
        public void setTotalCost(double totalCost) { this.totalCost = totalCost; }
    }
}
