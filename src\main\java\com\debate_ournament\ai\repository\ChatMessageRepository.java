package com.debate_ournament.ai.repository;

import java.time.LocalDateTime;
import java.util.List;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.debate_ournament.ai.entity.ChatMessage;

/**
 * 聊天消息数据访问接口
 */
@Repository
public interface ChatMessageRepository extends JpaRepository<ChatMessage, Long> {

    /**
     * 根据会话ID查找消息
     */
    List<ChatMessage> findBySessionIdOrderByCreatedAtAsc(String sessionId);

    /**
     * 根据用户ID查找消息（分页）
     */
    Page<ChatMessage> findByUserIdOrderByCreatedAtDesc(Long userId, Pageable pageable);

    /**
     * 根据用户ID和会话ID查找消息
     */
    List<ChatMessage> findByUserIdAndSessionIdOrderByCreatedAtAsc(Long userId, String sessionId);

    /**
     * 查找用户的会话列表
     */
    @Query("SELECT DISTINCT cm.sessionId FROM ChatMessage cm WHERE cm.userId = :userId ORDER BY MAX(cm.createdAt) DESC")
    List<String> findDistinctSessionIdsByUserIdOrderByLatestMessage(@Param("userId") Long userId);

    /**
     * 根据AI提供商统计消息数量
     */
    @Query("SELECT cm.aiProvider, COUNT(cm) FROM ChatMessage cm WHERE cm.aiProvider IS NOT NULL GROUP BY cm.aiProvider")
    List<Object[]> countMessagesByAiProvider();

    /**
     * 统计用户的消息总数
     */
    long countByUserId(Long userId);

    /**
     * 统计指定时间范围内的消息数量
     */
    long countByCreatedAtBetween(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 查找最近的N条消息
     */
    List<ChatMessage> findTop10ByUserIdOrderByCreatedAtDesc(Long userId);

    /**
     * 删除指定时间之前的消息
     */
    void deleteByCreatedAtBefore(LocalDateTime cutoffTime);

    /**
     * 根据状态查找消息
     */
    List<ChatMessage> findByStatusOrderByCreatedAtDesc(String status);

    /**
     * 统计Token消耗总数
     */
    @Query("SELECT SUM(cm.tokenCount) FROM ChatMessage cm WHERE cm.tokenCount IS NOT NULL AND cm.userId = :userId")
    Long sumTokenCountByUserId(@Param("userId") Long userId);

    /**
     * 查找某个会话的最后一条消息
     */
    ChatMessage findTopBySessionIdOrderByCreatedAtDesc(String sessionId);

    /**
     * 根据AI配置ID查找消息
     */
    List<ChatMessage> findByAiConfigIdOrderByCreatedAtDesc(Long aiConfigId);

    /**
     * 查找失败的消息
     */
    List<ChatMessage> findByStatusAndCreatedAtAfterOrderByCreatedAtDesc(String status, LocalDateTime after);

    /**
     * 统计系统消息数量
     */
    long countByIsSystemMessageTrue();
}
