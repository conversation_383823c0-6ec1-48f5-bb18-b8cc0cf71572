import { defineStore } from 'pinia';
import axios from '@/api';

export const useAiModelStore = defineStore('aiModel', {
  state: () => ({
    availableModels: [],
    selectedModels: {
      host: null,
      supporter: null,
      opposer: null,
      judge1: null,
      judge2: null,
      judge3: null
    },
    loading: false,
    error: null
  }),

  getters: {
    isModelSelectionComplete: (state) => {
      return Object.values(state.selectedModels).every(model => model !== null);
    },
    getSelectedModel: (state) => (role) => {
      return state.selectedModels[role];
    }
  },

  actions: {
    // 获取可用的AI模型列表
    async fetchAvailableModels() {
      try {
        this.loading = true;
        const response = await axios.get('/api/ai-models');
        this.availableModels = response.data;
      } catch (error) {
        this.error = error.message;
        console.error('获取AI模型列表失败:', error);
      } finally {
        this.loading = false;
      }
    },

    // 选择AI模型
    async selectModel(role, modelId) {
      try {
        this.loading = true;
        const response = await axios.post(`/api/ai-models/${modelId}/select`, { role });
        this.selectedModels[role] = response.data;
      } catch (error) {
        this.error = error.message;
        console.error('选择AI模型失败:', error);
      } finally {
        this.loading = false;
      }
    },

    // 获取已选择的AI模型
    async getAIModel(role) {
      if (this.selectedModels[role]) {
        return this.selectedModels[role];
      }

      try {
        this.loading = true;
        const response = await axios.get(`/api/ai-models/selected/${role}`);
        this.selectedModels[role] = response.data;
        return response.data;
      } catch (error) {
        this.error = error.message;
        console.error('获取AI模型失败:', error);
        return null;
      } finally {
        this.loading = false;
      }
    },

    // 生成AI发言
    async generateSpeech(role, round) {
      try {
        this.loading = true;
        const model = await this.getAIModel(role);
        if (!model) {
          throw new Error('未找到对应的AI模型');
        }

        const response = await axios.post('/api/ai-models/generate-speech', {
          modelId: model.id,
          role,
          round
        });

        return response.data.content;
      } catch (error) {
        this.error = error.message;
        console.error('生成AI发言失败:', error);
        return '抱歉，生成发言时出现错误。';
      } finally {
        this.loading = false;
      }
    },

    // 自定义AI模型
    async createCustomModel(modelData) {
      try {
        this.loading = true;
        const response = await axios.post('/api/ai-models/custom', modelData);
        this.availableModels.push(response.data);
        return response.data;
      } catch (error) {
        this.error = error.message;
        console.error('创建自定义AI模型失败:', error);
        throw error;
      } finally {
        this.loading = false;
      }
    },

    // 更新AI模型设置
    async updateModelSettings(modelId, settings) {
      try {
        this.loading = true;
        const response = await axios.put(`/api/ai-models/${modelId}/settings`, settings);
        const index = this.availableModels.findIndex(model => model.id === modelId);
        if (index !== -1) {
          this.availableModels[index] = response.data;
        }
        return response.data;
      } catch (error) {
        this.error = error.message;
        console.error('更新AI模型设置失败:', error);
        throw error;
      } finally {
        this.loading = false;
      }
    },

    // 重置选择的AI模型
    resetSelectedModels() {
      this.selectedModels = {
        host: null,
        supporter: null,
        opposer: null,
        judge1: null,
        judge2: null,
        judge3: null
      };
    },

    // 清除错误信息
    clearError() {
      this.error = null;
    }
  }
});
