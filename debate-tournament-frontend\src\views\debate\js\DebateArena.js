import { ref, reactive, computed, onMounted, onBeforeUnmount, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useDebateStore } from '@/store/debate';
import { useUserStore } from '@/store/user';
import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';

export default {
  name: 'DebateArena',

  setup() {
    const route = useRoute();
    const router = useRouter();
    const debateStore = useDebateStore();
    const userStore = useUserStore();

    // 场景状态
    const canvasContainer = ref(null);
    const historyContainer = ref(null);
    const speechTextarea = ref(null);

    const scene = ref(null);
    const camera = ref(null);
    const renderer = ref(null);
    const controls = ref(null);

    const debate = reactive({
      id: route.params.id,
      title: '',
      category: '',
      description: '',
      status: 'waiting',
      rounds: 0,
      speeches: 0,
      duration: 0,
      turnDuration: 3,
      result: null,
      supporterScore: 0,
      opposerScore: 0,
      judgementSummary: ''
    });

    const loading = reactive({
      debate: true,
      history: true,
      submit: false,
      skip: false,
      action: false,
      favorite: false
    });

    const speechHistory = ref([]);
    const currentTurn = ref(null);
    const speechContent = ref('');
    const maxSpeechLength = 1000;
    const showRules = ref(false);
    const showResult = ref(false);
    const isLoadingMore = ref(false);
    const hasMoreHistory = ref(true);
    const currentPage = ref(1);

    // 用户角色与状态
    const userRole = ref(''); // supporter, opposer, judge, spectator
    const isFavorite = ref(false);

    // 计算属性
    const supporterParticipants = computed(() => {
      return debateStore.getParticipantsByDebateAndSide(debate.id, 'supporter') || [];
    });

    const opposerParticipants = computed(() => {
      return debateStore.getParticipantsByDebateAndSide(debate.id, 'opposer') || [];
    });

    const judgeParticipants = computed(() => {
      return debateStore.getParticipantsByDebateAndSide(debate.id, 'judge') || [];
    });

    const isParticipant = computed(() => {
      return userRole.value !== 'spectator';
    });

    const isMyTurn = computed(() => {
      if (!currentTurn.value || !userStore.user) return false;
      return currentTurn.value.participantId === userStore.user.id;
    });

    const isJudge = computed(() => {
      return userRole.value === 'judge';
    });

    const isSpectator = computed(() => {
      return userRole.value === 'spectator';
    });

    const canSubmit = computed(() => {
      return speechContent.value.trim().length > 0 &&
             speechContent.value.length <= maxSpeechLength;
    });

    const currentSpeaker = computed(() => {
      if (!currentTurn.value) return null;

      // 查找当前发言者的信息
      const allParticipants = [
        ...supporterParticipants.value,
        ...opposerParticipants.value,
        ...judgeParticipants.value
      ];

      return allParticipants.find(p => p.id === currentTurn.value.participantId);
    });

    // 方法
    const initScene = () => {
      if (!canvasContainer.value) return;

      // 创建场景
      scene.value = new THREE.Scene();
      scene.value.background = new THREE.Color(0xf5f5f5);

      // 创建相机
      camera.value = new THREE.PerspectiveCamera(
        75,
        canvasContainer.value.clientWidth / canvasContainer.value.clientHeight,
        0.1,
        1000
      );
      camera.value.position.set(0, 5, 10);

      // 创建渲染器
      renderer.value = new THREE.WebGLRenderer({ antialias: true });
      renderer.value.setSize(
        canvasContainer.value.clientWidth,
        canvasContainer.value.clientHeight
      );
      renderer.value.setPixelRatio(window.devicePixelRatio);
      canvasContainer.value.appendChild(renderer.value.domElement);

      // 添加环境光
      const ambientLight = new THREE.AmbientLight(0xffffff, 0.5);
      scene.value.add(ambientLight);

      // 添加方向光
      const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
      directionalLight.position.set(0, 10, 10);
      scene.value.add(directionalLight);

      // 添加地板
      const floorGeometry = new THREE.PlaneGeometry(20, 20);
      const floorMaterial = new THREE.MeshStandardMaterial({
        color: 0x333333,
        side: THREE.DoubleSide
      });
      const floor = new THREE.Mesh(floorGeometry, floorMaterial);
      floor.rotation.x = Math.PI / 2;
      floor.position.y = -0.5;
      scene.value.add(floor);

      // 添加控制器
      controls.value = new OrbitControls(camera.value, renderer.value.domElement);
      controls.value.enableDamping = true;
      controls.value.dampingFactor = 0.05;

      // 开始动画循环
      animate();

      // 添加窗口大小变化事件监听
      window.addEventListener('resize', onWindowResize);
    };

    const animate = () => {
      requestAnimationFrame(animate);

      if (controls.value) {
        controls.value.update();
      }

      if (renderer.value && scene.value && camera.value) {
        renderer.value.render(scene.value, camera.value);
      }
    };

    const onWindowResize = () => {
      if (!canvasContainer.value || !camera.value || !renderer.value) return;

      camera.value.aspect = canvasContainer.value.clientWidth / canvasContainer.value.clientHeight;
      camera.value.updateProjectionMatrix();

      renderer.value.setSize(
        canvasContainer.value.clientWidth,
        canvasContainer.value.clientHeight
      );
    };

    const fetchDebate = async () => {
      loading.debate = true;
      try {
        await debateStore.fetchDebateById(debate.id);
        const debateData = debateStore.getDebateById(debate.id);
        if (!debateData) {
          router.push('/not-found');
          return;
        }

        Object.assign(debate, debateData);

        // 获取用户在辩论中的角色
        determineUserRole();

        // 检查用户是否收藏了辩论
        checkFavoriteStatus();

        // 如果辩论已经结束，显示结果页面
        if (debate.status === 'completed' && debate.result) {
          showResult.value = true;
        }
      } catch (error) {
        console.error('Error fetching debate:', error);
        router.push('/not-found');
      } finally {
        loading.debate = false;
      }
    };

    const fetchSpeechHistory = async () => {
      loading.history = true;
      try {
        const history = await debateStore.fetchDebateSpeechHistory(debate.id, 1);
        speechHistory.value = history.data || [];
        hasMoreHistory.value = history.hasMore || false;
        currentPage.value = 1;

        // 自动滚动到底部
        scrollToBottom();
      } catch (error) {
        console.error('Error fetching speech history:', error);
      } finally {
        loading.history = false;
      }
    };

    const loadMoreHistory = async () => {
      if (isLoadingMore.value || !hasMoreHistory.value) return;

      isLoadingMore.value = true;
      try {
        const nextPage = currentPage.value + 1;
        const history = await debateStore.fetchDebateSpeechHistory(debate.id, nextPage);

        // 将新加载的历史添加到顶部
        speechHistory.value = [...history.data, ...speechHistory.value];
        hasMoreHistory.value = history.hasMore || false;
        currentPage.value = nextPage;
      } catch (error) {
        console.error('Error loading more history:', error);
      } finally {
        isLoadingMore.value = false;
      }
    };

    const determineUserRole = () => {
      if (!userStore.isLoggedIn) {
        userRole.value = 'spectator';
        return;
      }

      const userId = userStore.user.id;

      // 检查用户是否是参与者
      const participant = [
        ...supporterParticipants.value,
        ...opposerParticipants.value,
        ...judgeParticipants.value
      ].find(p => p.id === userId);

      if (participant) {
        userRole.value = participant.side || 'spectator';
      } else {
        userRole.value = 'spectator';
      }
    };

    const checkFavoriteStatus = async () => {
      if (!userStore.isLoggedIn) {
        isFavorite.value = false;
        return;
      }

      try {
        const status = await debateStore.checkFavoriteStatus(debate.id);
        isFavorite.value = status;
      } catch (error) {
        console.error('Error checking favorite status:', error);
        isFavorite.value = false;
      }
    };

    const submitSpeech = async () => {
      if (!canSubmit.value || !isMyTurn.value) return;

      loading.submit = true;
      try {
        await debateStore.submitSpeech({
          debateId: debate.id,
          participantId: userStore.user.id,
          content: speechContent.value,
          side: userRole.value
        });

        // 清空发言内容
        speechContent.value = '';

        // 刷新辩论状态和历史记录
        await fetchDebate();
        await fetchSpeechHistory();
      } catch (error) {
        console.error('Error submitting speech:', error);
      } finally {
        loading.submit = false;
      }
    };

    const skipTurn = async () => {
      if (!isMyTurn.value) return;

      loading.skip = true;
      try {
        await debateStore.skipTurn({
          debateId: debate.id,
          participantId: userStore.user.id
        });

        // 刷新辩论状态
        await fetchDebate();
      } catch (error) {
        console.error('Error skipping turn:', error);
      } finally {
        loading.skip = false;
      }
    };

    const stopDebate = async () => {
      if (!isJudge.value || debate.status !== 'active') return;

      loading.action = true;
      try {
        await debateStore.stopDebate(debate.id);

        // 刷新辩论状态
        await fetchDebate();
        showResult.value = true;
      } catch (error) {
        console.error('Error stopping debate:', error);
      } finally {
        loading.action = false;
      }
    };

    const toggleFavorite = async () => {
      if (!userStore.isLoggedIn) {
        router.push('/login');
        return;
      }

      loading.favorite = true;
      try {
        if (isFavorite.value) {
          await debateStore.removeFavorite(debate.id);
        } else {
          await debateStore.addFavorite(debate.id);
        }

        isFavorite.value = !isFavorite.value;
      } catch (error) {
        console.error('Error toggling favorite:', error);
      } finally {
        loading.favorite = false;
      }
    };

    const reactToSpeech = async (speechId, reaction) => {
      if (!userStore.isLoggedIn) {
        router.push('/login');
        return;
      }

      try {
        await debateStore.reactToSpeech({
          speechId,
          userId: userStore.user.id,
          reaction
        });

        // 更新本地历史记录中的反应状态
        const speechIndex = speechHistory.value.findIndex(s => s.id === speechId);
        if (speechIndex !== -1) {
          const speech = speechHistory.value[speechIndex];
          const currentReaction = speech.userReaction;

          // 更新反应计数
          if (currentReaction === reaction) {
            // 取消反应
            speech.userReaction = null;
            if (reaction === 'like') speech.likes--;
            else if (reaction === 'dislike') speech.dislikes--;
          } else {
            // 添加新反应
            speech.userReaction = reaction;

            // 如果先前有其他反应，减少计数
            if (currentReaction === 'like') {
              speech.likes--;
              if (reaction === 'dislike') speech.dislikes++;
            } else if (currentReaction === 'dislike') {
              speech.dislikes--;
              if (reaction === 'like') speech.likes++;
            } else {
              // 没有先前反应
              if (reaction === 'like') speech.likes++;
              else if (reaction === 'dislike') speech.dislikes++;
            }
          }
        }
      } catch (error) {
        console.error('Error reacting to speech:', error);
      }
    };

    const copySpeech = (content) => {
      // 添加引用符号
      speechContent.value += `> ${content}\n\n`;

      // 聚焦到文本区域
      if (speechTextarea.value) {
        speechTextarea.value.focus();
      }
    };

    const clearSpeech = () => {
      speechContent.value = '';
    };

    const saveDraft = () => {
      if (!speechContent.value.trim()) return;

      localStorage.setItem(`speech_draft_${debate.id}`, speechContent.value);
    };

    const goToDebateHall = () => {
      router.push('/debate-hall');
    };

    const scrollToBottom = () => {
      if (!historyContainer.value) return;

      setTimeout(() => {
        historyContainer.value.scrollTop = historyContainer.value.scrollHeight;
      }, 100);
    };

    // 辅助方法
    const formatTime = (seconds) => {
      if (typeof seconds !== 'number') return '00:00';

      const mins = Math.floor(seconds / 60);
      const secs = Math.floor(seconds % 60);
      return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    };

    const formatDateTime = (timestamp) => {
      if (!timestamp) return '';

      const date = new Date(timestamp);
      return date.toLocaleString('zh-CN', {
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    };

    const formatSpeechContent = (content) => {
      if (!content) return '';

      // 将引用的文本标记出来（以 > 开头的段落）
      return content
        .split('\n')
        .map(line => {
          if (line.trim().startsWith('>')) {
            return `<div class="quoted-text">${line.trim().substring(1)}</div>`;
          }
          return line;
        })
        .join('<br>');
    };

    const getStatusText = (status) => {
      const statusMap = {
        'waiting': '等待中',
        'active': '进行中',
        'paused': '已暂停',
        'completed': '已结束'
      };
      return statusMap[status] || '未知状态';
    };

    const getRoleText = (role) => {
      const roleMap = {
        'supporter': '正方',
        'opposer': '反方',
        'judge': '裁判',
        'spectator': '观众',
        'host': '主持人'
      };
      return roleMap[role] || '未知角色';
    };

    // 生命周期钩子
    onMounted(async () => {
      await fetchDebate();
      await fetchSpeechHistory();

      // 初始化3D场景
      initScene();

      // 恢复草稿（如果有）
      const savedDraft = localStorage.getItem(`speech_draft_${debate.id}`);
      if (savedDraft) {
        speechContent.value = savedDraft;
      }

      // 定时更新辩论状态
      const statusInterval = setInterval(() => {
        if (debate.status === 'active') {
          fetchDebate();
        }
      }, 10000); // 每10秒更新一次

      onBeforeUnmount(() => {
        clearInterval(statusInterval);

        // 清理Three.js资源
        if (renderer.value) {
          renderer.value.dispose();
          canvasContainer.value?.removeChild(renderer.value.domElement);
        }

        window.removeEventListener('resize', onWindowResize);
      });
    });

    // 监听辩论状态变化
    watch(
      () => debate.status,
      (newStatus, oldStatus) => {
        if (newStatus === 'completed' && oldStatus !== 'completed') {
          showResult.value = true;
        }
      }
    );

    return {
      debate,
      loading,
      speechHistory,
      currentTurn,
      speechContent,
      maxSpeechLength,
      showRules,
      showResult,
      isLoadingMore,
      hasMoreHistory,
      canvasContainer,
      historyContainer,
      speechTextarea,
      userRole,
      isFavorite,
      supporterParticipants,
      opposerParticipants,
      judgeParticipants,
      isParticipant,
      isMyTurn,
      isJudge,
      isSpectator,
      canSubmit,
      currentSpeaker,
      submitSpeech,
      skipTurn,
      stopDebate,
      toggleFavorite,
      reactToSpeech,
      copySpeech,
      clearSpeech,
      saveDraft,
      loadMoreHistory,
      goToDebateHall,
      formatTime,
      formatDateTime,
      formatSpeechContent,
      getStatusText,
      getRoleText
    };
  }
};
