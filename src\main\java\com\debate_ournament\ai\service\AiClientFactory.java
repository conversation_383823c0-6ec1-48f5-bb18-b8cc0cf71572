package com.debate_ournament.ai.service;

import java.util.HashMap;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.debate_ournament.ai.entity.AiProvider;
import com.debate_ournament.ai.service.impl.AlibabaBailianClient;
import com.debate_ournament.ai.service.impl.DeepSeekClient;
import com.debate_ournament.ai.service.impl.OpenAiClient;
import com.debate_ournament.ai.service.impl.OpenRouterClient;
import com.debate_ournament.ai.service.impl.SiliconFlowClient;

/**
 * AI客户端工厂类
 * 根据提供商类型返回对应的客户端实例
 */
@Component
public class AiClientFactory {

    private final Map<AiProvider, AiClient> clientMap;

    @Autowired
    public AiClientFactory(
            OpenAiClient openAiClient,
            DeepSeekClient deepSeekClient,
            AlibabaBailianClient alibabaBailianClient,
            SiliconFlowClient siliconFlowClient,
            OpenRouterClient openRouterClient) {

        this.clientMap = new HashMap<>();
        clientMap.put(AiProvider.OPENAI, openAiClient);
        clientMap.put(AiProvider.DEEPSEEK, deepSeekClient);
        clientMap.put(AiProvider.ALIBABA_BAILIAN, alibabaBailianClient);
        clientMap.put(AiProvider.SILICONFLOW, siliconFlowClient);
        clientMap.put(AiProvider.OPENROUTER, openRouterClient);

        // 为了兼容性，也映射其他已有的提供商到OpenAI客户端
        // 这些可以在后续实现具体的客户端
        clientMap.put(AiProvider.ANTHROPIC, openAiClient);
        clientMap.put(AiProvider.BAIDU, openAiClient);
        clientMap.put(AiProvider.ALIBABA, openAiClient);
        clientMap.put(AiProvider.ZHIPU, openAiClient);
        clientMap.put(AiProvider.TENCENT, openAiClient);
        clientMap.put(AiProvider.MOONSHOT, openAiClient);
    }

    /**
     * 根据AI提供商获取对应的客户端
     *
     * @param provider AI提供商
     * @return AI客户端实例
     * @throws IllegalArgumentException 如果提供商不支持
     */
    public AiClient getClient(AiProvider provider) {
        AiClient client = clientMap.get(provider);
        if (client == null) {
            throw new IllegalArgumentException("不支持的AI提供商: " + provider);
        }
        return client;
    }

    /**
     * 检查是否支持指定的AI提供商
     *
     * @param provider AI提供商
     * @return 是否支持
     */
    public boolean isProviderSupported(AiProvider provider) {
        return clientMap.containsKey(provider);
    }

    /**
     * 获取所有支持的提供商
     *
     * @return 支持的提供商数组
     */
    public AiProvider[] getSupportedProviders() {
        return clientMap.keySet().toArray(new AiProvider[0]);
    }
}
