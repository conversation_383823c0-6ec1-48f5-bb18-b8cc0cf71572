<template>
  <div class="auth-container">
    <div class="auth-card">
      <div class="auth-header">
        <h1 class="auth-title">邮箱验证</h1>
        <p class="auth-subtitle">验证您的邮箱地址</p>
      </div>

      <div class="auth-body">
        <div v-if="loading" class="auth-loading">
          <div class="spinner"></div>
          <p>正在验证您的邮箱...</p>
        </div>

        <div v-else-if="error" class="auth-alert auth-alert--error">
          <h3>验证失败</h3>
          <p>{{ error }}</p>
          <div class="auth-actions">
            <button class="btn btn--primary" @click="resendVerification" :disabled="resendLoading">
              {{ resendLoading ? '发送中...' : '重新发送验证邮件' }}
            </button>
          </div>
        </div>

        <div v-else-if="success" class="auth-alert auth-alert--success">
          <h3>验证成功</h3>
          <p>您的邮箱已成功验证，现在可以使用所有功能了。</p>
          <div class="auth-actions">
            <button class="btn btn--primary" @click="goToHome">
              前往首页
            </button>
          </div>
        </div>
      </div>

      <div class="auth-footer">
        <div class="auth-links">
          <router-link to="/login" class="auth-link">返回登录</router-link>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import VerifyEmailJS from './js/VerifyEmail.js';
export default VerifyEmailJS;
</script>

<style lang="scss">
@use './scss/Auth.scss';

.auth-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-lg) 0;

  .spinner {
    margin-bottom: var(--spacing-md);
  }
}

.auth-actions {
  margin-top: var(--spacing-md);
  display: flex;
  justify-content: center;
}
</style>
