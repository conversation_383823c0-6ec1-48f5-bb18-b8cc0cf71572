package com.debate_ournament.exception;

/**
 * 用户未找到异常
 */
public class UserNotFoundException extends RuntimeException {

    public UserNotFoundException(String message) {
        super(message);
    }

    public UserNotFoundException(String message, Throwable cause) {
        super(message, cause);
    }

    public UserNotFoundException(Long userId) {
        super("用户未找到: ID = " + userId);
    }

    public UserNotFoundException(String field, String value) {
        super("用户未找到: " + field + " = " + value);
    }
}
