package com.debate_ournament.security;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import com.debate_ournament.users.entity.User;
import com.debate_ournament.users.service.UserService;

/**
 * 自定义用户详情服务
 */
@Service
public class CustomUserDetailsService implements UserDetailsService {

    private static final Logger logger = LoggerFactory.getLogger(CustomUserDetailsService.class);

    private final UserService userService;

    @Autowired
    public CustomUserDetailsService(UserService userService) {
        this.userService = userService;
    }

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        logger.debug("加载用户信息: username={}", username);

        User user = userService.findByUsernameOrEmail(username)
                .orElseThrow(() -> new UsernameNotFoundException("用户不存在: " + username));

        return new CustomUserPrincipal(user);
    }

    /**
     * 自定义用户主体类
     */
    public static class CustomUserPrincipal implements UserDetails {
        private final User user;

        public CustomUserPrincipal(User user) {
            this.user = user;
        }

        @Override
        public Collection<? extends GrantedAuthority> getAuthorities() {
            List<GrantedAuthority> authorities = new ArrayList<>();

            // 基础用户角色
            authorities.add(new SimpleGrantedAuthority("ROLE_USER"));

            // 根据用户状态添加额外权限
            if (user.getEmailVerified()) {
                authorities.add(new SimpleGrantedAuthority("ROLE_VERIFIED_USER"));
            }

            // 如果后续添加管理员功能，可以在这里扩展
            // if (user.isAdmin()) {
            //     authorities.add(new SimpleGrantedAuthority("ROLE_ADMIN"));
            // }

            return authorities;
        }

        @Override
        public String getPassword() {
            return user.getPassword();
        }

        @Override
        public String getUsername() {
            return user.getUsername();
        }

        @Override
        public boolean isAccountNonExpired() {
            // 账号不会过期
            return true;
        }

        @Override
        public boolean isAccountNonLocked() {
            return user.isAccountNonLocked();
        }

        @Override
        public boolean isCredentialsNonExpired() {
            // 凭证不会过期
            return true;
        }

        @Override
        public boolean isEnabled() {
            return user.getStatus() == User.UserStatus.ACTIVE;
        }

        // 获取用户对象
        public User getUser() {
            return user;
        }

        // 获取用户ID
        public Long getUserId() {
            return user.getId();
        }

        // 获取用户邮箱
        public String getEmail() {
            return user.getEmail();
        }

        // 检查是否邮箱已验证
        public boolean isEmailVerified() {
            return user.getEmailVerified();
        }
    }
}
