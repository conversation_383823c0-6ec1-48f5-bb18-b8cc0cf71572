package com.debate_ournament.ai.service;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import com.debate_ournament.ai.entity.MindMapNode;
import com.debate_ournament.ai.entity.MindMapResponse;

/**
 * 思维导图生成服务接口
 */
public interface MindMapService {

    /**
     * 基于文本内容生成思维导图
     */
    MindMapResponse generateMindMap(String content, String sessionId, Long userId);

    /**
     * 基于对话历史生成思维导图
     */
    MindMapResponse generateMindMapFromConversation(String sessionId, Long userId);

    /**
     * 生成结构化的思维导图数据
     */
    MindMapNode generateStructuredMindMap(String content, String topic);

    /**
     * 将思维导图导出为不同格式
     */
    String exportMindMap(Long mindMapId, String format);

    /**
     * 获取用户的思维导图列表
     */
    Page<MindMapResponse> getUserMindMaps(Long userId, Pageable pageable);

    /**
     * 删除思维导图
     */
    void deleteMindMap(Long mindMapId, Long userId);

    /**
     * 更新思维导图
     */
    MindMapResponse updateMindMap(Long mindMapId, Long userId, String title, String description, Boolean isPublic, List<String> tags);

    // 新增方法
    /**
     * 基于内容生成思维导图
     */
    MindMapResponse generateMindMapFromContent(Long userId, String content, String title, String description, Integer maxDepth, Integer maxNodes, Boolean isPublic, List<String> tags);

    /**
     * 基于会话生成思维导图
     */
    MindMapResponse generateMindMapFromSession(Long userId, String sessionId, String title, String description, Integer maxDepth, Integer maxNodes, Boolean isPublic, List<String> tags);

    /**
     * 异步生成思维导图
     */
    CompletableFuture<MindMapResponse> generateMindMapAsync(Long userId, String content, String title, String description, Integer maxDepth, Integer maxNodes, Boolean isPublic, List<String> tags);

    /**
     * 获取公开的思维导图
     */
    Page<MindMapResponse> getPublicMindMaps(Pageable pageable, String sortBy, List<String> tags);

    /**
     * 搜索思维导图
     */
    Page<MindMapResponse> searchMindMaps(Long userId, String keyword, Pageable pageable, String sortBy, List<String> tags);

    /**
     * 根据ID获取思维导图
     */
    MindMapResponse getMindMapById(Long id, Long userId);

    /**
     * 导出为JSON格式
     */
    String exportMindMapAsJson(Long id, Long userId);

    /**
     * 导出为Markdown格式
     */
    String exportMindMapAsMarkdown(Long id, Long userId);

    /**
     * 导出为XML格式
     */
    String exportMindMapAsXml(Long id, Long userId);

    /**
     * 导出为文本格式
     */
    String exportMindMapAsText(Long id, Long userId);

    /**
     * 获取用户思维导图统计信息
     */
    Map<String, Object> getUserMindMapStats(Long userId);

    /**
     * 批量删除思维导图
     */
    void batchDeleteMindMaps(List<Long> ids, Long userId);

    /**
     * 复制思维导图
     */
    MindMapResponse copyMindMap(Long id, Long userId, String newTitle);
}
