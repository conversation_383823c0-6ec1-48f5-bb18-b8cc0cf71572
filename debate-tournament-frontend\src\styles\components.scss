@use './variables' as v;
@use './mixins' as m;

/*
 * 组件样式库
 * 这个文件包含可复用的组件样式，遵循BEM命名规范
*/

// 导航栏
.navbar {
  position: sticky;
  top: 0;
  background-color: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  z-index: 100;

  &__container {
    @include m.flex-between;
    height: 60px;
    padding: 0 v.$spacing-md;
  }

  &__logo {
    display: flex;
    align-items: center;
    font-weight: 700;
    font-size: 1.5rem;
    color: v.$color-primary;

    img {
      height: 36px;
      margin-right: v.$spacing-sm;
    }
  }

  &__nav {
    display: none;

    @include m.respond-to(md) {
      display: flex;
    }

    .nav-item {
      margin-left: v.$spacing-md;

      a {
        position: relative;
        padding: v.$spacing-xs 0;
        color: v.$color-on-surface;
        font-weight: 500;

        &:hover,
        &.active {
          color: v.$color-primary;

          &::after {
            content: '';
            position: absolute;
            bottom: -4px;
            left: 0;
            width: 100%;
            height: 2px;
            background-color: v.$color-primary;
          }
        }
      }
    }
  }

  &__mobile-toggle {
    display: block;

    @include m.respond-to(md) {
      display: none;
    }
  }

  &__actions {
    display: flex;
    align-items: center;

    .action-btn {
      margin-left: v.$spacing-sm;
    }
  }
}

// 用户菜单
.user-menu {
  position: relative;
  display: flex;
  align-items: center;
  cursor: pointer;

  &__avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    overflow: hidden;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  &__name {
    margin-left: v.$spacing-sm;
    font-weight: 500;
    display: none;

    @include m.respond-to(md) {
      display: block;
    }
  }

  &__dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    width: 200px;
    margin-top: v.$spacing-sm;
    background-color: white;
    border-radius: v.$shape-corner-small;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    z-index: 100;

    &-item {
      display: block;
      padding: v.$spacing-sm v.$spacing-md;
      color: v.$color-on-surface;
      transition: background-color 0.2s ease;

      &:hover {
        background-color: #f5f5f5;
      }

      &--danger {
        color: v.$color-error;
      }
    }

    &-divider {
      height: 1px;
      background-color: #eee;
      margin: v.$spacing-xs 0;
    }
  }
}

// 辩论卡片
.debate-card {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: white;
  border-radius: v.$shape-corner-medium;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: transform 0.2s ease, box-shadow 0.2s ease;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  &__header {
    position: relative;
    padding: v.$spacing-md;
    border-bottom: 1px solid #eee;

    &-title {
      margin: 0 0 v.$spacing-xs;
      font-size: 1.25rem;
      font-weight: 600;
      color: v.$color-on-surface;
      @include m.text-truncate;
    }

    &-meta {
      display: flex;
      align-items: center;
      font-size: 0.85rem;
      color: v.$color-secondary;

      .category {
        padding: 2px v.$spacing-xs;
        background-color: rgba(v.$color-primary, 0.1);
        border-radius: 4px;
        margin-right: v.$spacing-sm;
      }

      .status {
        &--active {
          color: v.$color-success;
        }

        &--completed {
          color: v.$color-secondary;
        }

        &--waiting {
          color: v.$color-warning;
        }
      }
    }
  }

  &__body {
    padding: v.$spacing-md;
    flex-grow: 1;

    .debate-stats {
      display: flex;
      justify-content: space-around;
      margin-bottom: v.$spacing-md;

      &-item {
        text-align: center;

        .value {
          font-size: 1.5rem;
          font-weight: 600;
          color: v.$color-on-surface;
        }

        .label {
          font-size: 0.85rem;
          color: v.$color-secondary;
        }
      }
    }

    .debate-sides {
      display: flex;
      align-items: center;

      &-separator {
        margin: 0 v.$spacing-sm;
        font-weight: 600;
        color: v.$color-secondary;
      }

      .side {
        flex: 1;
        text-align: center;
        padding: v.$spacing-xs;
        border-radius: v.$shape-corner-small;

        &--supporter {
          @include m.debate-side-theme('supporter');
        }

        &--opposer {
          @include m.debate-side-theme('opposer');
        }

        .count {
          font-size: 1.25rem;
          font-weight: 600;
        }

        .label {
          font-size: 0.85rem;
        }
      }
    }
  }

  &__footer {
    padding: v.$spacing-md;
    border-top: 1px solid #eee;
    background-color: #f9f9f9;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .participants {
      display: flex;
      align-items: center;

      &-count {
        margin-left: v.$spacing-xs;
        font-size: 0.85rem;
        color: v.$color-secondary;
      }

      &-avatars {
        display: flex;

        .avatar {
          width: 24px;
          height: 24px;
          border-radius: 50%;
          border: 2px solid white;
          margin-left: -8px;

          &:first-child {
            margin-left: 0;
          }
        }
      }
    }

    .debate-actions {
      display: flex;
      gap: v.$spacing-xs;
    }
  }
}

// 发言气泡
.speech-bubble {
  position: relative;
  padding: v.$spacing-md;
  background-color: white;
  border-radius: v.$shape-corner-medium;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  margin-bottom: v.$spacing-md;

  &--supporter {
    border-left: 4px solid v.$debate-supporter-color;
  }

  &--opposer {
    border-left: 4px solid v.$debate-opposer-color;
  }

  &--judge {
    border-left: 4px solid v.$debate-neutral-color;
  }

  &__header {
    display: flex;
    justify-content: space-between;
    margin-bottom: v.$spacing-sm;

    &-author {
      display: flex;
      align-items: center;

      .avatar {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        margin-right: v.$spacing-xs;
      }

      .name {
        font-weight: 500;
      }

      .role {
        font-size: 0.75rem;

        &--supporter {
          color: v.$debate-supporter-color;
        }

        &--opposer {
          color: v.$debate-opposer-color;
        }

        &--judge {
          color: v.$debate-neutral-color;
        }
      }
    }

    &-meta {
      font-size: 0.75rem;
      color: v.$color-secondary;
    }
  }

  &__content {
    line-height: 1.6;

    .quoted-text {
      padding-left: v.$spacing-sm;
      border-left: 2px solid #ddd;
      color: v.$color-secondary;
      font-style: italic;
      margin: v.$spacing-xs 0;
    }
  }

  &__actions {
    display: flex;
    justify-content: flex-end;
    margin-top: v.$spacing-sm;

    .action-btn {
      background: none;
      border: none;
      padding: v.$spacing-xs v.$spacing-sm;
      margin-left: v.$spacing-xs;
      border-radius: v.$shape-corner-small;
      cursor: pointer;
      font-size: 0.9rem;

      &:hover {
        background-color: rgba(0, 0, 0, 0.05);
      }

      &.active {
        color: v.$color-primary;
        font-weight: 500;
      }
    }
  }
}

// 语音波浪动画
.voice-wave {
  display: flex;
  align-items: center;
  height: 20px;

  &__bar {
    width: 4px;
    background-color: v.$color-primary;
    border-radius: 2px;
    margin: 0 1px;
    height: 100%;
    animation: voice-wave 1s ease-in-out infinite;

    &:nth-child(1) {
      animation-delay: 0.0s;
    }

    &:nth-child(2) {
      animation-delay: 0.1s;
    }

    &:nth-child(3) {
      animation-delay: 0.2s;
    }

    &:nth-child(4) {
      animation-delay: 0.3s;
    }

    &:nth-child(5) {
      animation-delay: 0.4s;
    }
  }

  @keyframes voice-wave {
    0% {
      height: 20%;
    }

    50% {
      height: 100%;
    }

    100% {
      height: 20%;
    }
  }
}

// 类别徽章
.category-badge {
  display: inline-flex;
  align-items: center;
  padding: v.$spacing-xs v.$spacing-sm;
  border-radius: 16px;
  font-size: 0.85rem;
  background-color: rgba(v.$color-primary, 0.1);
  color: v.$color-primary;

  &__icon {
    margin-right: v.$spacing-xs;
    font-size: 1rem;
  }
}

// 分页组件
.pagination {
  display: flex;
  justify-content: center;
  margin: v.$spacing-lg 0;

  &__item {
    width: 36px;
    height: 36px;
    margin: 0 v.$spacing-xs;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: v.$shape-corner-small;
    background-color: white;
    border: 1px solid #eee;
    color: v.$color-on-surface;
    transition: all 0.2s ease;

    &:hover {
      border-color: v.$color-primary;
      color: v.$color-primary;
    }

    &.active {
      background-color: v.$color-primary;
      border-color: v.$color-primary;
      color: white;
    }

    &.disabled {
      opacity: 0.5;
      cursor: not-allowed;

      &:hover {
        border-color: #eee;
        color: v.$color-on-surface;
      }
    }
  }
}

// 步骤条
.steps {
  display: flex;
  margin: v.$spacing-lg 0;

  @include m.respond-to(md) {
    margin: v.$spacing-xl 0;
  }

  &__item {
    flex: 1;
    text-align: center;
    position: relative;

    &:not(:last-child):after {
      content: '';
      position: absolute;
      top: 16px;
      left: calc(50% + 16px);
      right: calc(50% - 16px);
      height: 2px;
      background-color: #eee;
      z-index: 1;
    }

    &.active {
      .steps__number {
        background-color: v.$color-primary;
        color: white;
      }

      .steps__title {
        color: v.$color-primary;
        font-weight: 600;
      }

      &:not(:last-child):after {
        background-color: v.$color-primary;
      }
    }

    &.completed {
      .steps__number {
        background-color: v.$color-success;
        color: white;
      }

      &:not(:last-child):after {
        background-color: v.$color-success;
      }
    }
  }

  &__number {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #eee;
    color: v.$color-secondary;
    font-weight: 500;
    margin: 0 auto v.$spacing-sm;
    position: relative;
    z-index: 2;
  }

  &__title {
    font-size: 0.9rem;
    margin-bottom: v.$spacing-xs;
  }

  &__desc {
    font-size: 0.8rem;
    color: v.$color-secondary;
    display: none;

    @include m.respond-to(md) {
      display: block;
    }
  }
}
