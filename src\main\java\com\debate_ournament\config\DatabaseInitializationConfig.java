package com.debate_ournament.config;

import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.ResultSet;

import javax.sql.DataSource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.core.io.ClassPathResource;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.datasource.init.ResourceDatabasePopulator;
import org.springframework.transaction.annotation.Transactional;

/**
 * 数据库初始化配置类
 * 负责在应用启动时检查并初始化数据库结构和数据
 *
 * <AUTHOR> Debate Tournament Platform
 * @version 1.0
 * @since 2025-05-25
 */
@Configuration
public class DatabaseInitializationConfig {

    private static final Logger log = LoggerFactory.getLogger(DatabaseInitializationConfig.class);

    @Autowired
    private DataSource dataSource;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Value("${app.database.init.enabled:true}")
    private boolean databaseInitEnabled;

    @Value("${app.database.init.force:false}")
    private boolean forceInit;

    @Value("${app.database.init.script-path:db/init-database-merged.sql}")
    private String initScriptPath;

    /**
     * 数据库初始化命令行运行器
     * 在应用启动完成后执行数据库初始化检查
     */
    @Bean
    @Profile("!test") // 测试环境下不执行
    public CommandLineRunner databaseInitializer() {
        return args -> {
            if (!databaseInitEnabled) {
                log.info("数据库初始化已禁用，跳过初始化步骤");
                return;
            }

            log.info("开始检查数据库初始化状态...");

            try {
                if (shouldInitializeDatabase()) {
                    log.info("检测到数据库需要初始化，开始执行初始化脚本...");
                    initializeDatabase();
                    log.info("数据库初始化完成！");
                } else {
                    log.info("数据库已存在必要的表结构，跳过初始化");
                    logDatabaseInfo();
                }
            } catch (Exception e) {
                log.error("数据库初始化过程中发生错误: {}", e.getMessage(), e);
                if (forceInit) {
                    log.warn("强制初始化模式已启用，应用将继续启动");
                } else {
                    throw new RuntimeException("数据库初始化失败，应用启动中止", e);
                }
            }
        };
    }

    /**
     * 检查是否需要初始化数据库
     * 通过检查关键表是否存在来判断
     */
    private boolean shouldInitializeDatabase() {
        if (forceInit) {
            log.info("强制初始化模式已启用，将重新初始化数据库");
            return true;
        }

        try {
            // 检查关键表是否存在
            String[] requiredTables = {
                "users",
                "user_preferences",
                "notification_settings",
                "user_levels",
                "user_profiles"
            };

            for (String tableName : requiredTables) {
                if (!tableExists(tableName)) {
                    log.info("检测到表 '{}' 不存在，需要初始化数据库", tableName);
                    return true;
                }
            }

            // 检查是否有用户数据
            Integer userCount = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM users", Integer.class);

            if (userCount == null || userCount == 0) {
                log.info("检测到用户表为空，需要初始化示例数据");
                return true;
            }

            return false;
        } catch (Exception e) {
            log.warn("检查数据库状态时发生错误: {}, 将执行初始化", e.getMessage());
            return true;
        }
    }

    /**
     * 检查指定表是否存在
     */
    private boolean tableExists(String tableName) {
        try (Connection connection = dataSource.getConnection()) {
            DatabaseMetaData metaData = connection.getMetaData();
            try (ResultSet rs = metaData.getTables(null, null, tableName, new String[]{"TABLE"})) {
                return rs.next();
            }
        } catch (Exception e) {
            log.warn("检查表 '{}' 是否存在时发生错误: {}", tableName, e.getMessage());
            return false;
        }
    }

    /**
     * 执行数据库初始化
     */
    @Transactional
    private void initializeDatabase() {
        try {
            log.info("开始执行数据库初始化脚本: {}", initScriptPath);

            ClassPathResource resource = new ClassPathResource(initScriptPath);
            if (!resource.exists()) {
                throw new RuntimeException("初始化脚本文件不存在: " + initScriptPath);
            }

            ResourceDatabasePopulator populator = new ResourceDatabasePopulator();
            populator.addScript(resource);
            populator.setSeparator(";");
            populator.setCommentPrefix("--");
            populator.setContinueOnError(false);

            // 执行初始化脚本
            populator.execute(dataSource);

            log.info("数据库初始化脚本执行成功");

            // 验证初始化结果
            validateInitialization();

        } catch (Exception e) {
            log.error("执行数据库初始化脚本时发生错误", e);
            throw new RuntimeException("数据库初始化失败", e);
        }
    }

    /**
     * 验证数据库初始化结果
     */
    private void validateInitialization() {
        try {
            // 检查表数量
            Integer tableCount = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE()",
                Integer.class);
            log.info("数据库中共有 {} 个表", tableCount);

            // 检查用户数量
            Integer userCount = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM users", Integer.class);
            log.info("用户表中共有 {} 个用户", userCount);

            // 检查示例数据
            if (userCount != null && userCount > 0) {
                String adminUser = jdbcTemplate.queryForObject(
                    "SELECT username FROM users WHERE username = 'admin'",
                    String.class);
                log.info("示例管理员用户已创建: {}", adminUser);
            }

        } catch (Exception e) {
            log.warn("验证数据库初始化结果时发生错误: {}", e.getMessage());
        }
    }

    /**
     * 记录数据库信息
     */
    private void logDatabaseInfo() {
        try {
            Integer userCount = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM users", Integer.class);
            Integer activeUserCount = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM users WHERE status = 'ACTIVE'", Integer.class);

            log.info("数据库状态 - 总用户数: {}, 活跃用户数: {}", userCount, activeUserCount);

        } catch (Exception e) {
            log.warn("获取数据库信息时发生错误: {}", e.getMessage());
        }
    }

    /**
     * 手动重新初始化数据库的方法
     * 可以通过管理接口调用
     */
    public void forceReinitializeDatabase() {
        log.warn("手动触发数据库强制重新初始化");
        try {
            // 先清理现有数据（谨慎操作）
            if (tableExists("users")) {
                log.warn("正在清理现有数据...");
                // 这里可以添加清理逻辑，但要非常谨慎
            }

            initializeDatabase();
            log.info("手动重新初始化数据库完成");

        } catch (Exception e) {
            log.error("手动重新初始化数据库失败", e);
            throw new RuntimeException("手动重新初始化数据库失败", e);
        }
    }

    /**
     * 获取数据库初始化状态
     */
    public DatabaseInitStatus getDatabaseInitStatus() {
        try {
            boolean isInitialized = !shouldInitializeDatabase();
            Integer userCount = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM users", Integer.class);
            Integer tableCount = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE()",
                Integer.class);

            return DatabaseInitStatus.builder()
                .initialized(isInitialized)
                .userCount(userCount != null ? userCount : 0)
                .tableCount(tableCount != null ? tableCount : 0)
                .initEnabled(databaseInitEnabled)
                .build();

        } catch (Exception e) {
            log.error("获取数据库初始化状态时发生错误", e);
            return DatabaseInitStatus.builder()
                .initialized(false)
                .userCount(0)
                .tableCount(0)
                .initEnabled(databaseInitEnabled)
                .error(e.getMessage())
                .build();
        }
    }

    /**
     * 数据库初始化状态数据类
     */
    public static class DatabaseInitStatus {
        private boolean initialized;
        private int userCount;
        private int tableCount;
        private boolean initEnabled;
        private String error;

        // 构造函数
        private DatabaseInitStatus(Builder builder) {
            this.initialized = builder.initialized;
            this.userCount = builder.userCount;
            this.tableCount = builder.tableCount;
            this.initEnabled = builder.initEnabled;
            this.error = builder.error;
        }

        // Getter方法
        public boolean isInitialized() { return initialized; }
        public int getUserCount() { return userCount; }
        public int getTableCount() { return tableCount; }
        public boolean isInitEnabled() { return initEnabled; }
        public String getError() { return error; }

        // Builder模式
        public static Builder builder() {
            return new Builder();
        }

        public static class Builder {
            private boolean initialized;
            private int userCount;
            private int tableCount;
            private boolean initEnabled;
            private String error;

            public Builder initialized(boolean initialized) {
                this.initialized = initialized;
                return this;
            }

            public Builder userCount(int userCount) {
                this.userCount = userCount;
                return this;
            }

            public Builder tableCount(int tableCount) {
                this.tableCount = tableCount;
                return this;
            }

            public Builder initEnabled(boolean initEnabled) {
                this.initEnabled = initEnabled;
                return this;
            }

            public Builder error(String error) {
                this.error = error;
                return this;
            }

            public DatabaseInitStatus build() {
                return new DatabaseInitStatus(this);
            }
        }
    }
}
