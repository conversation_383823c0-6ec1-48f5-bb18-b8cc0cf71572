@use 'sass:color';
@use '@/styles/variables' as v;
@use '@/styles/mixins' as m;

.about-page {
    padding: v.$spacing-lg 0;
}

.about-content {
    max-width: 800px;
    margin: 0 auto;
}

.about-section {
    margin-bottom: v.$spacing-xl;

    p {
        margin-bottom: v.$spacing-md;
        line-height: 1.6;
    }
}

.section-title {
    font-size: 1.5rem;
    color: v.$color-primary;
    margin-bottom: v.$spacing-md;
    padding-bottom: v.$spacing-sm;
    border-bottom: 1px solid color.adjust(black, $alpha: -0.9);
}

.mission-list,
.tech-list {
    margin-bottom: v.$spacing-md;
    padding-left: v.$spacing-lg;

    li {
        margin-bottom: v.$spacing-sm;
        line-height: 1.5;
    }
}

.contact-info {
    background-color: #f8f9fa;
    padding: v.$spacing-md;
    border-radius: v.$shape-corner-medium;
    margin-bottom: v.$spacing-md;

    p {
        margin-bottom: v.$spacing-xs;
    }
}

@include m.respond-to(sm) {
    .about-content {
        padding: 0 v.$spacing-md;
    }
}
