<template>
  <div class="debate-arena">
    <div class="arena-container">
      <!-- 辩论信息头部 -->
      <div class="arena-header">
        <div class="arena-title-container">
          <h1 class="arena-title">{{ debate.title }}</h1>
          <div class="arena-meta">
            <span class="arena-category">{{ debate.category }}</span>
            <span class="arena-status" :class="`status--${debate.status}`">
              {{ getStatusText(debate.status) }}
            </span>
          </div>
        </div>

        <div class="arena-actions">
          <button v-if="isJudge && debate.status === 'active'" class="btn btn--outline" @click="stopDebate"
            :disabled="loading.action">
            {{ loading.action ? '处理中...' : '结束辩论' }}
          </button>

          <button v-if="isSpectator" class="btn btn--outline" @click="toggleFavorite" :disabled="loading.favorite">
            <span v-if="isFavorite">已收藏</span>
            <span v-else>收藏</span>
          </button>

          <button class="btn btn--outline" @click="showRules = true">
            规则
          </button>
        </div>
      </div>

      <!-- 3D场景渲染区域 -->
      <div class="arena-stage">
        <div class="arena-debate-view">
          <div class="canvas-container" ref="canvasContainer"></div>

          <div class="debate-controls" v-if="debate.status === 'active'">
            <div class="debate-timer" v-if="currentTurn && currentTurn.timeRemaining > 0">
              <span class="timer-label">剩余时间</span>
              <span class="timer-value">{{ formatTime(currentTurn.timeRemaining) }}</span>
            </div>

            <div v-if="isMyTurn && debate.status === 'active'" class="my-turn-controls">
              <button class="btn btn--primary" @click="submitSpeech" :disabled="loading.submit || !canSubmit">
                {{ loading.submit ? '提交中...' : '提交发言' }}
              </button>
              <button class="btn btn--outline" @click="skipTurn" :disabled="loading.skip">
                {{ loading.skip ? '跳过中...' : '跳过' }}
              </button>
            </div>
          </div>
        </div>

        <div class="debate-info-sidebar">
          <div class="debate-participants">
            <div class="side-label supporter">正方</div>
            <div class="participants-list">
              <div v-for="participant in supporterParticipants" :key="participant.id" class="participant-item"
                :class="{ 'current-speaker': currentSpeaker?.id === participant.id }">
                <div class="participant-avatar">
                  <img :src="participant.avatar || 'http://placehold.co/40'" :alt="participant.name">
                  <div v-if="currentSpeaker?.id === participant.id" class="speaking-indicator"></div>
                </div>
                <div class="participant-info">
                  <div class="participant-name">{{ participant.name }}</div>
                  <div class="participant-type">{{ participant.isAI ? 'AI' : '人类' }}</div>
                </div>
              </div>
            </div>

            <div class="side-label opposer">反方</div>
            <div class="participants-list">
              <div v-for="participant in opposerParticipants" :key="participant.id" class="participant-item"
                :class="{ 'current-speaker': currentSpeaker?.id === participant.id }">
                <div class="participant-avatar">
                  <img :src="participant.avatar || 'http://placehold.co/40'" :alt="participant.name">
                  <div v-if="currentSpeaker?.id === participant.id" class="speaking-indicator"></div>
                </div>
                <div class="participant-info">
                  <div class="participant-name">{{ participant.name }}</div>
                  <div class="participant-type">{{ participant.isAI ? 'AI' : '人类' }}</div>
                </div>
              </div>
            </div>

            <div class="side-label judge">裁判</div>
            <div class="participants-list">
              <div v-for="participant in judgeParticipants" :key="participant.id" class="participant-item">
                <div class="participant-avatar">
                  <img :src="participant.avatar || 'http://placehold.co/40'" :alt="participant.name">
                </div>
                <div class="participant-info">
                  <div class="participant-name">{{ participant.name }}</div>
                  <div class="participant-type">{{ participant.isAI ? 'AI' : '人类' }}</div>
                </div>
              </div>
            </div>
          </div>

          <div class="debate-stats">
            <div class="stat-item">
              <span class="stat-value">{{ debate.rounds }}</span>
              <span class="stat-label">回合</span>
            </div>
            <div class="stat-item">
              <span class="stat-value">{{ debate.speeches }}</span>
              <span class="stat-label">发言</span>
            </div>
            <div class="stat-item">
              <span class="stat-value">{{ formatTime(debate.duration) }}</span>
              <span class="stat-label">持续时间</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 辩论内容区 -->
      <div class="arena-content">
        <div class="speech-editor" v-if="isParticipant && debate.status === 'active'">
          <div class="editor-header">
            <h3 class="editor-title">{{ isMyTurn ? '现在是您的发言回合' : '准备发言' }}</h3>
            <div class="editor-role">
              您的角色：<span :class="`role role--${userRole}`">{{ getRoleText(userRole) }}</span>
            </div>
          </div>

          <div class="editor-container">
            <textarea class="speech-textarea" v-model="speechContent" placeholder="输入您的辩论发言内容..."
              :disabled="!isParticipant || debate.status !== 'active'" ref="speechTextarea"></textarea>

            <div class="editor-controls">
              <div class="word-count">
                {{ speechContent.length > 0 ? speechContent.length : 0 }} / {{ maxSpeechLength }} 字
              </div>

              <div class="editor-buttons" v-if="!isMyTurn">
                <button class="btn btn--outline" @click="clearSpeech">
                  清空
                </button>
                <button class="btn btn--outline" @click="saveDraft" :disabled="!speechContent.trim()">
                  保存草稿
                </button>
              </div>
            </div>
          </div>
        </div>

        <div class="speech-history">
          <h3 class="history-title">辩论记录</h3>

          <div class="history-container" ref="historyContainer">
            <div v-if="loading.history" class="history-loading">
              <div class="spinner"></div>
              <p>加载辩论记录中...</p>
            </div>

            <template v-else>
              <div v-for="(speech, index) in speechHistory" :key="speech.id" class="speech-item" :class="[
                speech.side === 'supporter' ? 'supporter' : 'opposer',
                { 'system-message': speech.isSystem }
              ]">
                <div v-if="speech.isSystem" class="system-message-content">
                  {{ speech.content }}
                </div>
                <template v-else>
                  <div class="speech-header">
                    <div class="speech-author">
                      <div class="author-avatar">
                        <img :src="speech.author.avatar || 'http://placehold.co/30'" :alt="speech.author.name">
                      </div>
                      <div class="author-info">
                        <div class="author-name">{{ speech.author.name }}</div>
                        <div class="author-role">
                          <span class="role" :class="`role--${speech.side}`">{{ getRoleText(speech.side) }}</span>
                          <span class="participant-type">{{ speech.author.isAI ? 'AI' : '人类' }}</span>
                        </div>
                      </div>
                    </div>
                    <div class="speech-meta">
                      <span class="speech-time">{{ formatDateTime(speech.timestamp) }}</span>
                      <span class="speech-round" v-if="speech.round">回合 {{ speech.round }}</span>
                    </div>
                  </div>

                  <div class="speech-content" v-html="formatSpeechContent(speech.content)"></div>

                  <div class="speech-actions">
                    <button class="action-btn" :class="{ active: speech.userReaction === 'like' }"
                      @click="reactToSpeech(speech.id, 'like')">
                      👍 {{ speech.likes || 0 }}
                    </button>
                    <button class="action-btn" :class="{ active: speech.userReaction === 'dislike' }"
                      @click="reactToSpeech(speech.id, 'dislike')">
                      👎 {{ speech.dislikes || 0 }}
                    </button>
                    <button class="action-btn" @click="copySpeech(speech.content)">
                      📋 引用
                    </button>
                  </div>
                </template>
              </div>

              <div v-if="speechHistory.length === 0" class="empty-history">
                <p>辩论尚未开始，等待第一位参与者发言</p>
              </div>

              <div v-if="isLoadingMore" class="loading-more">
                <div class="spinner-small"></div>
                <span>加载更多...</span>
              </div>

              <div v-if="hasMoreHistory && !isLoadingMore" class="load-more">
                <button class="btn btn--text" @click="loadMoreHistory">
                  加载更多历史记录
                </button>
              </div>
            </template>
          </div>
        </div>
      </div>
    </div>

    <!-- 辩论规则模态框 -->
    <div v-if="showRules" class="modal-overlay" @click.self="showRules = false">
      <div class="modal debate-rules-modal">
        <div class="modal__header">
          <h2 class="modal__title">辩论规则</h2>
          <button class="modal__close" @click="showRules = false">×</button>
        </div>

        <div class="modal__body">
          <div class="rules-content">
            <h3>基本规则</h3>
            <ul>
              <li>辩论分为正方和反方两个阵营，每方由1-3名辩手组成</li>
              <li>辩论采用回合制，按照预设顺序轮流发言</li>
              <li>每位辩手每回合的发言时间限制为{{ debate.turnDuration || 3 }}分钟</li>
              <li>每位辩手的发言字数不应超过{{ maxSpeechLength }}字</li>
              <li>发言需遵循文明礼貌原则，不得人身攻击或使用不适当的语言</li>
            </ul>

            <h3>辩论流程</h3>
            <ol>
              <li>开场陈词：各方先介绍自己的立场和主要论点</li>
              <li>自由辩论：双方就辩题展开辩论，轮流进行</li>
              <li>总结陈词：各方总结自己的观点并强化论点</li>
              <li>裁判评判：由裁判根据双方表现进行评判，得出结果</li>
            </ol>

            <h3>评判标准</h3>
            <ul>
              <li>论点清晰：观点明确，论点有力</li>
              <li>逻辑严密：推理严谨，论证充分</li>
              <li>证据充分：引用可靠的事实、数据或权威观点</li>
              <li>表达能力：语言流畅，有条理，有说服力</li>
              <li>反驳能力：能够有效回应对方的质疑和挑战</li>
            </ul>
          </div>
        </div>

        <div class="modal__footer">
          <button class="btn btn--primary" @click="showRules = false">
            我已了解
          </button>
        </div>
      </div>
    </div>

    <!-- 辩论结果模态框 -->
    <div v-if="showResult" class="modal-overlay">
      <div class="modal debate-result-modal">
        <div class="modal__header">
          <h2 class="modal__title">辩论结果</h2>
        </div>

        <div class="modal__body">
          <div class="result-content">
            <div class="result-header">
              <h3 class="result-title">{{ debate.title }}</h3>
              <p class="result-category">{{ debate.category }}</p>
            </div>

            <div class="result-status" :class="`result-${debate.result}`">
              <span v-if="debate.result === 'supporter'">正方获胜</span>
              <span v-else-if="debate.result === 'opposer'">反方获胜</span>
              <span v-else-if="debate.result === 'draw'">平局</span>
              <span v-else>待定</span>
            </div>

            <div class="result-summary">
              <h4>裁判评语</h4>
              <p>{{ debate.judgementSummary }}</p>
            </div>

            <div class="result-teams">
              <div class="result-team supporter">
                <h4>正方</h4>
                <div class="team-score" :class="{ winner: debate.result === 'supporter' }">
                  {{ debate.supporterScore || 0 }} 分
                </div>
                <div class="team-members">
                  <div v-for="participant in supporterParticipants" :key="participant.id" class="team-member">
                    <img :src="participant.avatar || 'http://placehold.co/30'" :alt="participant.name">
                    <span>{{ participant.name }}</span>
                  </div>
                </div>
              </div>

              <div class="result-versus">VS</div>

              <div class="result-team opposer">
                <h4>反方</h4>
                <div class="team-score" :class="{ winner: debate.result === 'opposer' }">
                  {{ debate.opposerScore || 0 }} 分
                </div>
                <div class="team-members">
                  <div v-for="participant in opposerParticipants" :key="participant.id" class="team-member">
                    <img :src="participant.avatar || 'http://placehold.co/30'" :alt="participant.name">
                    <span>{{ participant.name }}</span>
                  </div>
                </div>
              </div>
            </div>

            <div class="result-stats">
              <div class="stat-item">
                <span class="stat-value">{{ debate.rounds }}</span>
                <span class="stat-label">回合</span>
              </div>
              <div class="stat-item">
                <span class="stat-value">{{ debate.speeches }}</span>
                <span class="stat-label">发言</span>
              </div>
              <div class="stat-item">
                <span class="stat-value">{{ formatTime(debate.duration) }}</span>
                <span class="stat-label">持续时间</span>
              </div>
            </div>
          </div>
        </div>

        <div class="modal__footer">
          <button class="btn btn--outline" @click="goToDebateHall">
            返回大厅
          </button>
          <button class="btn btn--primary" @click="showResult = false">
            查看辩论记录
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script src="./js/DebateArena.js"></script>

<style lang="scss">
@use './scss/DebateArena.scss';
</style>
