package com.debate_ournament.ai.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.debate_ournament.ai.entity.AiConfig;
import com.debate_ournament.ai.entity.AiProvider;

/**
 * AI配置数据访问接口
 */
@Repository
public interface AiConfigRepository extends JpaRepository<AiConfig, Long> {

    /**
     * 根据提供商查找配置
     */
    List<AiConfig> findByProviderAndEnabledTrue(AiProvider provider);

    /**
     * 根据提供商查找配置（分页）
     */
    Page<AiConfig> findByProviderAndEnabledTrue(AiProvider provider, Pageable pageable);

    /**
     * 查找所有启用的配置
     */
    List<AiConfig> findByEnabledTrueOrderByProviderAscConfigNameAsc();

    /**
     * 查找所有启用的配置（分页）
     */
    Page<AiConfig> findByEnabledTrueOrderByProviderAscConfigNameAsc(Pageable pageable);

    /**
     * 根据提供商和配置名称查找
     */
    Optional<AiConfig> findByProviderAndConfigNameAndEnabledTrue(AiProvider provider, String configName);

    /**
     * 查找默认配置
     */
    Optional<AiConfig> findByIsDefaultTrueAndEnabledTrue();

    /**
     * 根据创建者查找配置
     */
    List<AiConfig> findByCreatedByAndEnabledTrueOrderByCreatedAtDesc(Long createdBy);

    /**
     * 检查是否存在默认配置
     */
    boolean existsByIsDefaultTrueAndEnabledTrue();

    /**
     * 统计启用的配置数量
     */
    @Query("SELECT COUNT(ac) FROM AiConfig ac WHERE ac.enabled = true")
    long countEnabledConfigs();

    /**
     * 按提供商统计配置数量
     */
    @Query("SELECT ac.provider, COUNT(ac) FROM AiConfig ac WHERE ac.enabled = true GROUP BY ac.provider")
    List<Object[]> countConfigsByProvider();

    /**
     * 查找特定提供商的默认配置
     */
    Optional<AiConfig> findByProviderAndIsDefaultTrueAndEnabledTrue(AiProvider provider);

    /**
     * 更新默认配置状态
     */
    @Query("UPDATE AiConfig ac SET ac.isDefault = false WHERE ac.provider = :provider AND ac.enabled = true")
    void clearDefaultForProvider(@Param("provider") AiProvider provider);
}
