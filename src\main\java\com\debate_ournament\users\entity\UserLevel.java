package com.debate_ournament.users.entity;

import java.time.LocalDateTime;

import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;

/**
 * 用户等级实体类
 * 对应数据库表：user_levels
 */
@Entity
@Table(name = "user_levels")
@EntityListeners(AuditingEntityListener.class)
public class UserLevel {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false, columnDefinition = "bigint COMMENT '用户等级主键ID'")
    private Long id;

    @OneToOne
    @JoinColumn(name = "user_id", nullable = false, unique = true)
    private User user;

    @NotNull(message = "用户等级不能为空")
    @Min(value = 1, message = "用户等级不能小于1")
    @Column(name = "level", nullable = false,
            columnDefinition = "int DEFAULT 1 COMMENT '用户当前等级'")
    private Integer level = 1;

    @NotNull(message = "经验值不能为空")
    @Min(value = 0, message = "经验值不能小于0")
    @Column(name = "experience", nullable = false,
            columnDefinition = "bigint DEFAULT 0 COMMENT '用户当前经验值'")
    private Long experience = 0L;

    @NotNull(message = "升级所需经验值不能为空")
    @Min(value = 0, message = "升级所需经验值不能小于0")
    @Column(name = "experience_to_next_level", nullable = false,
            columnDefinition = "bigint DEFAULT 100 COMMENT '升级到下一等级所需的总经验值'")
    private Long experienceToNextLevel = 100L;

    @NotNull(message = "参与辩论次数不能为空")
    @Min(value = 0, message = "参与辩论次数不能小于0")
    @Column(name = "total_debates", nullable = false,
            columnDefinition = "int DEFAULT 0 COMMENT '参与辩论总次数'")
    private Integer totalDebates = 0;

    @NotNull(message = "获胜次数不能为空")
    @Min(value = 0, message = "获胜次数不能小于0")
    @Column(name = "total_wins", nullable = false,
            columnDefinition = "int DEFAULT 0 COMMENT '辩论获胜总次数'")
    private Integer totalWins = 0;

    @Column(name = "win_rate", nullable = false,
            columnDefinition = "decimal(5,2) DEFAULT 0.00 COMMENT '胜率（百分比）'")
    private Double winRate = 0.0;

    @Column(name = "highest_streak", nullable = false,
            columnDefinition = "int DEFAULT 0 COMMENT '最高连胜记录'")
    private Integer highestStreak = 0;

    @Column(name = "current_streak", nullable = false,
            columnDefinition = "int DEFAULT 0 COMMENT '当前连胜次数'")
    private Integer currentStreak = 0;

    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false,
            columnDefinition = "datetime DEFAULT CURRENT_TIMESTAMP COMMENT '等级信息创建时间'")
    private LocalDateTime createdAt;

    @LastModifiedDate
    @Column(name = "updated_at", nullable = false,
            columnDefinition = "datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '等级信息最后更新时间'")
    private LocalDateTime updatedAt;

    // 构造函数
    public UserLevel() {}

    public UserLevel(User user) {
        this.user = user;
        this.level = 1;
        this.experience = 0L;
        this.experienceToNextLevel = 100L;
        this.totalDebates = 0;
        this.totalWins = 0;
        this.winRate = 0.0;
        this.highestStreak = 0;
        this.currentStreak = 0;
    }

    // Getter 和 Setter 方法
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public Long getExperience() {
        return experience;
    }

    public void setExperience(Long experience) {
        this.experience = experience;
    }

    public Long getExperienceToNextLevel() {
        return experienceToNextLevel;
    }

    public void setExperienceToNextLevel(Long experienceToNextLevel) {
        this.experienceToNextLevel = experienceToNextLevel;
    }

    public Integer getTotalDebates() {
        return totalDebates;
    }

    public void setTotalDebates(Integer totalDebates) {
        this.totalDebates = totalDebates;
    }

    public Integer getTotalWins() {
        return totalWins;
    }

    public void setTotalWins(Integer totalWins) {
        this.totalWins = totalWins;
    }

    public Double getWinRate() {
        return winRate;
    }

    public void setWinRate(Double winRate) {
        this.winRate = winRate;
    }

    public Integer getHighestStreak() {
        return highestStreak;
    }

    public void setHighestStreak(Integer highestStreak) {
        this.highestStreak = highestStreak;
    }

    public Integer getCurrentStreak() {
        return currentStreak;
    }

    public void setCurrentStreak(Integer currentStreak) {
        this.currentStreak = currentStreak;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    // 业务方法

    /**
     * 增加经验值并检查是否升级
     * @param exp 要增加的经验值
     * @return 是否升级了
     */
    public boolean addExperience(long exp) {
        this.experience += exp;
        return checkLevelUp();
    }

    /**
     * 记录辩论结果
     * @param isWin 是否获胜
     */
    public void recordDebateResult(boolean isWin) {
        this.totalDebates++;
        if (isWin) {
            this.totalWins++;
            this.currentStreak++;
            if (this.currentStreak > this.highestStreak) {
                this.highestStreak = this.currentStreak;
            }
        } else {
            this.currentStreak = 0;
        }
        updateWinRate();
    }

    /**
     * 更新胜率
     */
    private void updateWinRate() {
        if (this.totalDebates > 0) {
            this.winRate = (double) this.totalWins / this.totalDebates * 100;
        } else {
            this.winRate = 0.0;
        }
    }

    /**
     * 检查是否可以升级
     * @return 是否升级了
     */
    private boolean checkLevelUp() {
        boolean leveledUp = false;
        while (this.experience >= this.experienceToNextLevel) {
            this.level++;
            this.experience -= this.experienceToNextLevel;
            this.experienceToNextLevel = calculateNextLevelExperience(this.level);
            leveledUp = true;
        }
        return leveledUp;
    }

    /**
     * 计算指定等级升级所需的经验值
     * @param level 等级
     * @return 升级所需经验值
     */
    private long calculateNextLevelExperience(int level) {
        // 使用指数增长公式：基础经验值 * (等级 ^ 1.5)
        return (long) (100 * Math.pow(level, 1.5));
    }

    /**
     * 获取等级称号
     * @return 等级称号
     */
    public String getLevelTitle() {
        if (level >= 50) {
            return "辩论大师";
        } else if (level >= 40) {
            return "辩论专家";
        } else if (level >= 30) {
            return "资深辩手";
        } else if (level >= 20) {
            return "优秀辩手";
        } else if (level >= 10) {
            return "进阶辩手";
        } else if (level >= 5) {
            return "初级辩手";
        } else {
            return "新手辩手";
        }
    }

    @Override
    public String toString() {
        return "UserLevel{" +
                "id=" + id +
                ", level=" + level +
                ", experience=" + experience +
                ", experienceToNextLevel=" + experienceToNextLevel +
                ", totalDebates=" + totalDebates +
                ", totalWins=" + totalWins +
                ", winRate=" + winRate +
                ", currentStreak=" + currentStreak +
                ", highestStreak=" + highestStreak +
                '}';
    }
}
