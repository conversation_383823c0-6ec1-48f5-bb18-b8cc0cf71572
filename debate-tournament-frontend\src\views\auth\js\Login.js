import { ref, reactive, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useUserStore } from '@/store/user';
import { login } from '@/api/auth';
import { useEncryptionStore } from '@/store/encryption';

// 生成验证码
export const generateCaptcha = () => {
  // 生成4位随机数字
  const captcha = Math.floor(1000 + Math.random() * 9000).toString();

  // 使用在线验证码服务，不依赖后端API
  // 添加时间戳参数确保图片不被缓存
  const timestamp = new Date().getTime();
  return `https://dummyimage.com/120x40/e0e0e0/000000.png&text=${captcha}&_=${timestamp}`;
}

// 刷新验证码
export const refreshCaptcha = () => {
  return generateCaptcha();
}

// 验证表单
export const validateForm = async (formRef) => {
  if (!formRef) return false;

  try {
    await formRef.validate();
    return true;
  } catch (error) {
    return false;
  }
}

export default {
  name: 'Login',
  setup() {
    const router = useRouter()
    const userStore = useUserStore()
    const loginForm = ref(null)

    const formData = reactive({
      username: '',
      password: '',
      captcha: '',
      rememberMe: false
    })

    // Element Plus表单验证规则
    const rules = {
      username: [
        { required: true, message: '请输入用户名', trigger: 'blur' },
        { min: 3, max: 20, message: '用户名长度在3到20个字符之间', trigger: 'blur' }
      ],
      password: [
        { required: true, message: '请输入密码', trigger: 'blur' },
        { min: 6, message: '密码长度不能少于6个字符', trigger: 'blur' }
      ],
      captcha: [
        { required: true, message: '请输入验证码', trigger: 'blur' },
        { len: 4, message: '验证码必须是4位', trigger: 'blur' },
        {
          validator: (rule, value, callback) => {
            if (value && value !== currentCaptcha.value) {
              callback(new Error('验证码错误'));
              refreshCaptcha();
            } else {
              callback();
            }
          },
          trigger: 'blur'
        }
      ]
    }

    const errors = reactive({
      username: '',
      password: '',
      captcha: '',
      general: ''
    })

    const loading = ref(false)
    const showPassword = ref(false)
    // 后端API验证码
    const captchaUrl = ref('/api/captcha/image?' + Date.now())
    // 当前验证码的值 (模拟)
    const currentCaptcha = ref('1234')

    const handleLogin = async () => {
      if (!loginForm.value) return;

      await loginForm.value.validate(async (valid) => {
        if (!valid) return;

        loading.value = true;
        errors.general = '';

        try {
          const encryptionStore = useEncryptionStore();
          await encryptionStore.initialize();
          const encryptedRequest = encryptionStore.encryptRequest({
            username: formData.username,
            password: formData.password,
            captcha: formData.captcha
          });

          const response = await login(encryptedRequest);
          const decryptedResponse = encryptionStore.decryptResponse(response.data);

          userStore.setUser(decryptedResponse.user);
          userStore.setToken(decryptedResponse.token);

          if (formData.rememberMe) {
            localStorage.setItem('rememberMe', 'true');
            localStorage.setItem('username', formData.username);
          } else {
            localStorage.removeItem('rememberMe');
            localStorage.removeItem('username');
          }

          router.push('/debate-hall');
          loading.value = false;
        } catch (error) {
          if (error.response?.status === 401) {
            errors.general = '用户名或密码错误';
          } else if (error.response?.status === 400) {
            errors.captcha = '验证码错误';
            refreshCaptcha();
          } else {
            errors.general = error.response?.data?.message || '登录失败,请重试';
          }
          loading.value = false;
        }
      });
    }

    const togglePasswordVisibility = () => {
      showPassword.value = !showPassword.value
    }

    const goToRegister = () => {
      router.push('/register')
    }

    // 检查是否记住用户名
    const checkRememberMe = () => {
      const remembered = localStorage.getItem('rememberMe')
      if (remembered) {
        formData.username = localStorage.getItem('username') || ''
        formData.rememberMe = true
      }
    }

    // 组件挂载时初始化验证码
    onMounted(() => {
      captchaUrl.value = generateCaptcha();
      checkRememberMe();
    })

    return {
      formData,
      errors,
      loading,
      showPassword,
      captchaUrl,
      handleLogin,
      togglePasswordVisibility,
      refreshCaptcha,
      goToRegister,
      rules,
      loginForm
    }
  }
}
