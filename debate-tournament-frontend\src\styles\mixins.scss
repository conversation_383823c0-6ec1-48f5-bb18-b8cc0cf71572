// 混入
@use './variables' as v;
@use 'sass:map';

// 响应式断点混入
@mixin respond-to($breakpoint) {
  @if map.has-key(v.$breakpoints, $breakpoint) {
    @media (min-width: #{map.get(v.$breakpoints, $breakpoint)}) {
      @content;
    }
  }

  @else {
    @warn "未知的断点: #{$breakpoint}。";
  }
}

// 文字溢出处理
@mixin text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// 多行文本溢出
@mixin text-clamp($lines) {
  display: -webkit-box;
  -webkit-line-clamp: $lines;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

// flex 布局快捷方式
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

@mixin flex-column {
  display: flex;
  flex-direction: column;
}

// 页面过渡动画
@mixin page-transition {

  .fade-enter-active,
  .fade-leave-active {
    transition: opacity 0.3s ease;
  }

  .fade-enter-from,
  .fade-leave-to {
    opacity: 0;
  }
}

// 阴影效果
@mixin shadow-sm {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

@mixin shadow-md {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

@mixin shadow-lg {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

// 交互状态
@mixin interactive {
  transition: all 0.2s ease;

  &:hover {
    transform: translateY(-2px);
  }

  &:active {
    transform: translateY(0);
  }
}

// 3D 场景容器
@mixin scene-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;

  canvas {
    display: block;
    width: 100%;
    height: 100%;
  }
}

// 辩论方颜色主题
@mixin debate-side-theme($side) {
  @if $side =='supporter' {
    color: v.$debate-supporter-color;
    border-color: v.$debate-supporter-color;
    background-color: rgba(v.$debate-supporter-color, 0.05);
  }

  @else if $side =='opposer' {
    color: v.$debate-opposer-color;
    border-color: v.$debate-opposer-color;
    background-color: rgba(v.$debate-opposer-color, 0.05);
  }

  @else if $side =='judge' {
    color: v.$debate-neutral-color;
    border-color: v.$debate-neutral-color;
    background-color: rgba(v.$debate-neutral-color, 0.05);
  }
}

// 图片背景覆盖
@mixin bg-cover {
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
}

// 玻璃拟态效果
@mixin glassmorphism {
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

// 流体排版（在不同屏幕尺寸下自动调整字体大小）
@mixin fluid-type($min-vw, $max-vw, $min-font-size, $max-font-size) {
  $u1: unit($min-vw);
  $u2: unit($max-vw);
  $u3: unit($min-font-size);
  $u4: unit($max-font-size);

  @if $u1 ==$u2 and $u1 ==$u3 and $u1 ==$u4 {
    font-size: $min-font-size;

    @media screen and (min-width: $min-vw) {
      font-size: calc(#{$min-font-size} + #{strip-unit($max-font-size - $min-font-size)} * ((100vw - #{$min-vw}) / #{strip-unit($max-vw - $min-vw)}));
    }

    @media screen and (min-width: $max-vw) {
      font-size: $max-font-size;
    }
  }
}

// 动画混入
@mixin keyframes($name) {
  @keyframes #{$name} {
    @content;
  }
}

@mixin animation($animation) {
  animation: $animation;
}

// 辅助函数：去除单位
@function strip-unit($value) {
  @return $value / ($value * 0 + 1);
}
