package com.debate_ournament.users.service;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.debate_ournament.dto.auth.LoginRequest;
import com.debate_ournament.dto.auth.RegisterRequest;
import com.debate_ournament.users.entity.User;
import com.debate_ournament.util.JwtUtil;

/**
 * 认证业务服务类
 */
@Service
@Transactional
public class AuthService {

    private static final Logger logger = LoggerFactory.getLogger(AuthService.class);

    private final UserService userService;
    private final CaptchaService captchaService;
    private final JwtUtil jwtUtil;

    @Autowired
    public AuthService(UserService userService, CaptchaService captchaService, JwtUtil jwtUtil) {
        this.userService = userService;
        this.captchaService = captchaService;
        this.jwtUtil = jwtUtil;
    }

    /**
     * 用户登录
     */
    public Map<String, Object> login(LoginRequest request, String clientIp) {
        logger.info("用户登录请求: username={}, ip={}", request.getUsername(), clientIp);

        // 验证验证码
        if (!captchaService.verifyCaptcha(request.getSessionId(), request.getCaptcha())) {
            throw new IllegalArgumentException("验证码错误");
        }

        // 查找用户
        Optional<User> userOpt = userService.findByUsernameOrEmail(request.getUsername());
        if (userOpt.isEmpty()) {
            logger.warn("用户不存在: username={}", request.getUsername());
            throw new IllegalArgumentException("用户名或密码错误");
        }

        User user = userOpt.get();

        // 检查账号状态
        if (!userService.isUserAccountUsable(user)) {
            if (user.getStatus() == User.UserStatus.LOCKED) {
                if (user.getLockedUntil() != null && LocalDateTime.now().isBefore(user.getLockedUntil())) {
                    throw new IllegalStateException("账号已被锁定，请稍后再试");
                } else {
                    // 解锁过期的账号
                    userService.unlockUser(user);
                }
            } else if (user.getStatus() == User.UserStatus.BANNED) {
                throw new IllegalStateException("账号已被禁用，请联系管理员");
            } else if (user.getStatus() == User.UserStatus.INACTIVE) {
                throw new IllegalStateException("账号未激活，请先验证邮箱");
            }
        }

        // 验证密码
        if (!userService.verifyPassword(user, request.getPassword())) {
            logger.warn("密码错误: userId={}", user.getId());
            userService.handleLoginFailure(user);
            throw new IllegalArgumentException("用户名或密码错误");
        }

        // 登录成功处理
        userService.handleLoginSuccess(user, clientIp);

        // 生成JWT令牌
        String accessToken = jwtUtil.generateAccessToken(user.getUsername(), user.getId());
        String refreshToken = jwtUtil.generateRefreshToken(user.getUsername(), user.getId());

        // 构建响应
        Map<String, Object> response = new HashMap<>();
        response.put("accessToken", accessToken);
        response.put("refreshToken", refreshToken);
        response.put("tokenType", "Bearer");

        // 用户信息
        Map<String, Object> userInfo = new HashMap<>();
        userInfo.put("id", user.getId());
        userInfo.put("username", user.getUsername());
        userInfo.put("email", user.getEmail());
        userInfo.put("emailVerified", user.getEmailVerified());
        userInfo.put("status", user.getStatus());
        response.put("user", userInfo);

        logger.info("用户登录成功: userId={}", user.getId());
        return response;
    }

    /**
     * 用户注册
     */
    public Map<String, Object> register(RegisterRequest request, String clientIp) {
        logger.info("用户注册请求: username={}, email={}, ip={}",
                   request.getUsername(), request.getEmail(), clientIp);

        // 验证验证码
        if (!captchaService.verifyCaptcha(request.getSessionId(), request.getCaptcha())) {
            throw new IllegalArgumentException("验证码错误");
        }

        // 验证密码确认
        if (!request.getPassword().equals(request.getConfirmPassword())) {
            throw new IllegalArgumentException("两次输入的密码不一致");
        }

        // 创建用户
        User user = userService.createUser(request.getUsername(), request.getEmail(), request.getPassword());

        // 构建响应
        Map<String, Object> response = new HashMap<>();
        response.put("userId", user.getId());
        response.put("username", user.getUsername());
        response.put("email", user.getEmail());
        response.put("message", "注册成功，请验证邮箱后登录");

        logger.info("用户注册成功: userId={}", user.getId());
        return response;
    }

    /**
     * 刷新令牌
     */
    public Map<String, Object> refreshToken(String refreshToken) {
        logger.info("刷新令牌请求");

        // 从令牌中提取用户名
        String username;
        try {
            username = jwtUtil.extractUsername(refreshToken);
        } catch (Exception e) {
            throw new IllegalArgumentException("刷新令牌无效");
        }

        // 验证刷新令牌
        if (!jwtUtil.validateRefreshToken(refreshToken, username)) {
            throw new IllegalArgumentException("刷新令牌无效或已过期");
        }

        // 获取用户ID
        Long userId = jwtUtil.extractUserId(refreshToken);
        Optional<User> userOpt = userService.findById(userId);

        if (userOpt.isEmpty()) {
            throw new IllegalArgumentException("用户不存在");
        }

        User user = userOpt.get();

        // 检查用户状态
        if (!userService.isUserAccountUsable(user)) {
            throw new IllegalStateException("账号状态异常，请重新登录");
        }

        // 生成新的访问令牌
        String newAccessToken = jwtUtil.generateAccessToken(user.getUsername(), user.getId());

        // 构建响应
        Map<String, Object> response = new HashMap<>();
        response.put("accessToken", newAccessToken);
        response.put("tokenType", "Bearer");

        logger.info("令牌刷新成功: userId={}", userId);
        return response;
    }

    /**
     * 用户登出
     */
    public void logout(String accessToken) {
        logger.info("用户登出请求");

        // 将令牌加入黑名单（如果有实现的话）
        // jwtUtil.invalidateToken(accessToken);

        logger.info("用户登出成功");
    }

    /**
     * 验证令牌
     */
    @Transactional(readOnly = true)
    public Map<String, Object> validateToken(String accessToken) {
        // 从令牌中提取用户名
        String username;
        try {
            username = jwtUtil.extractUsername(accessToken);
        } catch (Exception e) {
            throw new IllegalArgumentException("访问令牌无效");
        }

        if (!jwtUtil.validateAccessToken(accessToken, username)) {
            throw new IllegalArgumentException("访问令牌无效或已过期");
        }

        Long userId = jwtUtil.extractUserId(accessToken);

        Optional<User> userOpt = userService.findById(userId);
        if (userOpt.isEmpty()) {
            throw new IllegalArgumentException("用户不存在");
        }

        User user = userOpt.get();
        if (!userService.isUserAccountUsable(user)) {
            throw new IllegalStateException("账号状态异常");
        }

        // 构建响应
        Map<String, Object> response = new HashMap<>();
        response.put("valid", true);
        response.put("userId", userId);
        response.put("username", username);
        response.put("expiresAt", jwtUtil.extractExpiration(accessToken));

        return response;
    }

    /**
     * 获取当前用户信息
     */
    @Transactional(readOnly = true)
    public Map<String, Object> getCurrentUser(String accessToken) {
        // 从令牌中提取用户名
        String username;
        try {
            username = jwtUtil.extractUsername(accessToken);
        } catch (Exception e) {
            throw new IllegalArgumentException("访问令牌无效");
        }

        if (!jwtUtil.validateAccessToken(accessToken, username)) {
            throw new IllegalArgumentException("访问令牌无效或已过期");
        }

        Long userId = jwtUtil.extractUserId(accessToken);
        Optional<User> userOpt = userService.findById(userId);

        if (userOpt.isEmpty()) {
            throw new IllegalArgumentException("用户不存在");
        }

        User user = userOpt.get();

        // 构建用户信息响应
        Map<String, Object> userInfo = new HashMap<>();
        userInfo.put("id", user.getId());
        userInfo.put("username", user.getUsername());
        userInfo.put("email", user.getEmail());
        userInfo.put("emailVerified", user.getEmailVerified());
        userInfo.put("status", user.getStatus());
        userInfo.put("createdAt", user.getCreatedAt());
        userInfo.put("lastLoginTime", user.getLastLoginTime());

        return userInfo;
    }

    /**
     * 修改密码
     */
    public void changePassword(String accessToken, String oldPassword, String newPassword) {
        logger.info("修改密码请求");

        // 从令牌中提取用户名
        String username;
        try {
            username = jwtUtil.extractUsername(accessToken);
        } catch (Exception e) {
            throw new IllegalArgumentException("访问令牌无效");
        }

        if (!jwtUtil.validateAccessToken(accessToken, username)) {
            throw new IllegalArgumentException("访问令牌无效或已过期");
        }

        Long userId = jwtUtil.extractUserId(accessToken);
        Optional<User> userOpt = userService.findById(userId);

        if (userOpt.isEmpty()) {
            throw new IllegalArgumentException("用户不存在");
        }

        User user = userOpt.get();

        // 验证旧密码
        if (!userService.verifyPassword(user, oldPassword)) {
            throw new IllegalArgumentException("原密码错误");
        }

        // 更新密码
        userService.updatePassword(user, newPassword);

        logger.info("密码修改成功: userId={}", userId);
    }

    /**
     * 检查用户名是否可用
     */
    @Transactional(readOnly = true)
    public boolean isUsernameAvailable(String username) {
        return !userService.existsByUsername(username);
    }

    /**
     * 检查邮箱是否可用
     */
    @Transactional(readOnly = true)
    public boolean isEmailAvailable(String email) {
        return !userService.existsByEmail(email);
    }
}
