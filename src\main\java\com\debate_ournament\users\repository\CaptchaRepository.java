package com.debate_ournament.users.repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.debate_ournament.users.entity.Captcha;

/**
 * 验证码数据访问接口
 */
@Repository
public interface CaptchaRepository extends JpaRepository<Captcha, String> {

    /**
     * 根据会话ID查找验证码
     */
    Optional<Captcha> findBySessionId(String sessionId);

    /**
     * 查找有效的验证码（未使用且未过期）
     */
    @Query("SELECT c FROM Captcha c WHERE c.sessionId = :sessionId AND c.used = false AND c.expiresAt > :currentTime")
    Optional<Captcha> findValidCaptcha(@Param("sessionId") String sessionId, @Param("currentTime") LocalDateTime currentTime);

    /**
     * 查找已过期的验证码
     */
    @Query("SELECT c FROM Captcha c WHERE c.expiresAt < :currentTime")
    List<Captcha> findExpiredCaptchas(@Param("currentTime") LocalDateTime currentTime);

    /**
     * 查找指定IP地址在时间范围内创建的验证码数量
     */
    @Query("SELECT COUNT(c) FROM Captcha c WHERE c.clientIp = :clientIp AND c.createdAt > :cutoffTime")
    long countByClientIpAndCreatedAtAfter(@Param("clientIp") String clientIp, @Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * 查找尝试次数过多的验证码
     */
    @Query("SELECT c FROM Captcha c WHERE c.attemptCount >= :maxAttempts")
    List<Captcha> findCaptchasWithExcessiveAttempts(@Param("maxAttempts") int maxAttempts);

    /**
     * 根据IP地址查找最近的验证码
     */
    @Query("SELECT c FROM Captcha c WHERE c.clientIp = :clientIp ORDER BY c.createdAt DESC")
    List<Captcha> findRecentCaptchasByIp(@Param("clientIp") String clientIp);

    /**
     * 删除过期的验证码
     */
    @Modifying
    @Query("DELETE FROM Captcha c WHERE c.expiresAt < :currentTime")
    int deleteExpiredCaptchas(@Param("currentTime") LocalDateTime currentTime);

    /**
     * 删除已使用的验证码
     */
    @Modifying
    @Query("DELETE FROM Captcha c WHERE c.used = true AND c.createdAt < :cutoffTime")
    int deleteUsedCaptchas(@Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * 删除指定时间之前创建的验证码
     */
    @Modifying
    @Query("DELETE FROM Captcha c WHERE c.createdAt < :cutoffTime")
    int deleteCaptchasCreatedBefore(@Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * 标记验证码为已使用
     */
    @Modifying
    @Query("UPDATE Captcha c SET c.used = true WHERE c.sessionId = :sessionId")
    int markCaptchaAsUsed(@Param("sessionId") String sessionId);

    /**
     * 增加验证码尝试次数
     */
    @Modifying
    @Query("UPDATE Captcha c SET c.attemptCount = c.attemptCount + 1 WHERE c.sessionId = :sessionId")
    int incrementAttemptCount(@Param("sessionId") String sessionId);

    /**
     * 检查会话ID是否存在
     */
    boolean existsBySessionId(String sessionId);

    /**
     * 统计在指定时间段内创建的验证码总数
     */
    @Query("SELECT COUNT(c) FROM Captcha c WHERE c.createdAt BETWEEN :startTime AND :endTime")
    long countCaptchasCreatedBetween(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 查找特定IP地址的验证码历史
     */
    @Query("SELECT c FROM Captcha c WHERE c.clientIp = :clientIp AND c.createdAt > :cutoffTime ORDER BY c.createdAt DESC")
    List<Captcha> findCaptchaHistoryByIp(@Param("clientIp") String clientIp, @Param("cutoffTime") LocalDateTime cutoffTime);
}
