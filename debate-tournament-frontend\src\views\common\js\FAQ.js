import { ref } from 'vue';
import { useRouter } from 'vue-router';

export default {
  name: 'FAQ',
  setup() {
    const router = useRouter();

    // FAQ分类
    const faqCategories = ref([
      {
        id: 1,
        title: '平台基础',
        faqs: [
          {
            id: 101,
            question: '什么是AI辩论赛平台？',
            answer: 'AI辩论赛平台是一个创新型在线平台，用户可以在此与AI进行辩论，也可以观看或参与其他用户与AI的辩论。我们的目标是通过辩论形式促进批判性思维和多角度思考能力的培养。'
          },
          {
            id: 102,
            question: 'AI辩论赛平台的主要功能有哪些？',
            answer: '主要功能包括：发起辩论、参与辩论、观看辩论、评论和投票、社区互动等。用户可以选择与AI对战，也可以组队参与人机对抗赛。'
          },
          {
            id: 103,
            question: '平台是免费使用的吗？',
            answer: '平台基础功能完全免费，包括参与辩论、观看辩论等。我们也提供一些高级功能，如个性化AI训练、专业赛事组织等，这些功能可能需要付费使用。'
          }
        ]
      },
      {
        id: 2,
        title: '账号与安全',
        faqs: [
          {
            id: 201,
            question: '如何注册和登录？',
            answer: '点击网站右上角的"注册"按钮，填写用户名、邮箱和密码即可完成注册。注册成功后，使用您的用户名和密码即可登录平台。'
          },
          {
            id: 202,
            question: '忘记密码怎么办？',
            answer: '在登录页面点击"忘记密码"，输入您注册时使用的邮箱，系统会向您发送密码重置链接。'
          },
          {
            id: 203,
            question: '我的账号信息安全吗？',
            answer: '我们采用行业标准的加密技术保护用户数据，严格遵守隐私政策，不会将您的个人信息分享给第三方。详细信息请参阅我们的隐私政策。'
          }
        ]
      },
      {
        id: 3,
        title: '辩论规则',
        faqs: [
          {
            id: 301,
            question: '如何开始一场辩论？',
            answer: '登录后，在辩论大厅点击"发起辩论"按钮，设置辩题、选择辩论形式（如一对一、团队赛等），然后邀请其他用户或选择AI作为对手。'
          },
          {
            id: 302,
            question: '辩论的基本流程是什么？',
            answer: '一般包括开场陈述、自由辩论环节和总结陈词。具体流程会根据辩论形式有所不同，系统会在每个环节给出明确提示。'
          },
          {
            id: 303,
            question: '如何评判辩论胜负？',
            answer: '辩论结束后，观众可以进行投票。同时，系统也会基于逻辑性、论据充分度、表达清晰度等维度给出客观评分，综合决定胜负。'
          }
        ]
      },
      {
        id: 4,
        title: '技术问题',
        faqs: [
          {
            id: 401,
            question: '平台支持哪些设备和浏览器？',
            answer: '我们的平台支持电脑、平板和手机等多种设备，推荐使用Chrome、Firefox、Safari和Edge等现代浏览器获得最佳体验。'
          },
          {
            id: 402,
            question: '辩论过程中遇到网络问题怎么办？',
            answer: '系统会自动保存辩论进度，如果网络中断，重新连接后可以继续未完成的辩论。对于重要辩论，建议使用稳定的网络环境。'
          },
          {
            id: 403,
            question: '为什么有时AI响应较慢？',
            answer: '这可能与网络连接、服务器负载或AI模型处理复杂问题所需时间有关。我们持续优化系统性能，减少响应延迟。'
          }
        ]
      }
    ]);

    // 展开状态管理
    const expandedFaqs = ref({});

    // 切换FAQ展开状态
    const toggleFaq = (faqId) => {
      expandedFaqs.value[faqId] = !expandedFaqs.value[faqId];
    };

    // 检查FAQ是否展开
    const isFaqExpanded = (faqId) => {
      return !!expandedFaqs.value[faqId];
    };

    // 导航到帮助中心
    const goToHelpCenter = () => {
      router.push('/help-center');
    };

    // 导航到联系我们
    const goToContact = () => {
      router.push('/contact');
    };

    return {
      faqCategories,
      toggleFaq,
      isFaqExpanded,
      goToHelpCenter,
      goToContact
    };
  }
};
