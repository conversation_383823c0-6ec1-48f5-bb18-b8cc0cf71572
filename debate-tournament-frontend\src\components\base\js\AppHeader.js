/**
 * 应用头部组件的业务逻辑
 * 包含用户菜单、移动端导航等功能
 */
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useRouter } from 'vue-router';
import { useUserStore } from '@/store/user';

export default {
  name: 'AppHeader',

  setup() {
    const router = useRouter();
    const userStore = useUserStore();

    // 计算属性
    const isAuthenticated = computed(() => userStore.isAuthenticated);
    const userName = computed(() => userStore.user?.username || '用户');
    const userAvatar = computed(() => userStore.user?.avatar || 'http://placehold.co/40');

    // 用户菜单状态
    const userMenuVisible = ref(false);
    const userDropdown = ref(null);

    // 移动端导航状态
    const mobileNavVisible = ref(false);

    /**
     * 切换用户菜单
     */
    function toggleUserMenu() {
      userMenuVisible.value = !userMenuVisible.value;
    }

    /**
     * 切换移动端导航
     */
    function toggleMobileMenu() {
      mobileNavVisible.value = !mobileNavVisible.value;
      // 如果打开移动端导航，则禁止滚动
      document.body.style.overflow = mobileNavVisible.value ? 'hidden' : '';
    }

    /**
     * 关闭移动导航
     */
    function closeMobileMenu() {
      mobileNavVisible.value = false;
      document.body.style.overflow = '';
    }

    /**
     * 处理登出
     */
    async function handleLogout() {
      try {
        await userStore.logout();
        userMenuVisible.value = false;
        mobileNavVisible.value = false;
        router.push('/');
      } catch (error) {
        console.error('登出失败:', error);
      }
    }

    /**
     * 点击外部关闭菜单
     */
    function handleClickOutside(event) {
      // 关闭用户下拉菜单
      if (userDropdown.value && !userDropdown.value.contains(event.target)) {
        userMenuVisible.value = false;
      }
    }

    // 监听滚动，自动收起移动导航
    function handleScroll() {
      if (mobileNavVisible.value) {
        closeMobileMenu();
      }
    }

    // 监听屏幕尺寸变化，在大屏时关闭移动导航
    function handleResize() {
      if (window.innerWidth >= 768 && mobileNavVisible.value) {
        closeMobileMenu();
      }
    }

    // 生命周期钩子
    onMounted(() => {
      document.addEventListener('click', handleClickOutside);
      window.addEventListener('scroll', handleScroll);
      window.addEventListener('resize', handleResize);
    });

    onUnmounted(() => {
      document.removeEventListener('click', handleClickOutside);
      window.removeEventListener('scroll', handleScroll);
      window.removeEventListener('resize', handleResize);
      // 确保在组件卸载时恢复滚动
      document.body.style.overflow = '';
    });

    return {
      isAuthenticated,
      userName,
      userAvatar,
      userMenuVisible,
      mobileNavVisible,
      userDropdown,
      toggleUserMenu,
      toggleMobileMenu,
      closeMobileMenu,
      handleLogout
    };
  }
};
