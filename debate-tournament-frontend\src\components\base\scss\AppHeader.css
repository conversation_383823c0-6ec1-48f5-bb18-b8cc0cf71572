.app-header {
  background-color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.app-header__content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 64px;
}

.app-header__logo .logo {
  display: flex;
  align-items: center;
  text-decoration: none;
}

.app-header__logo .logo__text {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--color-primary);
  margin-left: 0.5rem;
}

.app-header__nav {
  flex: 1;
  margin: 0 2rem;
}

.app-header__nav .nav {
  display: flex;
  justify-content: center;
}

.user-dropdown {
  position: relative;
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: var(--shape-corner-small);
}

.user-dropdown:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.user-dropdown__avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  overflow: hidden;
}

.user-dropdown__avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.user-dropdown__name {
  margin: 0 0.5rem;
  font-weight: 500;
}

.user-dropdown__arrow {
  border-style: solid;
  border-width: 0.15em 0.15em 0 0;
  content: '';
  display: inline-block;
  height: 0.45em;
  width: 0.45em;
  position: relative;
  transform: rotate(135deg);
  vertical-align: middle;
  margin-top: -0.2em;
}

.user-dropdown__menu {
  position: absolute;
  top: 100%;
  right: 0;
  background-color: white;
  border-radius: var(--shape-corner-small);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  min-width: 180px;
  margin-top: 0.5rem;
  z-index: 10;
}

.user-dropdown__item {
  display: block;
  padding: 0.75rem 1rem;
  text-decoration: none;
  color: var(--color-on-surface);
  transition: background-color 0.2s;
  text-align: left;
  width: 100%;
  border: none;
  background: none;
  cursor: pointer;
  font-size: inherit;
}

.user-dropdown__item:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.user-dropdown__item--logout {
  color: var(--color-error);
}

.user-dropdown__divider {
  height: 1px;
  background-color: rgba(0, 0, 0, 0.1);
  margin: 0.5rem 0;
}

.auth-buttons {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

@media (max-width: 768px) {
  .app-header__nav {
    display: none;
  }
} 