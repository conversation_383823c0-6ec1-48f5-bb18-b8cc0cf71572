package com.debate_ournament.ai.repository;

import java.time.LocalDateTime;
import java.util.List;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.debate_ournament.ai.entity.AiProvider;
import com.debate_ournament.ai.entity.MediaType;
import com.debate_ournament.ai.entity.MultiModalResponse;

/**
 * 多模态响应数据访问接口
 */
@Repository
public interface MultiModalResponseRepository extends JpaRepository<MultiModalResponse, Long> {

    /**
     * 根据会话ID查找响应
     */
    List<MultiModalResponse> findBySessionIdOrderByCreatedAtAsc(String sessionId);

    /**
     * 根据用户ID查找响应（分页）
     */
    Page<MultiModalResponse> findByUserIdOrderByCreatedAtDesc(Long userId, Pageable pageable);

    /**
     * 根据用户ID和会话ID查找响应
     */
    List<MultiModalResponse> findByUserIdAndSessionIdOrderByCreatedAtAsc(Long userId, String sessionId);

    /**
     * 根据媒体类型查找响应
     */
    List<MultiModalResponse> findByPrimaryMediaTypeOrderByCreatedAtDesc(MediaType mediaType);

    /**
     * 根据AI提供商查找响应
     */
    List<MultiModalResponse> findByAiProviderOrderByCreatedAtDesc(AiProvider aiProvider);

    /**
     * 根据状态查找响应
     */
    List<MultiModalResponse> findByStatusOrderByCreatedAtDesc(String status);

    /**
     * 查找用户的不同会话ID
     */
    @Query("SELECT DISTINCT r.sessionId FROM MultiModalResponse r WHERE r.userId = :userId ORDER BY MAX(r.createdAt) DESC")
    List<String> findDistinctSessionIdsByUserIdOrderByLatestResponse(@Param("userId") Long userId);

    /**
     * 统计用户的响应数量
     */
    long countByUserId(Long userId);

    /**
     * 统计用户某种媒体类型的响应数量
     */
    long countByUserIdAndPrimaryMediaType(Long userId, MediaType mediaType);

    /**
     * 计算用户的总处理时间
     */
    @Query("SELECT SUM(r.processingTime) FROM MultiModalResponse r WHERE r.userId = :userId")
    Long sumProcessingTimeByUserId(@Param("userId") Long userId);

    /**
     * 计算用户的总成本
     */
    @Query("SELECT SUM(r.cost) FROM MultiModalResponse r WHERE r.userId = :userId")
    Double sumCostByUserId(@Param("userId") Long userId);

    /**
     * 删除指定时间之前的响应
     */
    void deleteByCreatedAtBefore(LocalDateTime cutoffTime);

    /**
     * 根据聊天消息ID查找响应
     */
    List<MultiModalResponse> findByChatMessageId(Long chatMessageId);

    /**
     * 查找最近的响应
     */
    Page<MultiModalResponse> findByOrderByCreatedAtDesc(Pageable pageable);

    /**
     * 按媒体类型统计响应数量
     */
    @Query("SELECT r.primaryMediaType, COUNT(r) FROM MultiModalResponse r GROUP BY r.primaryMediaType")
    List<Object[]> countResponsesByMediaType();

    /**
     * 按AI提供商统计响应数量
     */
    @Query("SELECT r.aiProvider, COUNT(r) FROM MultiModalResponse r GROUP BY r.aiProvider")
    List<Object[]> countResponsesByProvider();

    /**
     * 查找失败的响应
     */
    List<MultiModalResponse> findByStatusAndCreatedAtAfterOrderByCreatedAtDesc(String status, LocalDateTime after);

    /**
     * 根据文件大小范围查找响应
     */
    @Query("SELECT r FROM MultiModalResponse r WHERE r.fileSize BETWEEN :minSize AND :maxSize ORDER BY r.createdAt DESC")
    List<MultiModalResponse> findByFileSizeBetween(@Param("minSize") Long minSize, @Param("maxSize") Long maxSize);
}
