import { ref, reactive, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useAiModelStore } from '@/store/aiModel';
import { useUserStore } from '@/store/user';
import axios from 'axios';

export default {
  name: 'AIModelSelection',

  setup() {
    const router = useRouter();
    const aiModelStore = useAiModelStore();
    const userStore = useUserStore();

    // 状态变量
    const loading = ref(false);
    const error = ref(null);
    const activeCategory = ref('all');
    const selectedModel = ref(null);
    const categories = ref([
      { id: 'all', name: '全部模型' },
      { id: 'general', name: '通用模型' },
      { id: 'debate', name: '辩论专家' },
      { id: 'academic', name: '学术专家' },
      { id: 'creative', name: '创意模型' },
      { id: 'custom', name: '自定义模型' }
    ]);

    // 模型数据
    const aiModels = ref([]);

    // 计算属性
    const filteredModels = computed(() => {
      if (activeCategory.value === 'all') {
        return aiModels.value;
      }

      return aiModels.value.filter(model => model.category === activeCategory.value);
    });

    // 方法
    // 获取所有AI模型
    const fetchModels = async () => {
      loading.value = true;
      error.value = null;

      try {
        // 先尝试从本地store获取模型
        if (aiModelStore.models && aiModelStore.models.length > 0) {
          aiModels.value = aiModelStore.models;
        } else {
          // 否则从API获取
          const response = await axios.get('/api/ai-models');
          aiModels.value = response.data;

          // 保存到store
          aiModelStore.setModels(response.data);
        }
      } catch (err) {
        console.error('获取AI模型失败:', err);
        error.value = '无法加载AI模型，请稍后再试';
      } finally {
        loading.value = false;
      }
    };

    // 获取用户自定义模型
    const fetchUserModels = async () => {
      if (!userStore.isLoggedIn) return;

      try {
        const response = await axios.get('/api/user/ai-models');

        // 将用户自定义模型添加到模型列表
        const userModels = response.data.map(model => ({
          ...model,
          category: 'custom'
        }));

        // 确保不重复添加
        const existingIds = new Set(aiModels.value.map(m => m.id));
        const newModels = userModels.filter(m => !existingIds.has(m.id));

        if (newModels.length > 0) {
          aiModels.value = [...aiModels.value, ...newModels];
          aiModelStore.setModels(aiModels.value);
        }
      } catch (err) {
        console.error('获取用户自定义AI模型失败:', err);
      }
    };

    // 选择模型以查看详情
    const selectModel = (model) => {
      selectedModel.value = model;
    };

    // 关闭模型详情模态框
    const closeModal = () => {
      selectedModel.value = null;
    };

    // 确认选择模型
    const confirmModelSelection = async () => {
      if (!selectedModel.value || !selectedModel.value.available) return;

      try {
        // 保存选择的模型到store
        aiModelStore.setSelectedModel(selectedModel.value);

        // 如果在创建辩论过程中选择模型，则返回上一页
        if (router.options.history.state.back) {
          router.back();
        } else {
          // 否则跳转到创建辩论页面
          router.push('/ai-debate/create');
        }
      } catch (err) {
        console.error('选择模型失败:', err);
      }
    };

    // 生命周期钩子
    onMounted(async () => {
      await fetchModels();
      await fetchUserModels();
    });

    return {
      loading,
      error,
      activeCategory,
      selectedModel,
      categories,
      aiModels,
      filteredModels,
      selectModel,
      closeModal,
      confirmModelSelection
    };
  }
};
