.debate-arena {
  min-height: calc(100vh - 64px);
  background-color: var(--color-surface);
  position: relative;
  overflow: hidden;
  
  &__container {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  
  &__header {
    padding: var(--spacing-md) 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    
    &-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
      
      @media (max-width: 768px) {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
      }
    }
    
    &-title {
      font-size: 1.5rem;
      font-weight: 600;
      margin: 0;
      color: var(--color-primary);
    }
    
    &-controls {
      display: flex;
      gap: var(--spacing-md);
      
      @media (max-width: 768px) {
        width: 100%;
        justify-content: space-between;
      }
    }
  }
  
  &__main {
    flex: 1;
    display: flex;
    flex-direction: column;
    
    @media (min-width: 992px) {
      flex-direction: row;
      height: calc(100vh - 150px);
    }
  }
  
  &__stage {
    flex: 2;
    display: flex;
    flex-direction: column;
    
    @media (min-width: 992px) {
      flex: 3;
      height: 100%;
      overflow: hidden;
    }
  }
  
  &__sidebar {
    flex: 1;
    background-color: rgba(0, 0, 0, 0.02);
    border-left: 1px solid rgba(0, 0, 0, 0.1);
    
    @media (max-width: 991px) {
      border-left: none;
      border-top: 1px solid rgba(0, 0, 0, 0.1);
    }
  }
}

/* 辩论舞台 */
.debate-stage {
  position: relative;
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  
  &__content {
    flex: 1;
    display: flex;
    flex-direction: column;
    
    @media (min-width: 768px) {
      flex-direction: row;
    }
  }
  
  &__host {
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100%;
    max-width: 200px;
    padding: var(--spacing-sm);
    text-align: center;
    z-index: 10;
    
    @media (min-width: 768px) {
      top: var(--spacing-md);
    }
  }
  
  &__side {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: var(--spacing-lg) var(--spacing-md);
    
    &--supporter {
      background-color: rgba(26, 86, 219, 0.05);
    }
    
    &--opposer {
      background-color: rgba(245, 158, 11, 0.05);
    }
  }
  
  &__center {
    position: relative;
    width: 100%;
    padding: var(--spacing-lg) 0;
    background-color: rgba(0, 0, 0, 0.02);
    display: flex;
    flex-direction: column;
    align-items: center;
    
    @media (min-width: 768px) {
      width: auto;
      padding: 0 var(--spacing-md);
    }
  }
  
  &__judges {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: var(--spacing-md);
    margin-top: var(--spacing-lg);
    
    @media (min-width: 768px) {
      flex-direction: column;
      margin-top: 0;
      position: absolute;
      bottom: var(--spacing-lg);
      left: 0;
      right: 0;
    }
  }
  
  &__speech-area {
    flex: 1;
    width: 100%;
    max-width: 800px;
    margin: 0 auto;
    padding: var(--spacing-lg);
    overflow-y: auto;
    
    @media (min-width: 768px) {
      padding: var(--spacing-xl);
    }
  }
  
  &__controls {
    padding: var(--spacing-md);
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: center;
    gap: var(--spacing-md);
    flex-wrap: wrap;
  }
}

/* AI角色卡片 */
.ai-character {
  width: 100%;
  max-width: 200px;
  background-color: white;
  border-radius: var(--shape-corner-medium);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: transform 0.3s, box-shadow 0.3s;
  
  &--active {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
  }
  
  &--speaking {
    animation: pulse 2s infinite;
  }
  
  &__avatar {
    width: 100%;
    height: 120px;
    background-color: rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
  
  &__info {
    padding: var(--spacing-sm);
    text-align: center;
  }
  
  &__name {
    font-weight: 600;
    margin: 0;
    font-size: 1rem;
  }
  
  &__role {
    font-size: 0.8rem;
    color: rgba(0, 0, 0, 0.6);
    margin-top: var(--spacing-xs);
  }
  
  &__indicator {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: #ccc;
    margin: var(--spacing-xs) auto 0;
    
    &--active {
      background-color: #4CAF50;
    }
    
    &--speaking {
      background-color: #F44336;
      animation: blink 1s infinite;
    }
  }
  
  &--host {
    .ai-character__avatar {
      background-color: rgba(103, 80, 164, 0.1);
    }
  }
  
  &--supporter {
    .ai-character__avatar {
      background-color: rgba(26, 86, 219, 0.1);
    }
  }
  
  &--opposer {
    .ai-character__avatar {
      background-color: rgba(245, 158, 11, 0.1);
    }
  }
  
  &--judge {
    .ai-character__avatar {
      background-color: rgba(107, 114, 128, 0.1);
    }
  }
}

/* 语音气泡 */
.speech-bubble {
  position: relative;
  background-color: white;
  border-radius: var(--shape-corner-medium);
  padding: var(--spacing-md);
  margin: var(--spacing-md) 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  max-width: 80%;
  
  &::before {
    content: '';
    position: absolute;
    width: 0;
    height: 0;
    border: 10px solid transparent;
  }
  
  &--supporter {
    align-self: flex-start;
    border-left: 4px solid var(--debate-supporter-color);
    
    &::before {
      left: -20px;
      top: 20px;
      border-right-color: white;
    }
  }
  
  &--opposer {
    align-self: flex-end;
    border-right: 4px solid var(--debate-opposer-color);
    
    &::before {
      right: -20px;
      top: 20px;
      border-left-color: white;
    }
  }
  
  &--host {
    align-self: center;
    border-top: 4px solid var(--color-primary);
    
    &::before {
      top: -20px;
      left: 50%;
      transform: translateX(-50%);
      border-bottom-color: white;
    }
  }
  
  &--judge {
    align-self: center;
    border-bottom: 4px solid var(--debate-neutral-color);
    
    &::before {
      bottom: -20px;
      left: 50%;
      transform: translateX(-50%);
      border-top-color: white;
    }
  }
  
  &__content {
    font-size: 1rem;
    line-height: 1.5;
  }
  
  &__meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: var(--spacing-sm);
    padding-top: var(--spacing-sm);
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    font-size: 0.8rem;
    color: rgba(0, 0, 0, 0.6);
  }
  
  &__actions {
    display: flex;
    gap: var(--spacing-xs);
  }
  
  &__action {
    background: none;
    border: none;
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: 50%;
    
    &:hover {
      background-color: rgba(0, 0, 0, 0.05);
    }
  }
}

/* 辩论信息侧边栏 */
.debate-sidebar {
  height: 100%;
  display: flex;
  flex-direction: column;
  
  &__tabs {
    display: flex;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  }
  
  &__tab {
    flex: 1;
    padding: var(--spacing-md);
    text-align: center;
    cursor: pointer;
    font-weight: 500;
    color: rgba(0, 0, 0, 0.6);
    border-bottom: 2px solid transparent;
    transition: all 0.2s;
    
    &:hover {
      color: var(--color-primary);
    }
    
    &--active {
      color: var(--color-primary);
      border-bottom-color: var(--color-primary);
    }
  }
  
  &__content {
    flex: 1;
    overflow-y: auto;
    padding: var(--spacing-md);
  }
}

/* 辩论历史记录 */
.debate-history {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  
  &__item {
    padding: var(--spacing-sm);
    border-radius: var(--shape-corner-small);
    background-color: white;
    border-left: 4px solid transparent;
    
    &--supporter {
      border-left-color: var(--debate-supporter-color);
    }
    
    &--opposer {
      border-left-color: var(--debate-opposer-color);
    }
    
    &--host {
      border-left-color: var(--color-primary);
    }
    
    &--judge {
      border-left-color: var(--debate-neutral-color);
    }
  }
  
  &__header {
    display: flex;
    justify-content: space-between;
    margin-bottom: var(--spacing-xs);
  }
  
  &__speaker {
    font-weight: 500;
  }
  
  &__time {
    font-size: 0.8rem;
    color: rgba(0, 0, 0, 0.6);
  }
  
  &__content {
    font-size: 0.9rem;
    line-height: 1.5;
  }
}

/* 语音控制面板 */
.voice-controls {
  padding: var(--spacing-md);
  background-color: white;
  border-radius: var(--shape-corner-medium);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: var(--spacing-md);
  
  &__title {
    font-weight: 600;
    margin-bottom: var(--spacing-md);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
  }
  
  &__row {
    display: flex;
    align-items: center;
    margin-bottom: var(--spacing-sm);
    
    label {
      flex: 1;
      font-size: 0.9rem;
    }
  }
  
  &__slider {
    flex: 2;
    
    input[type="range"] {
      width: 100%;
    }
  }
  
  &__toggle {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-md);
    
    .toggle-switch {
      position: relative;
      display: inline-block;
      width: 40px;
      height: 20px;
      
      input {
        opacity: 0;
        width: 0;
        height: 0;
        
        &:checked + .toggle-slider {
          background-color: var(--color-primary);
        }
        
        &:checked + .toggle-slider:before {
          transform: translateX(20px);
        }
      }
      
      .toggle-slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #ccc;
        transition: .4s;
        border-radius: 34px;
        
        &:before {
          position: absolute;
          content: "";
          height: 16px;
          width: 16px;
          left: 2px;
          bottom: 2px;
          background-color: white;
          transition: .4s;
          border-radius: 50%;
        }
      }
    }
  }
}

/* 辩论状态指示器 */
.debate-status-indicator {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: 20px;
  background-color: rgba(0, 0, 0, 0.05);
  font-size: 0.9rem;
  
  &__status {
    font-weight: 500;
  }
  
  &__timer {
    padding: 2px var(--spacing-xs);
    border-radius: 4px;
    background-color: rgba(0, 0, 0, 0.1);
    font-family: monospace;
  }
  
  &__round {
    margin-left: auto;
    font-size: 0.8rem;
    color: rgba(0, 0, 0, 0.6);
  }
  
  &--waiting {
    background-color: rgba(107, 114, 128, 0.1);
    color: var(--debate-neutral-color);
  }
  
  &--active {
    background-color: rgba(25, 118, 210, 0.1);
    color: #1976d2;
  }
  
  &--paused {
    background-color: rgba(245, 158, 11, 0.1);
    color: var(--debate-opposer-color);
  }
  
  &--completed {
    background-color: rgba(76, 175, 80, 0.1);
    color: var(--color-success);
  }
}

/* 动画 */
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes blink {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
} 