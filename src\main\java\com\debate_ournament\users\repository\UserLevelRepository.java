package com.debate_ournament.users.repository;

import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.debate_ournament.users.entity.UserLevel;

/**
 * 用户等级数据访问接口
 */
@Repository
public interface UserLevelRepository extends JpaRepository<UserLevel, Long> {

    /**
     * 根据用户ID查找用户等级信息
     * @param userId 用户ID
     * @return 用户等级信息
     */
    Optional<UserLevel> findByUserId(Long userId);

    /**
     * 根据用户名查找用户等级信息
     * @param username 用户名
     * @return 用户等级信息
     */
    @Query("SELECT ul FROM UserLevel ul JOIN ul.user u WHERE u.username = :username")
    Optional<UserLevel> findByUsername(@Param("username") String username);

    /**
     * 检查用户是否已有等级信息
     * @param userId 用户ID
     * @return 是否存在
     */
    boolean existsByUserId(Long userId);

    /**
     * 删除指定用户的等级信息
     * @param userId 用户ID
     */
    @Modifying
    @Query("DELETE FROM UserLevel ul WHERE ul.user.id = :userId")
    void deleteByUserId(@Param("userId") Long userId);

    /**
     * 获取用户等级排行榜（按等级和经验值排序）
     * @return 用户等级列表
     */
    @Query("SELECT ul FROM UserLevel ul JOIN FETCH ul.user u " +
           "WHERE u.status = 'ACTIVE' " +
           "ORDER BY ul.level DESC, ul.experience DESC")
    java.util.List<UserLevel> findTopUsersByLevel();

    /**
     * 获取指定等级范围内的用户数量
     * @param minLevel 最小等级
     * @param maxLevel 最大等级
     * @return 用户数量
     */
    @Query("SELECT COUNT(ul) FROM UserLevel ul JOIN ul.user u " +
           "WHERE ul.level BETWEEN :minLevel AND :maxLevel " +
           "AND u.status = 'ACTIVE'")
    long countUsersByLevelRange(@Param("minLevel") int minLevel, @Param("maxLevel") int maxLevel);

    /**
     * 获取胜率排行榜
     * @param minDebates 最少辩论次数
     * @return 用户等级列表
     */
    @Query("SELECT ul FROM UserLevel ul JOIN FETCH ul.user u " +
           "WHERE ul.totalDebates >= :minDebates AND u.status = 'ACTIVE' " +
           "ORDER BY ul.winRate DESC, ul.totalWins DESC")
    java.util.List<UserLevel> findTopUsersByWinRate(@Param("minDebates") int minDebates);

    /**
     * 获取连胜排行榜
     * @return 用户等级列表
     */
    @Query("SELECT ul FROM UserLevel ul JOIN FETCH ul.user u " +
           "WHERE ul.currentStreak > 0 AND u.status = 'ACTIVE' " +
           "ORDER BY ul.currentStreak DESC, ul.totalWins DESC")
    java.util.List<UserLevel> findTopUsersByCurrentStreak();

    /**
     * 获取指定用户在等级排行榜中的排名
     * @param userId 用户ID
     * @return 排名（从1开始）
     */
    @Query("SELECT COUNT(ul2) + 1 FROM UserLevel ul1, UserLevel ul2 " +
           "JOIN ul2.user u2 " +
           "WHERE ul1.user.id = :userId " +
           "AND (ul2.level > ul1.level OR (ul2.level = ul1.level AND ul2.experience > ul1.experience)) " +
           "AND u2.status = 'ACTIVE'")
    long findUserRankByLevel(@Param("userId") Long userId);

    /**
     * 批量增加用户经验值
     * @param userIds 用户ID列表
     * @param experience 要增加的经验值
     */
    @Modifying
    @Query("UPDATE UserLevel ul SET ul.experience = ul.experience + :experience " +
           "WHERE ul.user.id IN :userIds")
    void addExperienceToUsers(@Param("userIds") java.util.List<Long> userIds,
                             @Param("experience") long experience);

    /**
     * 获取平均等级
     * @return 平均等级
     */
    @Query("SELECT AVG(ul.level) FROM UserLevel ul JOIN ul.user u WHERE u.status = 'ACTIVE'")
    Double getAverageLevel();

    /**
     * 获取最高等级
     * @return 最高等级
     */
    @Query("SELECT MAX(ul.level) FROM UserLevel ul JOIN ul.user u WHERE u.status = 'ACTIVE'")
    Integer getMaxLevel();
}
