@use '@/styles/variables' as v;
@use '@/styles/mixins' as m;

.ai-model-selection {
  padding: v.$spacing-lg 0;
}

.page-title {
  font-size: 2rem;
  color: v.$color-primary;
  margin-bottom: v.$spacing-lg;
  text-align: center;
}

.model-categories {
  display: flex;
  justify-content: center;
  gap: v.$spacing-md;
  margin-bottom: v.$spacing-xl;
}

.category-tab {
  padding: v.$spacing-sm v.$spacing-lg;
  cursor: pointer;
  border-radius: v.$shape-corner-medium;
  transition: all 0.3s ease;
  position: relative;

  &:hover {
    background-color: rgba(v.$color-primary, 0.1);
  }

  &.active {
    color: v.$color-primary;
    font-weight: 500;

    &::after {
      content: '';
      position: absolute;
      bottom: -2px;
      left: 0;
      width: 100%;
      height: 2px;
      background-color: v.$color-primary;
    }
  }
}

.models-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: v.$spacing-lg;
  padding: v.$spacing-md;
}

.model-card {
  background-color: v.$color-surface;
  border-radius: v.$shape-corner-large;
  padding: v.$spacing-lg;
  box-shadow: v.$shadow-sm;
  transition: all 0.3s ease;
  cursor: pointer;

  &:hover {
    transform: translateY(-2px);
    box-shadow: v.$shadow-md;
  }
}

.model-card__header {
  margin-bottom: v.$spacing-md;
}

.model-card__title {
  font-size: 1.25rem;
  margin-bottom: v.$spacing-xs;
}

.model-card__provider {
  color: v.$color-text-secondary;
  font-size: 0.875rem;
}

.model-card__body {
  margin-bottom: v.$spacing-lg;
}

.model-card__description {
  color: v.$color-text-secondary;
  margin-bottom: v.$spacing-md;
  line-height: 1.5;
}

.model-card__stats {
  margin-top: v.$spacing-md;
}

.stat {
  margin-bottom: v.$spacing-sm;
}

.stat__label {
  display: block;
  margin-bottom: v.$spacing-xs;
  font-size: 0.875rem;
  color: v.$color-text-secondary;
}

.stat__bar {
  width: 100%;
  height: 6px;
  background-color: rgba(v.$color-primary, 0.1);
  border-radius: v.$shape-corner-small;
  overflow: hidden;
}

.stat__fill {
  height: 100%;
  background-color: v.$color-primary;
  transition: width 0.3s ease;
}

.model-card__footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: v.$spacing-md;
}

.model-card__type {
  font-size: 0.875rem;
  color: v.$color-text-secondary;
}

.model-card__status {
  font-size: 0.875rem;
  padding: v.$spacing-xs v.$spacing-sm;
  border-radius: v.$shape-corner-small;
}

.model-card__status.available {
  background-color: rgba(v.$color-success, 0.1);
  color: v.$color-success;
}

.model-card__status.unavailable {
  background-color: rgba(v.$color-error, 0.1);
  color: v.$color-error;
}

.add-custom-model {
  text-align: center;
  margin-top: v.$spacing-xl;
}

@media (max-width: v.$breakpoint-md) {
  .model-categories {
    flex-wrap: wrap;
  }

  .category-tab {
    flex: 1 1 calc(50% - #{v.$spacing-md});
    text-align: center;
  }

  .models-grid {
    grid-template-columns: 1fr;
  }
}
