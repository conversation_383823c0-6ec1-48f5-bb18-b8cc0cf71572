package com.debate_ournament.users.controller;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.debate_ournament.dto.common.ApiResponse;
import com.debate_ournament.users.entity.Captcha;
import com.debate_ournament.users.service.CaptchaService;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpSession;

/**
 * 验证码控制器
 */
@RestController
@RequestMapping("/api/captcha")
public class CaptchaController {

    private static final Logger logger = LoggerFactory.getLogger(CaptchaController.class);

    private final CaptchaService captchaService;

    @Autowired
    public CaptchaController(CaptchaService captchaService) {
        this.captchaService = captchaService;
    }

    /**
     * 生成验证码
     */
    @GetMapping("/generate")
    public ResponseEntity<ApiResponse<Map<String, Object>>> generateCaptcha(
            HttpServletRequest request,
            HttpSession session) {

        try {
            String sessionId = session.getId();
            if (sessionId == null) {
                sessionId = UUID.randomUUID().toString();
            }

            String clientIp = getClientIp(request);

            // 生成验证码
            Captcha captcha = captchaService.generateCaptcha(sessionId, clientIp);

            // 构建响应
            Map<String, Object> result = new HashMap<>();
            result.put("sessionId", sessionId);
            result.put("expiresAt", captcha.getExpiresAt());
            result.put("message", "验证码生成成功");

            return ResponseEntity.ok(ApiResponse.success("验证码生成成功", result));

        } catch (IllegalStateException e) {
            return ResponseEntity.status(429) // Too Many Requests
                    .body(ApiResponse.error(e.getMessage()));
        } catch (Exception e) {
            logger.error("生成验证码异常", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("生成验证码失败"));
        }
    }

    /**
     * 获取验证码图片
     */
    @GetMapping("/image")
    public ResponseEntity<?> getCaptchaImage(
            @RequestParam String sessionId,
            @RequestParam(defaultValue = "120") int width,
            @RequestParam(defaultValue = "40") int height) {

        try {
            String imageBase64 = captchaService.generateCaptchaImageBase64(sessionId);

            // 构建响应
            Map<String, Object> result = new HashMap<>();
            result.put("image", imageBase64);
            result.put("sessionId", sessionId);

            return ResponseEntity.ok(ApiResponse.success("获取验证码图片成功", result));

        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        } catch (Exception e) {
            logger.error("获取验证码图片异常", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("获取验证码图片失败"));
        }
    }

    /**
     * 验证验证码
     */
    @PostMapping("/verify")
    public ResponseEntity<ApiResponse<Map<String, Object>>> verifyCaptcha(
            @RequestParam String sessionId,
            @RequestParam String code) {

        try {
            boolean isValid = captchaService.verifyCaptcha(sessionId, code);

            Map<String, Object> result = new HashMap<>();
            result.put("valid", isValid);
            result.put("sessionId", sessionId);

            String message = isValid ? "验证码验证成功" : "验证码验证失败";

            return ResponseEntity.ok(ApiResponse.success(message, result));

        } catch (Exception e) {
            logger.error("验证验证码异常", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("验证验证码失败"));
        }
    }

    /**
     * 刷新验证码
     */
    @PostMapping("/refresh")
    public ResponseEntity<ApiResponse<Map<String, Object>>> refreshCaptcha(
            @RequestParam String sessionId,
            HttpServletRequest request) {

        try {
            String clientIp = getClientIp(request);

            // 刷新验证码
            Captcha captcha = captchaService.refreshCaptcha(sessionId, clientIp);

            // 构建响应
            Map<String, Object> result = new HashMap<>();
            result.put("sessionId", sessionId);
            result.put("expiresAt", captcha.getExpiresAt());
            result.put("message", "验证码刷新成功");

            return ResponseEntity.ok(ApiResponse.success("验证码刷新成功", result));

        } catch (IllegalStateException e) {
            return ResponseEntity.status(429) // Too Many Requests
                    .body(ApiResponse.error(e.getMessage()));
        } catch (Exception e) {
            logger.error("刷新验证码异常", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("刷新验证码失败"));
        }
    }

    /**
     * 检查验证码状态
     */
    @GetMapping("/status")
    public ResponseEntity<ApiResponse<Map<String, Object>>> checkCaptchaStatus(
            @RequestParam String sessionId) {

        try {
            boolean isValid = captchaService.isCaptchaValid(sessionId);

            Map<String, Object> result = new HashMap<>();
            result.put("valid", isValid);
            result.put("sessionId", sessionId);

            String message = isValid ? "验证码有效" : "验证码无效或已过期";

            return ResponseEntity.ok(ApiResponse.success(message, result));

        } catch (Exception e) {
            logger.error("检查验证码状态异常", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("检查验证码状态失败"));
        }
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIp(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }

        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }

        return request.getRemoteAddr();
    }
}
