package com.debate_ournament.ai.entity;

import java.time.LocalDateTime;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.PreUpdate;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * AI配置实体类
 * 存储各个AI平台的配置信息，包括API密钥、系统提示词等
 */
@Entity
@Table(name = "ai_configs")
public class AiConfig {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * AI服务提供商
     */
    @Enumerated(EnumType.STRING)
    @NotNull(message = "AI服务提供商不能为空")
    @Column(name = "provider", nullable = false)
    private AiProvider provider;

    /**
     * 配置名称
     */
    @NotBlank(message = "配置名称不能为空")
    @Column(name = "config_name", nullable = false, length = 100)
    private String configName;

    /**
     * API密钥
     */
    @NotBlank(message = "API密钥不能为空")
    @Column(name = "api_key", nullable = false, length = 500)
    private String apiKey;

    /**
     * API基础URL
     */
    @Column(name = "base_url", length = 255)
    private String baseUrl;

    /**
     * 模型名称
     */
    @Column(name = "model_name", length = 100)
    private String modelName;

    /**
     * 系统提示词
     */
    @Column(name = "system_prompt", columnDefinition = "TEXT")
    private String systemPrompt;

    /**
     * 温度参数（0.0-1.0）
     */
    @Column(name = "temperature")
    private Double temperature = 0.7;

    /**
     * 最大输出token数
     */
    @Column(name = "max_tokens")
    private Integer maxTokens = 2000;

    /**
     * 是否启用
     */
    @Column(name = "enabled", nullable = false)
    private Boolean enabled = true;

    /**
     * 是否为默认配置
     */
    @Column(name = "is_default", nullable = false)
    private Boolean isDefault = false;

    /**
     * 创建时间
     */
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    /**
     * 创建者ID
     */
    @Column(name = "created_by")
    private Long createdBy;

    // 默认构造函数
    public AiConfig() {
        this.createdAt = LocalDateTime.now();
    }

    // 带参构造函数
    public AiConfig(AiProvider provider, String configName, String apiKey) {
        this();
        this.provider = provider;
        this.configName = configName;
        this.apiKey = apiKey;
    }

    @PreUpdate
    public void preUpdate() {
        this.updatedAt = LocalDateTime.now();
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public AiProvider getProvider() {
        return provider;
    }

    public void setProvider(AiProvider provider) {
        this.provider = provider;
    }

    public String getConfigName() {
        return configName;
    }

    public void setConfigName(String configName) {
        this.configName = configName;
    }

    public String getApiKey() {
        return apiKey;
    }

    public void setApiKey(String apiKey) {
        this.apiKey = apiKey;
    }

    public String getBaseUrl() {
        return baseUrl;
    }

    public void setBaseUrl(String baseUrl) {
        this.baseUrl = baseUrl;
    }

    public String getModelName() {
        return modelName;
    }

    public void setModelName(String modelName) {
        this.modelName = modelName;
    }

    public String getSystemPrompt() {
        return systemPrompt;
    }

    public void setSystemPrompt(String systemPrompt) {
        this.systemPrompt = systemPrompt;
    }

    public Double getTemperature() {
        return temperature;
    }

    public void setTemperature(Double temperature) {
        this.temperature = temperature;
    }

    public Integer getMaxTokens() {
        return maxTokens;
    }

    public void setMaxTokens(Integer maxTokens) {
        this.maxTokens = maxTokens;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public Boolean getIsDefault() {
        return isDefault;
    }

    public void setIsDefault(Boolean isDefault) {
        this.isDefault = isDefault;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Long getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    @Override
    public String toString() {
        return "AiConfig{" +
                "id=" + id +
                ", provider=" + provider +
                ", configName='" + configName + '\'' +
                ", baseUrl='" + baseUrl + '\'' +
                ", modelName='" + modelName + '\'' +
                ", enabled=" + enabled +
                ", isDefault=" + isDefault +
                '}';
    }
}
