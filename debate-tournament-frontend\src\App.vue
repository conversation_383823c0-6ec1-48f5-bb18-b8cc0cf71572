<template>
  <el-config-provider>
    <div class="app-container">
      <AppHeader />

      <el-main class="main-content">
        <router-view v-slot="{ Component }">
          <transition name="page" mode="out-in">
            <component :is="Component" />
          </transition>
        </router-view>
      </el-main>

      <AppFooter />
    </div>
  </el-config-provider>
</template>

<script setup>
import { onMounted } from 'vue';
import { ElConfigProvider } from 'element-plus';
import AppHeader from '@/components/base/AppHeader.vue';
import AppFooter from '@/components/base/AppFooter.vue';

// 页面加载完成后移除加载状态
onMounted(() => {
  const loadingElement = document.getElementById('app-loading');
  if (loadingElement) {
    loadingElement.classList.add('fade-out');
    setTimeout(() => {
      loadingElement.remove();
    }, 500);
  }
});
</script>

<style lang="scss">
// 导入ElementPlus样式
@use 'element-plus/dist/index.css';

// CSS变量定义
:root {
  // 颜色
  --primary-color: #409eff;
  --success-color: #67c23a;
  --warning-color: #e6a23c;
  --danger-color: #f56c6c;
  --info-color: #909399;

  // 文本颜色
  --text-primary: #303133;
  --text-regular: #606266;
  --text-secondary: #909399;
  --text-placeholder: #c0c4cc;

  // 边框颜色
  --border-color: #dcdfe6;
  --border-color-light: #e4e7ed;

  // 背景颜色
  --background-color: #f5f7fa;
  --background-color-light: #fafafa;

  // 字体
  --font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', Arial, sans-serif;

  // 间距
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;

  // 圆角
  --border-radius-sm: 2px;
  --border-radius-md: 4px;
  --border-radius-lg: 8px;
  --border-radius-round: 20px;

  // 阴影
  --box-shadow-light: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  --box-shadow: 0 2px 4px rgba(0, 0, 0, .12), 0 0 6px rgba(0, 0, 0, .04);
  --box-shadow-dark: 0 2px 4px rgba(0, 0, 0, .12), 0 0 6px rgba(0, 0, 0, .12);
}

// 全局重置
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html,
body {
  font-family: var(--font-family);
  font-size: 16px;
  color: var(--text-regular);
  line-height: 1.5;
  background-color: var(--background-color-light);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  height: 100%;
}

#app {
  height: 100%;
}

.app-container {
  display: flex;
  flex-direction: column;
  min-height: 100%;
}

// 主内容区域
.main-content {
  flex: 1;
  padding: 0;
  background-color: var(--background-color-light);
}

// 通用容器
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
}

// 页面过渡动画
.page-enter-active,
.page-leave-active {
  transition: opacity 0.3s, transform 0.3s;
}

.page-enter-from {
  opacity: 0;
  transform: translateY(20px);
}

.page-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}

// 辅助类
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.mb-0 {
  margin-bottom: 0 !important;
}

.mb-1 {
  margin-bottom: var(--spacing-xs) !important;
}

.mb-2 {
  margin-bottom: var(--spacing-sm) !important;
}

.mb-3 {
  margin-bottom: var(--spacing-md) !important;
}

.mb-4 {
  margin-bottom: var(--spacing-lg) !important;
}

.mb-5 {
  margin-bottom: var(--spacing-xl) !important;
}

.mt-0 {
  margin-top: 0 !important;
}

.mt-1 {
  margin-top: var(--spacing-xs) !important;
}

.mt-2 {
  margin-top: var(--spacing-sm) !important;
}

.mt-3 {
  margin-top: var(--spacing-md) !important;
}

.mt-4 {
  margin-top: var(--spacing-lg) !important;
}

.mt-5 {
  margin-top: var(--spacing-xl) !important;
}

.p-0 {
  padding: 0 !important;
}

.p-1 {
  padding: var(--spacing-xs) !important;
}

.p-2 {
  padding: var(--spacing-sm) !important;
}

.p-3 {
  padding: var(--spacing-md) !important;
}

.p-4 {
  padding: var(--spacing-lg) !important;
}

.p-5 {
  padding: var(--spacing-xl) !important;
}

// 卡片样式
.card {
  background-color: #fff;
  border-radius: var(--border-radius-md);
  box-shadow: var(--box-shadow-light);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

// 页面标题
.page-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-lg);
}

// 页面内容区块
.page-section {
  margin-bottom: var(--spacing-xl);

  &__title {
    font-size: 20px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    position: relative;
    padding-left: var(--spacing-md);

    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      height: 70%;
      width: 4px;
      background-color: var(--primary-color);
      border-radius: var(--border-radius-sm);
    }
  }
}

// 响应式辅助类
@media (max-width: 768px) {
  .hidden-sm-and-down {
    display: none !important;
  }
}

@media (min-width: 769px) {
  .hidden-md-and-up {
    display: none !important;
  }
}

// 加载动画淡出
#app-loading.fade-out {
  opacity: 0;
  transition: opacity 0.5s;
}
</style>
