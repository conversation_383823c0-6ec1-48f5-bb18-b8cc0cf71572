import { ref, reactive, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useUserStore } from '@/store/user';
import { useDebateStore } from '@/store/debate';
import { useNotificationStore } from '@/store/notification';
import { useToast } from '@/composables/useToast';

export default {
  name: 'UserCenter',

  setup() {
    const router = useRouter();
    const userStore = useUserStore();
    const debateStore = useDebateStore();
    const notificationStore = useNotificationStore();
    const toast = useToast();

    // 数据
    const activeTab = ref('debates');
    const debateFilter = ref('all');
    const showEditProfile = ref(false);

    const loadingDebates = ref(false);
    const loadingFavorites = ref(false);
    const loadingProfile = ref(false);
    const loadingPassword = ref(false);
    const loadingPrivacy = ref(false);
    const loadingPreferences = ref(false);
    const loadingNotifications = ref(false);
    const loadingSecurity = ref(false);

    const passwordSuccess = ref(false);
    const passwordMessage = ref('');
    const privacySuccess = ref(false);
    const privacyMessage = ref('');

    // 显示/隐藏密码
    const showCurrentPassword = ref(false);
    const showNewPassword = ref(false);
    const showConfirmPassword = ref(false);

    const user = reactive({
      username: '',
      email: '',
      bio: '',
      avatar: '',
      level: '',
      debateCount: 0,
      winCount: 0,
      pointsEarned: 0,
      lastLoginTime: '',
      registrationDate: ''
    });

    const profileForm = reactive({
      username: '',
      bio: '',
      avatar: null
    });

    const profileErrors = reactive({
      username: ''
    });

    const passwordForm = reactive({
      currentPassword: '',
      newPassword: '',
      confirmPassword: ''
    });

    const passwordErrors = reactive({
      currentPassword: '',
      newPassword: '',
      confirmPassword: ''
    });

    const privacySettings = reactive({
      showEmail: true,
      showStatistics: true,
      allowNotifications: true,
      showOnlineStatus: true,
      allowDirectMessages: true
    });

    const preferenceSettings = reactive({
      defaultDebateRole: 'spectator',
      preferredTopics: [],
      languagePreference: 'zh-CN',
      debateStyle: 'academic',
      aiModelPreference: 'gpt-4'
    });

    const notificationSettings = reactive({
      emailNotifications: true,
      debateInvitations: true,
      debateUpdates: true,
      mentionNotifications: true,
      systemAnnouncements: true,
      digestFrequency: 'daily'
    });

    const securitySettings = reactive({
      twoFactorEnabled: false,
      loginNotifications: true,
      trustedDevices: [],
      lastPasswordChange: null,
      sessionTimeout: 30
    });

    const userDebates = ref([]);
    const favorites = ref([]);
    const userData = reactive({
      totalDebates: 0,
      wins: 0,
      losses: 0,
      draws: 0,
      winRate: 0,
      topicPreferences: [],
      recentActivities: []
    });

    const tabs = [
      { id: 'debates', name: '我的辩论', icon: '📋' },
      { id: 'favorites', name: '收藏的辩论', icon: '⭐' },
      { id: 'statistics', name: '数据统计', icon: '📊' },
      { id: 'preferences', name: '偏好设置', icon: '🎯' },
      { id: 'notifications', name: '通知设置', icon: '🔔' },
      { id: 'privacy', name: '隐私设置', icon: '🔒' },
      { id: 'security', name: '账户安全', icon: '🛡️' }
    ];

    // 计算属性
    const filteredDebates = computed(() => {
      if (debateFilter.value === 'all') return userDebates.value;
      return userDebates.value.filter(debate => debate.status === debateFilter.value);
    });

    // 方法
    const fetchUserData = async () => {
      if (!userStore.isLoggedIn) {
        router.push('/login');
        return;
      }

      try {
        await userStore.fetchUserProfile();
        const userData = userStore.user;

        Object.assign(user, {
          username: userData.username,
          email: userData.email,
          bio: userData.bio,
          avatar: userData.avatar,
          level: userData.level,
          debateCount: userData.debateCount,
          winCount: userData.winCount,
          pointsEarned: userData.pointsEarned,
          lastLoginTime: userData.lastLoginTime,
          registrationDate: userData.registrationDate
        });

        Object.assign(profileForm, {
          username: userData.username,
          bio: userData.bio || '',
          avatar: userData.avatar
        });

        Object.assign(privacySettings, userData.privacySettings || {});
        Object.assign(preferenceSettings, userData.preferenceSettings || {});
        Object.assign(notificationSettings, userData.notificationSettings || {});
        Object.assign(securitySettings, userData.securitySettings || {});
      } catch (error) {
        console.error('Error fetching user data:', error);
        toast.error('获取用户数据失败');
      }
    };

    const savePreferences = async () => {
      loadingPreferences.value = true;
      try {
        await userStore.updatePreferences(preferenceSettings);
        toast.success('偏好设置已保存');
      } catch (error) {
        console.error('Error saving preferences:', error);
        toast.error('保存偏好设置失败');
      } finally {
        loadingPreferences.value = false;
      }
    };

    const saveNotificationSettings = async () => {
      loadingNotifications.value = true;
      try {
        await notificationStore.updateSettings(notificationSettings);
        toast.success('通知设置已保存');
      } catch (error) {
        console.error('Error saving notification settings:', error);
        toast.error('保存通知设置失败');
      } finally {
        loadingNotifications.value = false;
      }
    };

    const toggleTwoFactorAuth = async () => {
      loadingSecurity.value = true;
      try {
        if (securitySettings.twoFactorEnabled) {
          await userStore.disableTwoFactor();
          securitySettings.twoFactorEnabled = false;
          toast.success('已关闭两步验证');
        } else {
          const qrCode = await userStore.enableTwoFactor();
          securitySettings.twoFactorEnabled = true;
          // 显示二维码设置弹窗
          showTwoFactorSetupModal(qrCode);
        }
      } catch (error) {
        console.error('Error toggling 2FA:', error);
        toast.error('两步验证设置失败');
      } finally {
        loadingSecurity.value = false;
      }
    };

    const showTwoFactorSetupModal = (qrCode) => {
      // 实现二维码设置弹窗逻辑
    };

    const fetchUserDebates = async () => {
      loadingDebates.value = true;
      try {
        const debates = await debateStore.fetchUserDebates();
        userDebates.value = debates;
      } catch (error) {
        console.error('Error fetching user debates:', error);
      } finally {
        loadingDebates.value = false;
      }
    };

    const fetchUserFavorites = async () => {
      loadingFavorites.value = true;
      try {
        const favoriteDebates = await debateStore.fetchUserFavorites();
        favorites.value = favoriteDebates;
      } catch (error) {
        console.error('Error fetching favorites:', error);
      } finally {
        loadingFavorites.value = false;
      }
    };

    const fetchUserStatistics = async () => {
      try {
        const stats = await userStore.fetchUserStatistics();

        Object.assign(userData, {
          totalDebates: stats.totalDebates || 0,
          wins: stats.wins || 0,
          losses: stats.losses || 0,
          draws: stats.draws || 0,
          winRate: stats.winRate || 0,
          topicPreferences: stats.topicPreferences || []
        });
      } catch (error) {
        console.error('Error fetching user statistics:', error);
      }
    };

    const navigateToDebate = (debateId) => {
      router.push(`/debate-arena/${debateId}`);
    };

    const handleAvatarChange = (event) => {
      const file = event.target.files[0];
      if (!file) return;

      // 检查文件类型和大小
      if (!file.type.match('image.*')) {
        alert('请选择图片文件');
        return;
      }

      if (file.size > 2 * 1024 * 1024) { // 2MB限制
        alert('图片大小不能超过2MB');
        return;
      }

      // 预览图片
      const reader = new FileReader();
      reader.onload = (e) => {
        profileForm.avatar = e.target.result;
      };
      reader.readAsDataURL(file);
    };

    const validateProfileForm = () => {
      let isValid = true;

      // 验证用户名
      if (!profileForm.username.trim()) {
        profileErrors.username = '用户名不能为空';
        isValid = false;
      } else if (profileForm.username.length < 3) {
        profileErrors.username = '用户名不能少于3个字符';
        isValid = false;
      } else {
        profileErrors.username = '';
      }

      return isValid;
    };

    const updateProfile = async () => {
      if (!validateProfileForm()) return;

      loadingProfile.value = true;
      try {
        await userStore.updateUserProfile({
          username: profileForm.username,
          bio: profileForm.bio,
          avatar: profileForm.avatar
        });

        // 更新成功后刷新用户数据
        await fetchUserData();

        // 关闭编辑模态框
        showEditProfile.value = false;
      } catch (error) {
        console.error('Error updating profile:', error);
        profileErrors.username = error.message || '更新失败，请稍后再试';
      } finally {
        loadingProfile.value = false;
      }
    };

    const validatePasswordForm = () => {
      let isValid = true;

      // 验证当前密码
      if (!passwordForm.currentPassword) {
        passwordErrors.currentPassword = '请输入当前密码';
        isValid = false;
      } else {
        passwordErrors.currentPassword = '';
      }

      // 验证新密码
      if (!passwordForm.newPassword) {
        passwordErrors.newPassword = '请输入新密码';
        isValid = false;
      } else if (passwordForm.newPassword.length < 8) {
        passwordErrors.newPassword = '密码不能少于8个字符';
        isValid = false;
      } else {
        passwordErrors.newPassword = '';
      }

      // 验证确认密码
      if (passwordForm.newPassword !== passwordForm.confirmPassword) {
        passwordErrors.confirmPassword = '两次输入的密码不一致';
        isValid = false;
      } else {
        passwordErrors.confirmPassword = '';
      }

      return isValid;
    };

    const changePassword = async () => {
      if (!validatePasswordForm()) return;

      loadingPassword.value = true;
      try {
        await userStore.changePassword({
          currentPassword: passwordForm.currentPassword,
          newPassword: passwordForm.newPassword
        });

        // 重置表单
        passwordForm.currentPassword = '';
        passwordForm.newPassword = '';
        passwordForm.confirmPassword = '';

        passwordSuccess.value = true;
        passwordMessage.value = '密码修改成功';

        // 3秒后清除成功消息
        setTimeout(() => {
          passwordSuccess.value = false;
          passwordMessage.value = '';
        }, 3000);
      } catch (error) {
        console.error('Error changing password:', error);
        passwordSuccess.value = false;
        passwordMessage.value = error.message || '密码修改失败，请检查当前密码是否正确';
      } finally {
        loadingPassword.value = false;
      }
    };

    const savePrivacySettings = async () => {
      loadingPrivacy.value = true;
      try {
        await userStore.updatePrivacySettings(privacySettings);

        privacySuccess.value = true;
        privacyMessage.value = '隐私设置已保存';

        // 3秒后清除成功消息
        setTimeout(() => {
          privacySuccess.value = false;
          privacyMessage.value = '';
        }, 3000);
      } catch (error) {
        console.error('Error saving privacy settings:', error);
        privacySuccess.value = false;
        privacyMessage.value = error.message || '保存失败，请稍后再试';
      } finally {
        loadingPrivacy.value = false;
      }
    };

    // 辅助方法
    const formatTime = (timestamp) => {
      if (!timestamp) return '';

      const date = new Date(timestamp);
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    };

    const getStatusText = (status) => {
      const statusMap = {
        'waiting': '等待中',
        'active': '进行中',
        'paused': '已暂停',
        'completed': '已结束'
      };
      return statusMap[status] || '未知状态';
    };

    const getRoleText = (role) => {
      const roleMap = {
        'supporter': '正方',
        'opposer': '反方',
        'judge': '裁判',
        'spectator': '观众'
      };
      return roleMap[role] || '未知角色';
    };

    // 监听标签切换以按需加载数据
    const handleTabChange = async (tab) => {
      activeTab.value = tab;

      if (tab === 'debates' && userDebates.value.length === 0) {
        await fetchUserDebates();
      } else if (tab === 'favorites' && favorites.value.length === 0) {
        await fetchUserFavorites();
      } else if (tab === 'statistics') {
        await fetchUserStatistics();
      }
    };

    // 生命周期钩子
    onMounted(async () => {
      if (!userStore.isLoggedIn) {
        router.push('/login');
        return;
      }

      await fetchUserData();
      await fetchUserDebates(); // 默认加载我的辩论
    });

    return {
      user,
      activeTab,
      debateFilter,
      showEditProfile,
      loadingDebates,
      loadingFavorites,
      loadingProfile,
      loadingPassword,
      loadingPrivacy,
      passwordSuccess,
      passwordMessage,
      privacySuccess,
      privacyMessage,
      showCurrentPassword,
      showNewPassword,
      showConfirmPassword,
      profileForm,
      profileErrors,
      passwordForm,
      passwordErrors,
      privacySettings,
      userDebates,
      favorites,
      userData,
      tabs,
      filteredDebates,
      navigateToDebate,
      handleAvatarChange,
      updateProfile,
      changePassword,
      savePrivacySettings,
      formatTime,
      getStatusText,
      getRoleText,
      handleTabChange
    };
  }
};
