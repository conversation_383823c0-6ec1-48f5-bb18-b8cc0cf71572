package com.debate_ournament.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 应用配置类
 */
@Configuration
@ConfigurationProperties(prefix = "app")
public class AppConfig {

    private Jwt jwt = new Jwt();
    private Captcha captcha = new Captcha();
    private Email email = new Email();
    private Password password = new Password();
    private Account account = new Account();
    private Upload upload = new Upload();

    // JWT配置
    public static class Jwt {
        private String secret = "mySecretKey123456789012345678901234567890";
        private Long expiration = 86400000L; // 24小时
        private Long refreshExpiration = 604800000L; // 7天

        public String getSecret() {
            return secret;
        }

        public void setSecret(String secret) {
            this.secret = secret;
        }

        public Long getExpiration() {
            return expiration;
        }

        public void setExpiration(Long expiration) {
            this.expiration = expiration;
        }

        public Long getRefreshExpiration() {
            return refreshExpiration;
        }

        public void setRefreshExpiration(Long refreshExpiration) {
            this.refreshExpiration = refreshExpiration;
        }
    }

    // 验证码配置
    public static class Captcha {
        private Integer width = 120;
        private Integer height = 40;
        private Integer length = 4;
        private Integer expiration = 300; // 5分钟

        public Integer getWidth() {
            return width;
        }

        public void setWidth(Integer width) {
            this.width = width;
        }

        public Integer getHeight() {
            return height;
        }

        public void setHeight(Integer height) {
            this.height = height;
        }

        public Integer getLength() {
            return length;
        }

        public void setLength(Integer length) {
            this.length = length;
        }

        public Integer getExpiration() {
            return expiration;
        }

        public void setExpiration(Integer expiration) {
            this.expiration = expiration;
        }
    }

    // 邮箱配置
    public static class Email {
        private Verification verification = new Verification();

        public static class Verification {
            private Integer expiration = 1440; // 24小时
            private Integer resendInterval = 60; // 1分钟

            public Integer getExpiration() {
                return expiration;
            }

            public void setExpiration(Integer expiration) {
                this.expiration = expiration;
            }

            public Integer getResendInterval() {
                return resendInterval;
            }

            public void setResendInterval(Integer resendInterval) {
                this.resendInterval = resendInterval;
            }
        }

        public Verification getVerification() {
            return verification;
        }

        public void setVerification(Verification verification) {
            this.verification = verification;
        }
    }

    // 密码配置
    public static class Password {
        private Reset reset = new Reset();

        public static class Reset {
            private Integer expiration = 60; // 1小时
            private Integer maxAttempts = 3;

            public Integer getExpiration() {
                return expiration;
            }

            public void setExpiration(Integer expiration) {
                this.expiration = expiration;
            }

            public Integer getMaxAttempts() {
                return maxAttempts;
            }

            public void setMaxAttempts(Integer maxAttempts) {
                this.maxAttempts = maxAttempts;
            }
        }

        public Reset getReset() {
            return reset;
        }

        public void setReset(Reset reset) {
            this.reset = reset;
        }
    }

    // 账号配置
    public static class Account {
        private Lock lock = new Lock();

        public static class Lock {
            private Integer maxAttempts = 5;
            private Integer lockDuration = 30; // 30分钟

            public Integer getMaxAttempts() {
                return maxAttempts;
            }

            public void setMaxAttempts(Integer maxAttempts) {
                this.maxAttempts = maxAttempts;
            }

            public Integer getLockDuration() {
                return lockDuration;
            }

            public void setLockDuration(Integer lockDuration) {
                this.lockDuration = lockDuration;
            }
        }

        public Lock getLock() {
            return lock;
        }

        public void setLock(Lock lock) {
            this.lock = lock;
        }
    }

    // 文件上传配置
    public static class Upload {
        private String path = "/uploads";
        private Avatar avatar = new Avatar();

        public static class Avatar {
            private String maxSize = "2MB";
            private String allowedTypes = "jpg,jpeg,png,gif";

            public String getMaxSize() {
                return maxSize;
            }

            public void setMaxSize(String maxSize) {
                this.maxSize = maxSize;
            }

            public String getAllowedTypes() {
                return allowedTypes;
            }

            public void setAllowedTypes(String allowedTypes) {
                this.allowedTypes = allowedTypes;
            }
        }

        public String getPath() {
            return path;
        }

        public void setPath(String path) {
            this.path = path;
        }

        public Avatar getAvatar() {
            return avatar;
        }

        public void setAvatar(Avatar avatar) {
            this.avatar = avatar;
        }
    }


    // Getters and Setters
    public Jwt getJwt() {
        return jwt;
    }

    public void setJwt(Jwt jwt) {
        this.jwt = jwt;
    }

    public Captcha getCaptcha() {
        return captcha;
    }

    public void setCaptcha(Captcha captcha) {
        this.captcha = captcha;
    }

    public Email getEmail() {
        return email;
    }

    public void setEmail(Email email) {
        this.email = email;
    }

    public Password getPassword() {
        return password;
    }

    public void setPassword(Password password) {
        this.password = password;
    }

    public Account getAccount() {
        return account;
    }

    public void setAccount(Account account) {
        this.account = account;
    }

    public Upload getUpload() {
        return upload;
    }

    public void setUpload(Upload upload) {
        this.upload = upload;
    }
}
