package com.debate_ournament.users.service;

import java.io.IOException;
import java.util.List;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import com.debate_ournament.users.entity.User;
import com.debate_ournament.users.entity.UserProfile;
import com.debate_ournament.users.repository.UserProfileRepository;
import com.debate_ournament.users.repository.UserRepository;

/**
 * 用户资料服务类
 */
@Service
@Transactional
public class UserProfileService {

    @Autowired
    private UserProfileRepository userProfileRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private FileStorageService fileStorageService;

    /**
     * 为新用户创建资料信息
     * @param user 用户
     * @return 用户资料信息
     */
    public UserProfile createUserProfile(User user) {
        if (userProfileRepository.existsByUserId(user.getId())) {
            throw new IllegalArgumentException("用户已存在资料信息");
        }

        UserProfile userProfile = new UserProfile(user);
        return userProfileRepository.save(userProfile);
    }

    /**
     * 根据用户ID获取资料信息
     * @param userId 用户ID
     * @return 用户资料信息
     */
    @Transactional(readOnly = true)
    public Optional<UserProfile> getUserProfile(Long userId) {
        return userProfileRepository.findByUserId(userId);
    }

    /**
     * 根据用户名获取资料信息
     * @param username 用户名
     * @return 用户资料信息
     */
    @Transactional(readOnly = true)
    public Optional<UserProfile> getUserProfileByUsername(String username) {
        return userProfileRepository.findByUsername(username);
    }

    /**
     * 更新用户资料
     * @param userId 用户ID
     * @param userProfile 用户资料信息
     * @return 更新后的用户资料
     */
    public UserProfile updateUserProfile(Long userId, UserProfile userProfile) {
        UserProfile existingProfile = getUserProfile(userId)
                .orElseThrow(() -> new IllegalArgumentException("用户资料不存在"));

        // 更新基本信息
        existingProfile.setDisplayName(userProfile.getDisplayName());
        existingProfile.setBio(userProfile.getBio());
        existingProfile.setBirthDate(userProfile.getBirthDate());
        existingProfile.setGender(userProfile.getGender());
        existingProfile.setLocation(userProfile.getLocation());
        existingProfile.setSchoolOrOrganization(userProfile.getSchoolOrOrganization());
        existingProfile.setMajorOrField(userProfile.getMajorOrField());
        existingProfile.setIsProfilePublic(userProfile.getIsProfilePublic());
        existingProfile.setShowEmail(userProfile.getShowEmail());
        existingProfile.setShowRealName(userProfile.getShowRealName());

        return userProfileRepository.save(existingProfile);
    }

    /**
     * 上传用户头像
     * @param userId 用户ID
     * @param file 头像文件
     * @return 头像URL
     * @throws IOException 文件操作异常
     */
    public String uploadAvatar(Long userId, MultipartFile file) throws IOException {
        UserProfile userProfile = getUserProfile(userId)
                .orElseThrow(() -> new IllegalArgumentException("用户资料不存在"));

        // 删除旧头像
        if (userProfile.hasAvatar()) {
            fileStorageService.deleteAvatarFile(userProfile.getAvatarUrl());
        }

        // 存储新头像
        String avatarUrl = fileStorageService.storeAvatarFile(file, userId);

        // 更新用户资料
        userProfile.updateAvatar(
                avatarUrl,
                file.getOriginalFilename(),
                file.getSize(),
                file.getContentType()
        );

        userProfileRepository.save(userProfile);
        return avatarUrl;
    }

    /**
     * 删除用户头像
     * @param userId 用户ID
     */
    public void deleteAvatar(Long userId) {
        UserProfile userProfile = getUserProfile(userId)
                .orElseThrow(() -> new IllegalArgumentException("用户资料不存在"));

        if (userProfile.hasAvatar()) {
            // 删除文件
            fileStorageService.deleteAvatarFile(userProfile.getAvatarUrl());

            // 清除数据库记录
            userProfile.clearAvatar();
            userProfileRepository.save(userProfile);
        }
    }

    /**
     * 获取所有公开的用户资料
     * @return 公开用户资料列表
     */
    @Transactional(readOnly = true)
    public List<UserProfile> getPublicProfiles() {
        return userProfileRepository.findPublicProfiles();
    }

    /**
     * 搜索用户资料
     * @param keyword 关键词
     * @return 匹配的用户资料列表
     */
    @Transactional(readOnly = true)
    public List<UserProfile> searchProfiles(String keyword) {
        if (keyword == null || keyword.trim().isEmpty()) {
            return getPublicProfiles();
        }
        return userProfileRepository.findByDisplayNameContaining(keyword.trim());
    }

    /**
     * 根据学校或机构搜索用户
     * @param schoolOrOrganization 学校或机构
     * @return 匹配的用户资料列表
     */
    @Transactional(readOnly = true)
    public List<UserProfile> searchBySchoolOrOrganization(String schoolOrOrganization) {
        return userProfileRepository.findBySchoolOrOrganizationContaining(schoolOrOrganization);
    }

    /**
     * 根据所在地搜索用户
     * @param location 所在地
     * @return 匹配的用户资料列表
     */
    @Transactional(readOnly = true)
    public List<UserProfile> searchByLocation(String location) {
        return userProfileRepository.findByLocationContaining(location);
    }

    /**
     * 根据性别获取用户资料
     * @param gender 性别
     * @return 匹配的用户资料列表
     */
    @Transactional(readOnly = true)
    public List<UserProfile> getUserProfilesByGender(UserProfile.Gender gender) {
        return userProfileRepository.findByGender(gender);
    }

    /**
     * 统计有头像的用户数量
     * @return 用户数量
     */
    @Transactional(readOnly = true)
    public long countUsersWithAvatar() {
        return userProfileRepository.countUsersWithAvatar();
    }

    /**
     * 统计公开资料的用户数量
     * @return 用户数量
     */
    @Transactional(readOnly = true)
    public long countPublicProfiles() {
        return userProfileRepository.countPublicProfiles();
    }

    /**
     * 批量更新用户资料的公开状态
     * @param userIds 用户ID列表
     * @param isPublic 是否公开
     */
    public void updateProfileVisibility(List<Long> userIds, boolean isPublic) {
        userProfileRepository.updateProfileVisibility(userIds, isPublic);
    }

    /**
     * 初始化用户资料信息（用于已存在的用户）
     * @param userId 用户ID
     * @return 用户资料信息
     */
    public UserProfile initializeUserProfile(Long userId) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new IllegalArgumentException("用户不存在"));

        if (userProfileRepository.existsByUserId(userId)) {
            return getUserProfile(userId).get();
        }

        return createUserProfile(user);
    }

    /**
     * 删除用户资料信息
     * @param userId 用户ID
     */
    public void deleteUserProfile(Long userId) {
        UserProfile userProfile = getUserProfile(userId).orElse(null);

        if (userProfile != null) {
            // 删除头像文件
            if (userProfile.hasAvatar()) {
                fileStorageService.deleteAvatarFile(userProfile.getAvatarUrl());
            }

            // 删除数据库记录
            userProfileRepository.deleteByUserId(userId);
        }
    }

    /**
     * 验证头像文件
     * @param file 文件
     * @return 验证结果消息，null表示验证通过
     */
    public String validateAvatarFile(MultipartFile file) {
        try {
            // 这里调用FileStorageService的验证方法
            // 由于FileStorageService的validateAvatarFile是私有方法，
            // 我们可以通过尝试存储文件来验证
            if (file == null || file.isEmpty()) {
                return "文件不能为空";
            }

            if (file.getSize() > 10 * 1024 * 1024) { // 10MB
                return "文件大小不能超过10MB";
            }

            String contentType = file.getContentType();
            if (contentType == null || !contentType.startsWith("image/")) {
                return "只能上传图片文件";
            }

            return null; // 验证通过
        } catch (Exception e) {
            return "文件验证失败：" + e.getMessage();
        }
    }

    /**
     * 获取用户的完整显示信息（包含等级信息）
     * @param userId 用户ID
     * @return 用户显示信息
     */
    @Transactional(readOnly = true)
    public UserDisplayInfo getUserDisplayInfo(Long userId) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new IllegalArgumentException("用户不存在"));

        UserProfile profile = getUserProfile(userId).orElse(null);

        return new UserDisplayInfo(user, profile);
    }

    /**
     * 用户显示信息DTO
     */
    public static class UserDisplayInfo {
        private final User user;
        private final UserProfile profile;

        public UserDisplayInfo(User user, UserProfile profile) {
            this.user = user;
            this.profile = profile;
        }

        public User getUser() {
            return user;
        }

        public UserProfile getProfile() {
            return profile;
        }

        public String getDisplayName() {
            return profile != null ? profile.getEffectiveDisplayName() : user.getUsername();
        }

        public String getAvatarUrl() {
            return profile != null ? profile.getEffectiveAvatarUrl() : "/images/default-avatar.png";
        }

        public boolean hasProfile() {
            return profile != null;
        }

        public boolean isProfilePublic() {
            return profile != null && profile.getIsProfilePublic();
        }
    }
}
