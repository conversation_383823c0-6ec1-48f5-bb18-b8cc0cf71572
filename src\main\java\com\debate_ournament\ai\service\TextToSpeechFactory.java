package com.debate_ournament.ai.service;

import org.springframework.stereotype.Component;

import com.debate_ournament.ai.entity.AiConfig;
import com.debate_ournament.ai.entity.AiProvider;
import com.debate_ournament.ai.service.impl.OpenAiTextToSpeechClient;

/**
 * 文本转语音服务工厂
 * 根据AI提供商返回相应的TTS客户端
 */
@Component
public class TextToSpeechFactory {

    private final OpenAiTextToSpeechClient openAiTtsClient;

    public TextToSpeechFactory(OpenAiTextToSpeechClient openAiTtsClient) {
        this.openAiTtsClient = openAiTtsClient;
    }

    /**
     * 根据AI配置获取TTS客户端
     */
    public TextToSpeechService getTtsClient(AiConfig aiConfig) {
        if (aiConfig == null || aiConfig.getProvider() == null) {
            throw new IllegalArgumentException("AI配置或提供商不能为空");
        }

        switch (aiConfig.getProvider()) {
            case OPENAI:
                return openAiTtsClient;
            case DEEPSEEK:
                // DeepSeek暂不支持TTS
                throw new UnsupportedOperationException("DeepSeek暂不支持语音合成");
            case ALIBABA_BAILIAN:
                // 阿里云百炼暂不支持TTS
                throw new UnsupportedOperationException("阿里云百炼暂不支持语音合成");
            case SILICONFLOW:
                // SiliconFlow暂不支持TTS
                throw new UnsupportedOperationException("SiliconFlow暂不支持语音合成");
            case OPENROUTER:
                // OpenRouter暂不支持TTS
                throw new UnsupportedOperationException("OpenRouter暂不支持语音合成");
            default:
                throw new UnsupportedOperationException("不支持的AI提供商: " + aiConfig.getProvider());
        }
    }

    /**
     * 检查提供商是否支持TTS
     */
    public boolean supportsTts(AiProvider provider) {
        return switch (provider) {
            case OPENAI -> true;
            case DEEPSEEK, ALIBABA_BAILIAN, SILICONFLOW, OPENROUTER,
                 ANTHROPIC, BAIDU, ALIBABA, ZHIPU, TENCENT, MOONSHOT -> false;
        };
    }
}
