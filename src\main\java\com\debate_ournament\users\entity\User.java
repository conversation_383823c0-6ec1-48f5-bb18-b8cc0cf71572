package com.debate_ournament.users.entity;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OneToOne;
import jakarta.persistence.PrePersist;
import jakarta.persistence.PreUpdate;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * 用户实体类
 * 对应数据库表：users
 */
@Entity
@Table(name = "users")
@EntityListeners(AuditingEntityListener.class)
public class User {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false, columnDefinition = "bigint COMMENT '用户主键ID'")
    private Long id;

    @NotBlank(message = "用户名不能为空")
    @Size(min = 3, max = 50, message = "用户名长度必须在3-50个字符之间")
    @Column(name = "username", unique = true, nullable = false, length = 50,
            columnDefinition = "varchar(50) COMMENT '用户名（唯一标识）'")
    private String username;

    @NotBlank(message = "邮箱不能为空")
    @Email(message = "邮箱格式不正确")
    @Column(name = "email", unique = true, nullable = false, length = 100,
            columnDefinition = "varchar(100) COMMENT '邮箱地址（唯一标识）'")
    private String email;

    @NotBlank(message = "密码不能为空")
    @Column(name = "password", nullable = false, length = 255,
            columnDefinition = "varchar(255) COMMENT '加密后的用户密码'")
    private String password;

    @Column(name = "email_verified", nullable = false,
            columnDefinition = "boolean DEFAULT false COMMENT '邮箱是否已验证（true=已验证，false=未验证）'")
    private Boolean emailVerified = false;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false, length = 20,
            columnDefinition = "varchar(20) DEFAULT 'ACTIVE' COMMENT '用户账号状态（ACTIVE=活跃，INACTIVE=非活跃，LOCKED=锁定，BANNED=封禁）'")
    private UserStatus status = UserStatus.ACTIVE;

    @Column(name = "login_attempts", nullable = false,
            columnDefinition = "int DEFAULT 0 COMMENT '登录失败尝试次数'")
    private Integer loginAttempts = 0;

    @Column(name = "last_login_attempt",
            columnDefinition = "datetime COMMENT '最后一次登录尝试时间'")
    private LocalDateTime lastLoginAttempt;

    @Column(name = "locked_until",
            columnDefinition = "datetime COMMENT '账号锁定截止时间'")
    private LocalDateTime lockedUntil;

    @Column(name = "last_login_ip", length = 45,
            columnDefinition = "varchar(45) COMMENT '最后登录IP地址（支持IPv6）'")
    private String lastLoginIp;

    @Column(name = "last_login_time",
            columnDefinition = "datetime COMMENT '最后成功登录时间'")
    private LocalDateTime lastLoginTime;

    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false,
            columnDefinition = "datetime DEFAULT CURRENT_TIMESTAMP COMMENT '账号创建时间'")
    private LocalDateTime createdAt;

    @LastModifiedDate
    @Column(name = "updated_at", nullable = false,
            columnDefinition = "datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '账号信息最后更新时间'")
    private LocalDateTime updatedAt;

    // 关联实体
    @OneToOne(mappedBy = "user", cascade = CascadeType.ALL, orphanRemoval = true)
    private UserPreferences preferences;

    @OneToOne(mappedBy = "user", cascade = CascadeType.ALL, orphanRemoval = true)
    private NotificationSettings notificationSettings;

    @OneToOne(mappedBy = "user", cascade = CascadeType.ALL, orphanRemoval = true)
    private SecuritySettings securitySettings;

    @OneToOne(mappedBy = "user", cascade = CascadeType.ALL, orphanRemoval = true)
    private UserLevel userLevel;

    @OneToOne(mappedBy = "user", cascade = CascadeType.ALL, orphanRemoval = true)
    private UserProfile userProfile;

    @OneToMany(mappedBy = "user", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<UserDevice> devices = new ArrayList<>();

    @OneToMany(mappedBy = "user", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<PasswordResetToken> passwordResetTokens = new ArrayList<>();

    /**
     * 用户状态枚举
     */
    public enum UserStatus {
        ACTIVE("活跃"),     // 活跃用户
        INACTIVE("非活跃"), // 非活跃用户（如未验证邮箱）
        LOCKED("锁定"),    // 临时锁定用户
        BANNED("封禁");    // 永久封禁用户

        private final String description;

        UserStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    @PrePersist
    protected void onCreate() {
        LocalDateTime now = LocalDateTime.now();
        if (createdAt == null) {
            createdAt = now;
        }
        if (updatedAt == null) {
            updatedAt = now;
        }
        // 初始化默认状态
        if (status == null) {
            status = UserStatus.ACTIVE;
        }
        if (emailVerified == null) {
            emailVerified = false;
        }
        if (loginAttempts == null) {
            loginAttempts = 0;
        }
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }

    // 业务方法

    /**
     * 检查账号是否未被锁定
     */
    public boolean isAccountNonLocked() {
        if (status == UserStatus.BANNED) {
            return false;
        }
        if (lockedUntil == null) {
            return true;
        }
        return LocalDateTime.now().isAfter(lockedUntil);
    }

    /**
     * 检查用户是否为活跃状态
     */
    public boolean isActive() {
        return status == UserStatus.ACTIVE;
    }

    /**
     * 检查账号是否可用（未锁定且活跃）
     */
    public boolean isAccountUsable() {
        return isActive() && isAccountNonLocked();
    }

    /**
     * 增加登录失败次数
     */
    public void incrementLoginAttempts() {
        this.loginAttempts++;
        this.lastLoginAttempt = LocalDateTime.now();
    }

    /**
     * 重置登录失败次数
     */
    public void resetLoginAttempts() {
        this.loginAttempts = 0;
        this.lastLoginAttempt = null;
        this.lockedUntil = null;
        // 如果用户被临时锁定，解锁用户
        if (this.status == UserStatus.LOCKED) {
            this.status = UserStatus.ACTIVE;
        }
    }

    /**
     * 锁定账号
     * @param lockDurationMinutes 锁定时长（分钟）
     */
    public void lockAccount(int lockDurationMinutes) {
        this.lockedUntil = LocalDateTime.now().plusMinutes(lockDurationMinutes);
        this.status = UserStatus.LOCKED;
    }

    /**
     * 永久封禁账号
     */
    public void banAccount() {
        this.status = UserStatus.BANNED;
        this.lockedUntil = null; // 清除临时锁定时间
    }

    /**
     * 更新最后登录信息
     * @param clientIp 客户端IP地址
     */
    public void updateLastLogin(String clientIp) {
        this.lastLoginIp = clientIp;
        this.lastLoginTime = LocalDateTime.now();
        // 登录成功后重置失败次数
        resetLoginAttempts();
    }

    /**
     * 验证邮箱
     */
    public void verifyEmail() {
        this.emailVerified = true;
        // 如果用户状态是非活跃（通常是因为邮箱未验证），改为活跃
        if (this.status == UserStatus.INACTIVE) {
            this.status = UserStatus.ACTIVE;
        }
    }

    /**
     * 激活用户账号
     */
    public void activateAccount() {
        this.status = UserStatus.ACTIVE;
        this.lockedUntil = null;
        this.loginAttempts = 0;
    }

    /**
     * 停用用户账号
     */
    public void deactivateAccount() {
        this.status = UserStatus.INACTIVE;
    }

    // Getters and Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }

    public String getUsername() { return username; }
    public void setUsername(String username) { this.username = username; }

    public String getEmail() { return email; }
    public void setEmail(String email) { this.email = email; }

    public String getPassword() { return password; }
    public void setPassword(String password) { this.password = password; }

    public Boolean getEmailVerified() { return emailVerified; }
    public void setEmailVerified(Boolean emailVerified) { this.emailVerified = emailVerified; }

    public UserStatus getStatus() { return status; }
    public void setStatus(UserStatus status) { this.status = status; }

    public Integer getLoginAttempts() { return loginAttempts; }
    public void setLoginAttempts(Integer loginAttempts) { this.loginAttempts = loginAttempts; }

    public LocalDateTime getLastLoginAttempt() { return lastLoginAttempt; }
    public void setLastLoginAttempt(LocalDateTime lastLoginAttempt) { this.lastLoginAttempt = lastLoginAttempt; }

    public LocalDateTime getLockedUntil() { return lockedUntil; }
    public void setLockedUntil(LocalDateTime lockedUntil) { this.lockedUntil = lockedUntil; }

    public String getLastLoginIp() { return lastLoginIp; }
    public void setLastLoginIp(String lastLoginIp) { this.lastLoginIp = lastLoginIp; }

    public LocalDateTime getLastLoginTime() { return lastLoginTime; }
    public void setLastLoginTime(LocalDateTime lastLoginTime) { this.lastLoginTime = lastLoginTime; }

    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }

    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }

    public UserPreferences getPreferences() { return preferences; }
    public void setPreferences(UserPreferences preferences) {
        this.preferences = preferences;
        if (preferences != null) {
            preferences.setUser(this);
        }
    }

    public NotificationSettings getNotificationSettings() { return notificationSettings; }
    public void setNotificationSettings(NotificationSettings notificationSettings) {
        this.notificationSettings = notificationSettings;
        if (notificationSettings != null) {
            notificationSettings.setUser(this);
        }
    }

    public SecuritySettings getSecuritySettings() { return securitySettings; }
    public void setSecuritySettings(SecuritySettings securitySettings) {
        this.securitySettings = securitySettings;
        if (securitySettings != null) {
            securitySettings.setUser(this);
        }
    }

    public UserLevel getUserLevel() { return userLevel; }
    public void setUserLevel(UserLevel userLevel) {
        this.userLevel = userLevel;
        if (userLevel != null) {
            userLevel.setUser(this);
        }
    }

    public UserProfile getUserProfile() { return userProfile; }
    public void setUserProfile(UserProfile userProfile) {
        this.userProfile = userProfile;
        if (userProfile != null) {
            userProfile.setUser(this);
        }
    }

    public List<UserDevice> getDevices() { return devices; }
    public void setDevices(List<UserDevice> devices) { this.devices = devices; }

    public List<PasswordResetToken> getPasswordResetTokens() { return passwordResetTokens; }
    public void setPasswordResetTokens(List<PasswordResetToken> passwordResetTokens) { this.passwordResetTokens = passwordResetTokens; }

    // 添加设备的便捷方法
    public void addDevice(UserDevice device) {
        devices.add(device);
        device.setUser(this);
    }

    public void removeDevice(UserDevice device) {
        devices.remove(device);
        device.setUser(null);
    }

    @Override
    public String toString() {
        return "User{" +
                "id=" + id +
                ", username='" + username + '\'' +
                ", email='" + email + '\'' +
                ", emailVerified=" + emailVerified +
                ", status=" + status +
                ", createdAt=" + createdAt +
                '}';
    }
}
