<template>
  <div class="encryption-test">
    <div class="test-header">
      <h1>加密传输测试</h1>
      <p>测试前后端加密通信功能</p>
    </div>

    <div class="test-sections">
      <!-- 公钥获取测试 -->
      <div class="test-section">
        <h2>1. 公钥获取测试</h2>
        <div class="test-controls">
          <el-button @click="testGetPublicKey" type="primary">获取公钥</el-button>
        </div>
        <div class="test-result">
          <h3>结果:</h3>
          <pre>{{ publicKeyResult }}</pre>
        </div>
      </div>

      <!-- 加密登录测试 -->
      <div class="test-section">
        <h2>2. 加密登录测试</h2>
        <div class="test-form">
          <el-form :model="loginForm" label-width="100px">
            <el-form-item label="用户名">
              <el-input v-model="loginForm.username" placeholder="请输入用户名"></el-input>
            </el-form-item>
            <el-form-item label="密码">
              <el-input v-model="loginForm.password" type="password" placeholder="请输入密码"></el-input>
            </el-form-item>
            <el-form-item label="验证码">
              <div class="captcha-container">
                <el-input v-model="loginForm.captcha" placeholder="请输入验证码" style="width: 200px;"></el-input>
                <img :src="captchaUrl" @click="refreshCaptcha" class="captcha-image" alt="验证码">
              </div>
            </el-form-item>
          </el-form>
        </div>
        <div class="test-controls">
          <el-button @click="testEncryptedLogin" type="success">加密登录</el-button>
          <el-button @click="testPlaintextLogin" type="warning">明文登录</el-button>
        </div>
        <div class="test-result">
          <h3>结果:</h3>
          <pre>{{ loginResult }}</pre>
        </div>
      </div>

      <!-- 加密注册测试 -->
      <div class="test-section">
        <h2>3. 加密注册测试</h2>
        <div class="test-form">
          <el-form :model="registerForm" label-width="100px">
            <el-form-item label="用户名">
              <el-input v-model="registerForm.username" placeholder="请输入用户名"></el-input>
            </el-form-item>
            <el-form-item label="邮箱">
              <el-input v-model="registerForm.email" placeholder="请输入邮箱"></el-input>
            </el-form-item>
            <el-form-item label="密码">
              <el-input v-model="registerForm.password" type="password" placeholder="请输入密码"></el-input>
            </el-form-item>
            <el-form-item label="确认密码">
              <el-input v-model="registerForm.confirmPassword" type="password" placeholder="请确认密码"></el-input>
            </el-form-item>
            <el-form-item label="验证码">
              <div class="captcha-container">
                <el-input v-model="registerForm.captcha" placeholder="请输入验证码" style="width: 200px;"></el-input>
                <img :src="captchaUrl" @click="refreshCaptcha" class="captcha-image" alt="验证码">
              </div>
            </el-form-item>
          </el-form>
        </div>
        <div class="test-controls">
          <el-button @click="testEncryptedRegister" type="success">加密注册</el-button>
          <el-button @click="testPlaintextRegister" type="warning">明文注册</el-button>
        </div>
        <div class="test-result">
          <h3>结果:</h3>
          <pre>{{ registerResult }}</pre>
        </div>
      </div>

      <!-- 加密状态监控 -->
      <div class="test-section">
        <h2>4. 加密状态监控</h2>
        <div class="test-controls">
          <el-button @click="getEncryptionStats" type="info">获取加密统计</el-button>
          <el-button @click="refreshEncryptionKeys" type="primary">刷新密钥</el-button>
        </div>
        <div class="test-result">
          <h3>加密存储状态:</h3>
          <pre>{{ encryptionStats }}</pre>
        </div>
      </div>

      <!-- 性能测试 -->
      <div class="test-section">
        <h2>5. 性能测试</h2>
        <div class="test-controls">
          <el-button @click="runPerformanceTest" type="danger" :loading="performanceTestRunning">
            运行性能测试
          </el-button>
        </div>
        <div class="test-result">
          <h3>性能测试结果:</h3>
          <pre>{{ performanceResult }}</pre>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { useEncryptionStore } from '@/store/encryption'
import { login, register, getCaptcha } from '@/api/auth'
import request from '@/api/index'

export default {
  name: 'EncryptionTest',
  setup() {
    const encryptionStore = useEncryptionStore()

    // 响应式数据
    const publicKeyResult = ref('')
    const loginResult = ref('')
    const registerResult = ref('')
    const encryptionStats = ref('')
    const performanceResult = ref('')
    const performanceTestRunning = ref(false)
    const captchaUrl = ref('')
    const sessionId = ref('')

    // 表单数据
    const loginForm = ref({
      username: 'testuser',
      password: 'password123',
      captcha: '',
      sessionId: ''
    })

    const registerForm = ref({
      username: 'newuser' + Date.now(),
      email: 'test' + Date.now() + '@example.com',
      password: 'password123',
      confirmPassword: 'password123',
      captcha: '',
      sessionId: ''
    })

    // 获取验证码
    const refreshCaptcha = async () => {
      try {
        const response = await getCaptcha()
        if (response instanceof Blob) {
          captchaUrl.value = URL.createObjectURL(response)
          // 从响应头获取sessionId（如果有的话）
          sessionId.value = Date.now().toString()
          loginForm.value.sessionId = sessionId.value
          registerForm.value.sessionId = sessionId.value
        }
      } catch (error) {
        console.error('获取验证码失败:', error)
      }
    }

    // 测试获取公钥
    const testGetPublicKey = async () => {
      try {
        publicKeyResult.value = '正在获取公钥...'
        await encryptionStore.initialize()

        publicKeyResult.value = JSON.stringify({
          success: true,
          message: '公钥获取成功',
          data: {
            publicKeyId: encryptionStore.publicKeyId,
            keyExpiresAt: encryptionStore.keyExpiresAt,
            isInitialized: encryptionStore.isInitialized,
            isKeyExpired: encryptionStore.isKeyExpired,
            publicKeyLength: encryptionStore.publicKey.length
          }
        }, null, 2)
      } catch (error) {
        publicKeyResult.value = JSON.stringify({
          success: false,
          error: error.message
        }, null, 2)
      }
    }

    // 测试加密登录
    const testEncryptedLogin = async () => {
      try {
        loginResult.value = '正在进行加密登录...'

        const startTime = Date.now()
        const result = await request.encryptedPost('/api/auth/login', loginForm.value)
        const endTime = Date.now()

        loginResult.value = JSON.stringify({
          success: true,
          message: '加密登录成功',
          duration: `${endTime - startTime}ms`,
          data: result
        }, null, 2)
      } catch (error) {
        loginResult.value = JSON.stringify({
          success: false,
          error: error.message,
          response: error.response?.data
        }, null, 2)
      }
    }

    // 测试明文登录
    const testPlaintextLogin = async () => {
      try {
        loginResult.value = '正在进行明文登录...'

        const startTime = Date.now()
        const result = await request.plaintextPost('/api/auth/login', loginForm.value)
        const endTime = Date.now()

        loginResult.value = JSON.stringify({
          success: true,
          message: '明文登录成功',
          duration: `${endTime - startTime}ms`,
          data: result
        }, null, 2)
      } catch (error) {
        loginResult.value = JSON.stringify({
          success: false,
          error: error.message,
          response: error.response?.data
        }, null, 2)
      }
    }

    // 测试加密注册
    const testEncryptedRegister = async () => {
      try {
        registerResult.value = '正在进行加密注册...'

        const startTime = Date.now()
        const result = await request.encryptedPost('/api/auth/register', registerForm.value)
        const endTime = Date.now()

        registerResult.value = JSON.stringify({
          success: true,
          message: '加密注册成功',
          duration: `${endTime - startTime}ms`,
          data: result
        }, null, 2)
      } catch (error) {
        registerResult.value = JSON.stringify({
          success: false,
          error: error.message,
          response: error.response?.data
        }, null, 2)
      }
    }

    // 测试明文注册
    const testPlaintextRegister = async () => {
      try {
        registerResult.value = '正在进行明文注册...'

        const startTime = Date.now()
        const result = await request.plaintextPost('/api/auth/register', registerForm.value)
        const endTime = Date.now()

        registerResult.value = JSON.stringify({
          success: true,
          message: '明文注册成功',
          duration: `${endTime - startTime}ms`,
          data: result
        }, null, 2)
      } catch (error) {
        registerResult.value = JSON.stringify({
          success: false,
          error: error.message,
          response: error.response?.data
        }, null, 2)
      }
    }

    // 获取加密统计
    const getEncryptionStats = async () => {
      try {
        const response = await request.get('/api/auth/encryption/stats')

        encryptionStats.value = JSON.stringify({
          frontend: {
            publicKeyId: encryptionStore.publicKeyId,
            sessionKey: encryptionStore.sessionKey ? '已生成' : '未生成',
            keyExpiresAt: encryptionStore.keyExpiresAt,
            isInitialized: encryptionStore.isInitialized,
            isKeyExpired: encryptionStore.isKeyExpired
          },
          backend: response.data
        }, null, 2)
      } catch (error) {
        encryptionStats.value = JSON.stringify({
          success: false,
          error: error.message
        }, null, 2)
      }
    }

    // 刷新加密密钥
    const refreshEncryptionKeys = async () => {
      try {
        await encryptionStore.refreshKeys()
        encryptionStats.value = '密钥已刷新'
      } catch (error) {
        encryptionStats.value = JSON.stringify({
          success: false,
          error: error.message
        }, null, 2)
      }
    }

    // 性能测试
    const runPerformanceTest = async () => {
      performanceTestRunning.value = true
      performanceResult.value = '正在运行性能测试...'

      try {
        const testData = {
          username: 'perftest',
          password: 'password123',
          test: 'performance'
        }

        const results = {
          encryptedRequests: [],
          plaintextRequests: [],
          encryptionOverhead: 0,
          averageEncryptedTime: 0,
          averagePlaintextTime: 0
        }

        // 测试加密请求（10次）
        for (let i = 0; i < 10; i++) {
          const startTime = Date.now()
          try {
            await encryptionStore.encryptRequest(testData)
            const endTime = Date.now()
            results.encryptedRequests.push(endTime - startTime)
          } catch (error) {
            results.encryptedRequests.push(-1)
          }
        }

        // 测试明文请求（10次）
        for (let i = 0; i < 10; i++) {
          const startTime = Date.now()
          JSON.stringify(testData) // 模拟明文处理
          const endTime = Date.now()
          results.plaintextRequests.push(endTime - startTime)
        }

        // 计算平均时间
        results.averageEncryptedTime = results.encryptedRequests
          .filter(t => t > 0)
          .reduce((a, b) => a + b, 0) / results.encryptedRequests.filter(t => t > 0).length

        results.averagePlaintextTime = results.plaintextRequests
          .reduce((a, b) => a + b, 0) / results.plaintextRequests.length

        results.encryptionOverhead = results.averageEncryptedTime - results.averagePlaintextTime

        performanceResult.value = JSON.stringify(results, null, 2)

      } catch (error) {
        performanceResult.value = JSON.stringify({
          success: false,
          error: error.message
        }, null, 2)
      } finally {
        performanceTestRunning.value = false
      }
    }

    // 初始化
    onMounted(() => {
      refreshCaptcha()
    })

    return {
      // 数据
      publicKeyResult,
      loginResult,
      registerResult,
      encryptionStats,
      performanceResult,
      performanceTestRunning,
      captchaUrl,
      loginForm,
      registerForm,

      // 方法
      testGetPublicKey,
      testEncryptedLogin,
      testPlaintextLogin,
      testEncryptedRegister,
      testPlaintextRegister,
      getEncryptionStats,
      refreshEncryptionKeys,
      runPerformanceTest,
      refreshCaptcha
    }
  }
}
</script>

<style scoped>
.encryption-test {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.test-header {
  text-align: center;
  margin-bottom: 30px;
}

.test-header h1 {
  color: #409eff;
  margin-bottom: 10px;
}

.test-sections {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.test-section {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 20px;
  background: #fff;
}

.test-section h2 {
  color: #303133;
  margin-bottom: 15px;
  border-bottom: 2px solid #409eff;
  padding-bottom: 5px;
}

.test-form {
  margin-bottom: 20px;
}

.captcha-container {
  display: flex;
  align-items: center;
  gap: 10px;
}

.captcha-image {
  height: 40px;
  cursor: pointer;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}

.test-controls {
  margin-bottom: 20px;
}

.test-controls .el-button {
  margin-right: 10px;
}

.test-result {
  background: #f5f7fa;
  border-radius: 4px;
  padding: 15px;
}

.test-result h3 {
  margin-top: 0;
  margin-bottom: 10px;
  color: #606266;
}

.test-result pre {
  background: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 10px;
  margin: 0;
  overflow-x: auto;
  font-size: 12px;
  line-height: 1.4;
}
</style>
