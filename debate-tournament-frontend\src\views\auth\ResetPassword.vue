<template>
  <div class="auth-container">
    <div class="auth-card">
      <div class="auth-header">
        <h1 class="auth-title">重置密码</h1>
        <p class="auth-subtitle">请设置您的新密码</p>
      </div>

      <div class="auth-body">
        <div v-if="errors.general" class="auth-alert auth-alert--error">
          {{ errors.general }}
        </div>

        <div v-if="success" class="auth-alert auth-alert--success">
          {{ success }}
        </div>

        <form v-if="!success" class="auth-form" @submit.prevent="handleSubmit">
          <!-- 新密码输入 -->
          <div class="form-group">
            <label class="form-label" for="password">新密码</label>
            <div class="password-input-container">
              <input id="password" v-model="formData.password" :type="showPassword.password ? 'text' : 'password'"
                class="form-control" :class="{ 'is-invalid': errors.password }" placeholder="请输入新密码">
              <button type="button" class="password-toggle-btn" @click="togglePasswordVisibility('password')">
                {{ showPassword.password ? '隐藏' : '显示' }}
              </button>
            </div>
            <span v-if="errors.password" class="form-text text-error">{{ errors.password }}</span>
          </div>

          <!-- 确认密码输入 -->
          <div class="form-group">
            <label class="form-label" for="confirmPassword">确认密码</label>
            <div class="password-input-container">
              <input id="confirmPassword" v-model="formData.confirmPassword"
                :type="showPassword.confirmPassword ? 'text' : 'password'" class="form-control"
                :class="{ 'is-invalid': errors.confirmPassword }" placeholder="请再次输入新密码">
              <button type="button" class="password-toggle-btn" @click="togglePasswordVisibility('confirmPassword')">
                {{ showPassword.confirmPassword ? '隐藏' : '显示' }}
              </button>
            </div>
            <span v-if="errors.confirmPassword" class="form-text text-error">{{ errors.confirmPassword }}</span>
          </div>

          <!-- 提交按钮 -->
          <button type="submit" class="btn btn--primary btn--block" :disabled="loading">
            {{ loading ? '提交中...' : '重置密码' }}
          </button>
        </form>

        <div v-if="success" class="auth-success-actions">
          <button class="btn btn--primary btn--block" @click="goToLogin">
            返回登录
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ResetPasswordJS from './js/ResetPassword.js';
export default ResetPasswordJS;
</script>

<style lang="scss">
@use './scss/Auth.scss';
</style>
