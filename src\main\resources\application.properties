# Spring Boot 应用配置
spring.application.name=ai-debate-tournament-platform

# 允许循环依赖
spring.main.allow-circular-references=true

# 数据库配置
spring.datasource.url=***********************************************************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=xiaoxiao123
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.maximum-pool-size=20
spring.datasource.hikari.auto-commit=true
spring.datasource.hikari.idle-timeout=30000
spring.datasource.hikari.pool-name=DatebookHikariCP
spring.datasource.hikari.max-lifetime=1800000
spring.datasource.hikari.connection-timeout=30000
spring.datasource.hikari.connection-test-query=SELECT 1

# JPA配置
spring.jpa.hibernate.ddl-auto=update
spring.jpa.hibernate.naming.physical-strategy=org.hibernate.boot.model.naming.CamelCaseToUnderscoresNamingStrategy
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQLDialect
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.open-in-view=false

# Redis配置
spring.data.redis.host=localhost
spring.data.redis.port=6379
spring.data.redis.password=
spring.data.redis.database=0
spring.data.redis.timeout=6000ms
spring.data.redis.lettuce.pool.max-active=8
spring.data.redis.lettuce.pool.max-wait=-1ms
spring.data.redis.lettuce.pool.max-idle=8
spring.data.redis.lettuce.pool.min-idle=0

# 缓存配置
spring.cache.type=redis
spring.cache.redis.time-to-live=600000

# 邮件配置
spring.mail.host=smtp.qq.com
spring.mail.port=587
spring.mail.username=<EMAIL>
spring.mail.password=your-auth-code
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true
spring.mail.properties.mail.smtp.ssl.trust=smtp.qq.com

# 文件上传配置
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB

# Jackson配置
spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
spring.jackson.time-zone=GMT+8

# 服务器配置
server.port=8080
server.servlet.context-path=/api
server.servlet.encoding.charset=UTF-8
server.servlet.encoding.enabled=true
server.servlet.encoding.force=true

# 日志配置
logging.level.com.debate_ournament=DEBUG
logging.level.org.springframework.security=DEBUG
logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE
logging.pattern.console=%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}

# 应用自定义配置
# JWT配置
app.jwt.secret=mySecretKey123456789012345678901234567890
app.jwt.expiration=********
app.jwt.refresh-expiration=*********

# 验证码配置
app.captcha.width=120
app.captcha.height=40
app.captcha.length=4
app.captcha.expiration=300

# 邮箱验证配置
app.email.verification.expiration=1440
app.email.verification.resend-interval=60

# 密码重置配置
app.password.reset.expiration=60
app.password.reset.max-attempts=3

# 账号锁定配置
app.account.lock.max-attempts=5
app.account.lock.lock-duration=30

# 文件上传配置
app.upload.path=uploads
app.upload.avatar.max-size=2097152
app.upload.avatar.allowed-types=jpg,jpeg,png,gif,webp

# 文件清理配置
app.cleanup.orphaned-files.enabled=true
app.cleanup.orphaned-files.max-age-days=7

# CORS配置
app.cors.allowed-origins=http://localhost:3000,http://localhost:5173
app.cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS
app.cors.allowed-headers=*
app.cors.allow-credentials=true
app.cors.max-age=3600
