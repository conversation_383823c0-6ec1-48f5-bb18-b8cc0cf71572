package com.debate_ournament.ai.service;

import java.io.File;
import java.util.List;
import java.util.concurrent.CompletableFuture;

import com.debate_ournament.ai.entity.AiConfig;

/**
 * 文本转语音服务接口
 * 支持多种TTS提供商
 */
public interface TextToSpeechService {

    /**
     * 语音合成回调接口
     */
    interface SynthesisCallback {
        void onProgress(int progress);
        void onSuccess(String audioUrl, Integer duration, String format);
        void onError(String error);
    }

    /**
     * 语音配置类
     */
    class VoiceConfig {
        private String voice = "default";      // 语音类型
        private String language = "zh-CN";     // 语言
        private Double speed = 1.0;            // 语速
        private Double pitch = 1.0;            // 音调
        private String format = "mp3";         // 输出格式
        private Integer sampleRate = 22050;    // 采样率

        // Getters and Setters
        public String getVoice() { return voice; }
        public void setVoice(String voice) { this.voice = voice; }

        public String getLanguage() { return language; }
        public void setLanguage(String language) { this.language = language; }

        public Double getSpeed() { return speed; }
        public void setSpeed(Double speed) { this.speed = speed; }

        public Double getPitch() { return pitch; }
        public void setPitch(Double pitch) { this.pitch = pitch; }

        public String getFormat() { return format; }
        public void setFormat(String format) { this.format = format; }

        public Integer getSampleRate() { return sampleRate; }
        public void setSampleRate(Integer sampleRate) { this.sampleRate = sampleRate; }
    }

    /**
     * 同步文本转语音
     *
     * @param text 要转换的文本
     * @param voiceConfig 语音配置
     * @param aiConfig AI配置
     * @return 音频文件路径
     */
    String synthesizeText(String text, VoiceConfig voiceConfig, AiConfig aiConfig);

    /**
     * 异步文本转语音
     *
     * @param text 要转换的文本
     * @param voiceConfig 语音配置
     * @param aiConfig AI配置
     * @param callback 回调接口
     */
    void synthesizeTextAsync(String text, VoiceConfig voiceConfig, AiConfig aiConfig, SynthesisCallback callback);

    /**
     * 批量文本转语音
     *
     * @param texts 文本列表
     * @param voiceConfig 语音配置
     * @param aiConfig AI配置
     * @return 异步任务
     */
    CompletableFuture<List<String>> synthesizeTexts(List<String> texts, VoiceConfig voiceConfig, AiConfig aiConfig);

    /**
     * 获取支持的语音列表
     *
     * @param aiConfig AI配置
     * @return 语音列表
     */
    List<String> getSupportedVoices(AiConfig aiConfig);

    /**
     * 获取支持的语言列表
     *
     * @param aiConfig AI配置
     * @return 语言列表
     */
    List<String> getSupportedLanguages(AiConfig aiConfig);

    /**
     * 获取支持的音频格式
     *
     * @param aiConfig AI配置
     * @return 格式列表
     */
    List<String> getSupportedFormats(AiConfig aiConfig);

    /**
     * 估算音频时长
     *
     * @param text 文本内容
     * @param voiceConfig 语音配置
     * @return 预估时长(秒)
     */
    Integer estimateAudioDuration(String text, VoiceConfig voiceConfig);

    /**
     * 获取音频文件信息
     *
     * @param audioFile 音频文件
     * @return 音频信息
     */
    AudioInfo getAudioInfo(File audioFile);

    /**
     * 音频信息类
     */
    class AudioInfo {
        private Integer duration;    // 时长(秒)
        private Long fileSize;       // 文件大小
        private String format;       // 格式
        private Integer sampleRate;  // 采样率
        private Integer bitRate;     // 比特率

        // Getters and Setters
        public Integer getDuration() { return duration; }
        public void setDuration(Integer duration) { this.duration = duration; }

        public Long getFileSize() { return fileSize; }
        public void setFileSize(Long fileSize) { this.fileSize = fileSize; }

        public String getFormat() { return format; }
        public void setFormat(String format) { this.format = format; }

        public Integer getSampleRate() { return sampleRate; }
        public void setSampleRate(Integer sampleRate) { this.sampleRate = sampleRate; }

        public Integer getBitRate() { return bitRate; }
        public void setBitRate(Integer bitRate) { this.bitRate = bitRate; }
    }

    /**
     * 测试TTS服务连接
     *
     * @param aiConfig AI配置
     * @return 是否连接成功
     */
    boolean testConnection(AiConfig aiConfig);
}
