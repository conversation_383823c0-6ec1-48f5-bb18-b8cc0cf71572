package com.debate_ournament.ai.service;

import java.util.List;
import java.util.Map;

import com.debate_ournament.ai.entity.AiConfig;
import com.debate_ournament.ai.entity.ChatMessage;

/**
 * AI客户端接口
 * 定义了各个AI平台的通用接口方法
 */
public interface AiClient {

    /**
     * 发送聊天消息
     *
     * @param messages AI聊天消息列表
     * @param config AI配置
     * @return AI回复内容
     * @throws Exception 调用异常
     */
    String sendChatMessage(List<ChatMessage> messages, AiConfig config) throws Exception;

    /**
     * 流式发送聊天消息
     *
     * @param messages AI聊天消息列表
     * @param config AI配置
     * @param callback 流式回调接口
     * @throws Exception 调用异常
     */
    void sendStreamChatMessage(List<ChatMessage> messages, AiConfig config, StreamCallback callback) throws Exception;

    /**
     * 测试连接
     *
     * @param config AI配置
     * @return 是否连接成功
     */
    boolean testConnection(AiConfig config);

    /**
     * 获取支持的模型列表
     *
     * @param config AI配置
     * @return 模型列表
     */
    List<String> getSupportedModels(AiConfig config);

    /**
     * 估算Token数量
     *
     * @param text 文本内容
     * @return Token数量估算
     */
    int estimateTokenCount(String text);

    /**
     * 获取客户端名称
     *
     * @return 客户端名称
     */
    String getClientName();

    /**
     * 流式回调接口
     */
    interface StreamCallback {
        /**
         * 接收流式数据
         *
         * @param chunk 数据块
         */
        void onData(String chunk);

        /**
         * 流式完成
         */
        void onComplete();

        /**
         * 发生错误
         *
         * @param error 错误信息
         */
        void onError(String error);
    }

    /**
     * AI响应结果
     */
    class AiResponse {
        private String content;
        private int tokenCount;
        private long responseTime;
        private String modelUsed;
        private Map<String, Object> metadata;

        public AiResponse() {}

        public AiResponse(String content, int tokenCount, long responseTime) {
            this.content = content;
            this.tokenCount = tokenCount;
            this.responseTime = responseTime;
        }

        // Getters and Setters
        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }

        public int getTokenCount() {
            return tokenCount;
        }

        public void setTokenCount(int tokenCount) {
            this.tokenCount = tokenCount;
        }

        public long getResponseTime() {
            return responseTime;
        }

        public void setResponseTime(long responseTime) {
            this.responseTime = responseTime;
        }

        public String getModelUsed() {
            return modelUsed;
        }

        public void setModelUsed(String modelUsed) {
            this.modelUsed = modelUsed;
        }

        public Map<String, Object> getMetadata() {
            return metadata;
        }

        public void setMetadata(Map<String, Object> metadata) {
            this.metadata = metadata;
        }
    }
}
