package com.debate_ournament.users.service;

import java.time.LocalDateTime;
import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.debate_ournament.config.AppConfig;
import com.debate_ournament.users.entity.User;
import com.debate_ournament.users.entity.UserLevel;
import com.debate_ournament.users.entity.UserProfile;
import com.debate_ournament.users.repository.UserLevelRepository;
import com.debate_ournament.users.repository.UserProfileRepository;
import com.debate_ournament.users.repository.UserRepository;

/**
 * 用户业务服务类
 */
@Service
@Transactional
public class UserService {

    private static final Logger logger = LoggerFactory.getLogger(UserService.class);

    private final UserRepository userRepository;
    private final UserLevelRepository userLevelRepository;
    private final UserProfileRepository userProfileRepository;
    private final PasswordEncoder passwordEncoder;
    private final AppConfig appConfig;

    @Autowired
    public UserService(UserRepository userRepository,
                      UserLevelRepository userLevelRepository,
                      UserProfileRepository userProfileRepository,
                      PasswordEncoder passwordEncoder,
                      AppConfig appConfig) {
        this.userRepository = userRepository;
        this.userLevelRepository = userLevelRepository;
        this.userProfileRepository = userProfileRepository;
        this.passwordEncoder = passwordEncoder;
        this.appConfig = appConfig;
    }

    /**
     * 创建新用户
     */
    public User createUser(String username, String email, String rawPassword) {
        logger.info("创建新用户: username={}, email={}", username, email);

        // 检查用户名和邮箱是否已存在
        if (userRepository.existsByUsername(username)) {
            throw new IllegalArgumentException("用户名已存在");
        }
        if (userRepository.existsByEmail(email)) {
            throw new IllegalArgumentException("邮箱已存在");
        }

        // 创建用户实体
        User user = new User();
        user.setUsername(username);
        user.setEmail(email);
        user.setPassword(passwordEncoder.encode(rawPassword));
        user.setEmailVerified(false);
        user.setStatus(User.UserStatus.ACTIVE);

        User savedUser = userRepository.save(user);

        // 自动创建用户等级记录
        try {
            UserLevel userLevel = new UserLevel(savedUser);
            userLevelRepository.save(userLevel);
            logger.info("用户等级记录创建成功: userId={}", savedUser.getId());
        } catch (Exception e) {
            logger.error("创建用户等级记录失败: userId={}, error={}", savedUser.getId(), e.getMessage());
        }

        // 自动创建用户资料记录
        try {
            UserProfile userProfile = new UserProfile(savedUser);
            userProfileRepository.save(userProfile);
            logger.info("用户资料记录创建成功: userId={}", savedUser.getId());
        } catch (Exception e) {
            logger.error("创建用户资料记录失败: userId={}, error={}", savedUser.getId(), e.getMessage());
        }

        logger.info("用户创建成功: userId={}", savedUser.getId());
        return savedUser;
    }

    /**
     * 根据用户名或邮箱查找用户
     */
    @Transactional(readOnly = true)
    public Optional<User> findByUsernameOrEmail(String usernameOrEmail) {
        return userRepository.findByUsernameOrEmail(usernameOrEmail);
    }

    /**
     * 根据用户名查找用户
     */
    @Transactional(readOnly = true)
    public Optional<User> findByUsername(String username) {
        return userRepository.findByUsername(username);
    }

    /**
     * 根据ID查找用户
     */
    @Transactional(readOnly = true)
    public Optional<User> findById(Long userId) {
        return userRepository.findById(userId);
    }

    /**
     * 根据邮箱查找用户
     */
    @Transactional(readOnly = true)
    public Optional<User> findByEmail(String email) {
        return userRepository.findByEmail(email);
    }

    /**
     * 验证密码
     */
    @Transactional(readOnly = true)
    public boolean verifyPassword(User user, String rawPassword) {
        return passwordEncoder.matches(rawPassword, user.getPassword());
    }

    /**
     * 处理登录成功
     */
    public void handleLoginSuccess(User user, String clientIp) {
        logger.info("用户登录成功: userId={}, ip={}", user.getId(), clientIp);

        user.resetLoginAttempts();
        user.updateLastLogin(clientIp);
        user.setStatus(User.UserStatus.ACTIVE);

        userRepository.save(user);
    }

    /**
     * 处理登录失败
     */
    public void handleLoginFailure(User user) {
        logger.warn("用户登录失败: userId={}, attempts={}", user.getId(), user.getLoginAttempts() + 1);

        user.incrementLoginAttempts();

        int maxAttempts = appConfig.getAccount().getLock().getMaxAttempts();
        int lockDuration = appConfig.getAccount().getLock().getLockDuration();

        if (user.getLoginAttempts() >= maxAttempts) {
            user.lockAccount(lockDuration);
            logger.warn("用户账号被锁定: userId={}, lockUntil={}", user.getId(), user.getLockedUntil());
        }

        userRepository.save(user);
    }

    /**
     * 更新密码
     */
    public void updatePassword(User user, String newRawPassword) {
        logger.info("更新用户密码: userId={}", user.getId());

        user.setPassword(passwordEncoder.encode(newRawPassword));
        user.resetLoginAttempts(); // 重置登录尝试次数

        userRepository.save(user);
    }

    /**
     * 激活用户邮箱
     */
    public void activateUserEmail(User user) {
        logger.info("激活用户邮箱: userId={}", user.getId());

        user.setEmailVerified(true);
        if (user.getStatus() == User.UserStatus.INACTIVE) {
            user.setStatus(User.UserStatus.ACTIVE);
        }

        userRepository.save(user);
    }

    /**
     * 锁定用户账号
     */
    public void lockUser(User user, int lockDurationMinutes) {
        logger.warn("锁定用户账号: userId={}, duration={}分钟", user.getId(), lockDurationMinutes);

        user.lockAccount(lockDurationMinutes);
        userRepository.save(user);
    }

    /**
     * 解锁用户账号
     */
    public void unlockUser(User user) {
        logger.info("解锁用户账号: userId={}", user.getId());

        user.resetLoginAttempts();
        user.setStatus(User.UserStatus.ACTIVE);
        user.setLockedUntil(null);

        userRepository.save(user);
    }

    /**
     * 禁用用户账号
     */
    public void disableUser(User user) {
        logger.warn("禁用用户账号: userId={}", user.getId());

        user.setStatus(User.UserStatus.BANNED);
        userRepository.save(user);
    }

    /**
     * 启用用户账号
     */
    public void enableUser(User user) {
        logger.info("启用用户账号: userId={}", user.getId());

        user.setStatus(User.UserStatus.ACTIVE);
        user.resetLoginAttempts();

        userRepository.save(user);
    }

    /**
     * 检查用户账号是否可用
     */
    @Transactional(readOnly = true)
    public boolean isUserAccountUsable(User user) {
        return user.isActive() && user.isAccountNonLocked();
    }

    /**
     * 更新用户信息
     */
    public User updateUser(User user) {
        logger.info("更新用户信息: userId={}", user.getId());
        return userRepository.save(user);
    }

    /**
     * 删除用户
     */
    public void deleteUser(Long userId) {
        logger.warn("删除用户: userId={}", userId);
        userRepository.deleteById(userId);
    }

    /**
     * 检查用户名是否存在
     */
    @Transactional(readOnly = true)
    public boolean existsByUsername(String username) {
        return userRepository.existsByUsername(username);
    }

    /**
     * 检查邮箱是否存在
     */
    @Transactional(readOnly = true)
    public boolean existsByEmail(String email) {
        return userRepository.existsByEmail(email);
    }

    /**
     * 解锁过期的用户账号
     */
    @Transactional
    public int unlockExpiredAccounts() {
        logger.info("执行过期账号解锁任务");
        return userRepository.unlockExpiredAccounts(User.UserStatus.ACTIVE, LocalDateTime.now());
    }

    /**
     * 重置长时间未尝试登录的用户登录失败计数
     */
    @Transactional
    public int resetOldLoginAttempts() {
        logger.info("重置旧的登录尝试记录");
        LocalDateTime cutoffTime = LocalDateTime.now().minusHours(24);
        return userRepository.resetLoginAttempts(cutoffTime);
    }

    /**
     * 删除长期未验证的用户账号
     */
    @Transactional
    public int deleteUnverifiedUsers() {
        logger.info("删除长期未验证的用户账号");
        LocalDateTime cutoffTime = LocalDateTime.now().minusDays(7);
        return userRepository.deleteUnverifiedUsers(cutoffTime);
    }
}
