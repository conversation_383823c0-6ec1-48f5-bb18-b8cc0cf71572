@use '@/styles/variables' as v;
@use '@/styles/mixins' as m;

.app-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 60px;
    background-color: white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    z-index: 100;

    &__content {
        @include m.flex-between;
        height: 100%;
        padding: 0 v.$spacing-md;
        max-width: 1200px;
        margin: 0 auto;
    }

    &__logo {
        display: flex;
        align-items: center;

        .logo {
            display: flex;
            align-items: center;
            text-decoration: none;

            &__text {
                font-size: 1.5rem;
                font-weight: 700;
                color: v.$color-primary;
                margin-left: v.$spacing-xs;
            }
        }
    }

    &__nav {
        display: none;

        @include m.respond-to(md) {
            display: block;
        }

        .nav {
            display: flex;
            list-style: none;
            margin: 0;
            padding: 0;

            &__item {
                margin-left: v.$spacing-lg;

                &:first-child {
                    margin-left: 0;
                }
            }

            &__link {
                position: relative;
                display: inline-block;
                padding: v.$spacing-xs v.$spacing-sm;
                color: v.$color-on-surface;
                font-weight: 500;
                text-decoration: none;
                transition: color 0.2s ease;

                &:hover,
                &.active {
                    color: v.$color-primary;
                }

                &.active::after {
                    content: '';
                    position: absolute;
                    left: 0;
                    right: 0;
                    bottom: -2px;
                    height: 2px;
                    background-color: v.$color-primary;
                }
            }
        }
    }

    &__user {
        display: flex;
        align-items: center;
    }
}

.user-menu {
    position: relative;
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: v.$spacing-xs v.$spacing-sm;
    border-radius: v.$shape-corner-medium;
    transition: background-color 0.2s ease;

    &:hover {
        background-color: #f5f5f5;
    }

    .user-avatar {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        overflow: hidden;

        img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
    }

    .user-name {
        margin: 0 v.$spacing-xs;
        font-weight: 500;
        display: none;

        @include m.respond-to(md) {
            display: inline;
        }
    }

    .dropdown-icon {
        font-size: 0.75rem;
        color: v.$color-secondary;
    }

    .dropdown-menu {
        position: absolute;
        top: 100%;
        right: 0;
        min-width: 200px;
        margin-top: v.$spacing-xs;
        background-color: white;
        border-radius: v.$shape-corner-small;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        overflow: hidden;
        z-index: 10;

        .dropdown-item {
            display: block;
            padding: v.$spacing-sm v.$spacing-md;
            color: v.$color-on-surface;
            text-decoration: none;
            transition: background-color 0.2s ease;

            &:hover {
                background-color: #f5f5f5;
            }
        }

        .dropdown-divider {
            height: 1px;
            background-color: #eee;
            margin: v.$spacing-xs 0;
        }
    }
}

.auth-buttons {
    display: flex;
    gap: v.$spacing-sm;

    .btn {
        padding: v.$spacing-xs v.$spacing-md;

        @include m.respond-to(sm) {
            padding: v.$spacing-xs v.$spacing-sm;
        }
    }
}

// 移动端菜单按钮
.mobile-menu-toggle {
    display: block;
    width: 40px;
    height: 40px;
    padding: v.$spacing-xs;
    background: none;
    border: none;
    cursor: pointer;

    @include m.respond-to(md) {
        display: none;
    }

    &__icon {
        position: relative;
        display: block;
        width: 100%;
        height: 2px;
        background-color: v.$color-on-surface;
        transition: all 0.3s ease;

        &::before,
        &::after {
            content: '';
            position: absolute;
            left: 0;
            width: 100%;
            height: 2px;
            background-color: v.$color-on-surface;
            transition: all 0.3s ease;
        }

        &::before {
            top: -8px;
        }

        &::after {
            bottom: -8px;
        }
    }

    &.active {
        .mobile-menu-toggle__icon {
            background-color: transparent;

            &::before {
                top: 0;
                transform: rotate(45deg);
            }

            &::after {
                bottom: 0;
                transform: rotate(-45deg);
            }
        }
    }
}

// 移动端导航菜单
.mobile-nav {
    display: flex;
    position: fixed;
    top: 60px;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: white;
    transform: translateY(-100%);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 99;

    &.active {
        transform: translateY(0);
        opacity: 1;
        visibility: visible;
    }

    &__content {
        width: 100%;
        max-width: 400px;
        margin: 0 auto;
        padding: v.$spacing-lg;
        overflow-y: auto;
    }

    &__list {
        list-style: none;
        margin: 0;
        padding: 0;

        &-item {
            margin-bottom: v.$spacing-md;

            a {
                display: block;
                padding: v.$spacing-sm 0;
                font-size: 1.1rem;
                font-weight: 500;
                color: v.$color-on-surface;
                text-decoration: none;

                &.active {
                    color: v.$color-primary;
                }
            }
        }
    }
}

// 响应式调整
@media (max-width: 768px) {
    .app-header {
        &__content {
            padding: 0 v.$spacing-sm;
        }
    }
}

// 空出顶部导航栏高度
body {
    padding-top: 60px;
}
