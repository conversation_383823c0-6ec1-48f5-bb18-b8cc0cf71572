<template>
  <div class="mind-map-visualization">
    <div class="mind-map-container" ref="mindMapContainer"></div>
    <div class="mind-map-loading" v-if="loading">
      <el-icon class="is-loading"><Loading /></el-icon>
      <span>加载中...</span>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onBeforeUnmount, watch } from 'vue';
import * as go from 'gojs';

export default {
  name: 'MindMapVisualization',

  props: {
    debateTopic: {
      type: String,
      required: true
    }
  },

  setup(props) {
    const mindMapContainer = ref(null);
    const loading = ref(true);

    let diagram = null;

    // 初始化思维导图
    const initMindMap = () => {
      // 创建GoJS图表
      const $ = go.GraphObject.make;

      diagram = $(go.Diagram, mindMapContainer.value, {
        initialContentAlignment: go.Spot.Center,
        "undoManager.isEnabled": true,
        layout: $(go.TreeLayout, {
          angle: 90,
          nodeSpacing: 20,
          layerSpacing: 50
        }),
        "animationManager.isEnabled": true
      });

      // 节点模板
      diagram.nodeTemplate = $(
        go.Node, "Auto",
        {
          selectionAdorned: false,
          fromSpot: go.Spot.AllSides,
          toSpot: go.Spot.AllSides
        },
        $(
          go.Shape, "RoundedRectangle",
          {
            fill: "white",
            stroke: "#CCCCCC",
            strokeWidth: 1,
            shadowVisible: true
          },
          new go.Binding("fill", "color")
        ),
        $(
          go.Panel, "Vertical",
          { margin: 8 },
          $(
            go.TextBlock,
            {
              margin: new go.Margin(0, 5),
              font: "bold 14px Arial, sans-serif",
              stroke: "#333333",
              alignment: go.Spot.Center,
              wrap: go.TextBlock.WrapFit,
              width: 160
            },
            new go.Binding("text", "text")
          ),
          $(
            go.TextBlock,
            {
              margin: new go.Margin(0, 5),
              font: "12px Arial, sans-serif",
              stroke: "#666666",
              alignment: go.Spot.Center,
              wrap: go.TextBlock.WrapFit,
              width: 160
            },
            new go.Binding("text", "subText")
          )
        )
      );

      // 连线模板
      diagram.linkTemplate = $(
        go.Link,
        {
          routing: go.Link.AvoidsNodes,
          corner: 10,
          curve: go.Link.JumpOver
        },
        $(
          go.Shape,
          { strokeWidth: 1.5 },
          new go.Binding("stroke", "color")
        ),
        $(
          go.Shape,
          { toArrow: "Standard", stroke: null },
          new go.Binding("fill", "color")
        )
      );

      // 生成示例数据
      generateMindMapData(props.debateTopic);

      // 加载完成
      loading.value = false;
    };

    // 生成思维导图数据
    const generateMindMapData = (topic) => {
      if (!diagram) return;

      const nodeDataArray = [
        // 中心主题
        { key: 1, text: topic, color: "#EFEFEF" },

        // 支持论点
        { key: 2, text: "支持论点", color: "#D5F5E3", category: "support" },
        { key: 3, text: "AI具备理性思考能力", subText: "基于大量数据和逻辑分析", color: "#D5F5E3", category: "support" },
        { key: 4, text: "AI可以提供客观公正的判断", subText: "不受个人情感和利益影响", color: "#D5F5E3", category: "support" },
        { key: 5, text: "AI代表多元化的观点", subText: "基于全球数据训练", color: "#D5F5E3", category: "support" },

        // 反对论点
        { key: 6, text: "反对论点", color: "#F5B7B1", category: "oppose" },
        { key: 7, text: "缺乏道德责任感", subText: "无法理解投票的道德意义", color: "#F5B7B1", category: "oppose" },
        { key: 8, text: "可能被操控", subText: "容易受到设计者和使用者的影响", color: "#F5B7B1", category: "oppose" },
        { key: 9, text: "缺乏人类经验", subText: "无法真正理解人类社会需求", color: "#F5B7B1", category: "oppose" },

        // 支持论点的子论点
        { key: 10, text: "基于算法的决策更加一致", color: "#D5F5E3", category: "support" },
        { key: 11, text: "能处理海量信息做出决策", color: "#D5F5E3", category: "support" },

        // 反对论点的子论点
        { key: 12, text: "无法体验政策影响", color: "#F5B7B1", category: "oppose" },
        { key: 13, text: "可能导致技术垄断投票权", color: "#F5B7B1", category: "oppose" }
      ];

      const linkDataArray = [
        // 中心连接到主要论点
        { from: 1, to: 2, color: "#82E0AA" },
        { from: 1, to: 6, color: "#EC7063" },

        // 支持论点连接
        { from: 2, to: 3, color: "#82E0AA" },
        { from: 2, to: 4, color: "#82E0AA" },
        { from: 2, to: 5, color: "#82E0AA" },

        // 反对论点连接
        { from: 6, to: 7, color: "#EC7063" },
        { from: 6, to: 8, color: "#EC7063" },
        { from: 6, to: 9, color: "#EC7063" },

        // 子论点连接
        { from: 3, to: 10, color: "#82E0AA" },
        { from: 4, to: 11, color: "#82E0AA" },
        { from: 7, to: 12, color: "#EC7063" },
        { from: 8, to: 13, color: "#EC7063" }
      ];

      diagram.model = new go.GraphLinksModel(nodeDataArray, linkDataArray);
    };

    // 更新思维导图
    const updateMindMap = () => {
      generateMindMapData(props.debateTopic);
    };

    // 监听主题变化
    watch(() => props.debateTopic, (newTopic) => {
      updateMindMap();
    });

    // 处理窗口大小变化
    const handleResize = () => {
      if (diagram) {
        diagram.requestUpdate();
      }
    };

    onMounted(() => {
      // 使用setTimeout确保DOM已经渲染
      setTimeout(() => {
        if (mindMapContainer.value) {
          initMindMap();
        }
      }, 100);

      window.addEventListener('resize', handleResize);
    });

    onBeforeUnmount(() => {
      window.removeEventListener('resize', handleResize);

      // 清理GoJS资源
      if (diagram) {
        diagram.div = null;
      }
    });

    return {
      mindMapContainer,
      loading
    };
  }
};
</script>

<style lang="scss" scoped>
.mind-map-visualization {
  position: relative;
  width: 100%;
  height: 400px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

  .mind-map-container {
    width: 100%;
    height: 100%;
    background-color: #fafafa;
  }

  .mind-map-loading {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: rgba(255, 255, 255, 0.8);

    .el-icon {
      font-size: 32px;
      margin-bottom: 8px;
    }
  }
}
</style>
