package com.debate_ournament.util;

import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import com.debate_ournament.dto.common.ApiResponse;
import com.debate_ournament.users.service.EncryptionService;
import com.debate_ournament.users.service.EncryptionService.DecryptResult;

/**
 * 加密请求处理工具类
 *
 * 提供通用的加密请求处理逻辑，支持以下功能：
 * - 统一的加密请求验证和解密
 * - 统一的响应加密处理
 * - 混合请求处理（加密/明文自适应）
 * - 标准化的错误处理和日志记录
 * - 可复用的处理器接口设计
 *
 * 使用方式：
 * 1. 注入此组件到需要处理加密请求的控制器中
 * 2. 调用handleEncryptedRequest()处理纯加密请求
 * 3. 调用handleMixedRequest()处理混合请求
 *
 * <AUTHOR> Debate Tournament Platform
 * @version 2.0
 * @since 2024
 */
@Component
public class EncryptedRequestHandler {

    private static final Logger logger = LoggerFactory.getLogger(EncryptedRequestHandler.class);

    private final EncryptionService encryptionService;

    /**
     * 构造函数注入
     *
     * @param encryptionService 加密服务
     */
    public EncryptedRequestHandler(EncryptionService encryptionService) {
        this.encryptionService = encryptionService;
    }

    /**
     * 处理加密请求的通用方法
     *
     * 此方法提供完整的加密请求处理流程：
     * 1. 验证请求格式
     * 2. 解密请求数据
     * 3. 执行业务逻辑
     * 4. 加密响应数据
     * 5. 返回加密响应
     *
     * @param requestBody 加密请求体，必须包含完整的加密字段
     * @param processor 解密后的业务处理函数
     * @return 加密响应或错误响应
     */
    public ResponseEntity<?> handleEncryptedRequest(
            Map<String, Object> requestBody,
            EncryptedRequestProcessor processor) {

        if (processor == null) {
            logger.error("处理器不能为空");
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("系统配置错误"));
        }

        try {
            // 1. 输入验证
            if (requestBody == null || requestBody.isEmpty()) {
                logger.warn("收到空的加密请求体");
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("请求体不能为空"));
            }

            // 2. 验证加密格式
            if (!encryptionService.isEncryptedRequest(requestBody)) {
                logger.warn("收到未加密的请求，期望加密格式。请求键: {}",
                           requestBody.keySet());
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("请求必须使用加密格式"));
            }

            logger.debug("开始处理加密请求，请求大小: {} 字节",
                        requestBody.toString().length());

            // 3. 解密请求
            DecryptResult decryptResult;
            try {
                decryptResult = encryptionService.decryptRequestWithDetails(requestBody);
            } catch (SecurityException e) {
                logger.error("解密安全验证失败: {}", e.getMessage());
                return ResponseEntity.status(403)
                        .body(ApiResponse.error("安全验证失败"));
            } catch (IllegalArgumentException e) {
                logger.warn("解密参数错误: {}", e.getMessage());
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("请求格式错误: " + e.getMessage()));
            }

            Map<String, Object> decryptedData = decryptResult.getData();
            logger.debug("请求解密成功，keyId: {}, 解密数据大小: {} 字节",
                        decryptResult.getKeyId(),
                        decryptedData.toString().length());

            // 4. 执行业务逻辑
            Map<String, Object> responseData;
            try {
                responseData = processor.process(decryptedData);
            } catch (IllegalArgumentException e) {
                logger.warn("业务处理参数错误: {}", e.getMessage());
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("请求参数错误: " + e.getMessage()));
            } catch (SecurityException e) {
                logger.error("业务处理安全错误: {}", e.getMessage());
                return ResponseEntity.status(403)
                        .body(ApiResponse.error("权限验证失败"));
            } catch (Exception e) {
                logger.error("业务处理失败: {}", e.getMessage(), e);
                return ResponseEntity.internalServerError()
                        .body(ApiResponse.error("业务处理失败"));
            }

            // 5. 验证响应数据
            if (responseData == null) {
                logger.warn("业务处理器返回了空的响应数据");
                return ResponseEntity.internalServerError()
                        .body(ApiResponse.error("处理请求时发生错误"));
            }

            logger.debug("业务处理成功，响应数据大小: {} 字节",
                        responseData.toString().length());

            // 6. 加密响应
            Map<String, Object> encryptedResponse;
            try {
                encryptedResponse = encryptionService.encryptResponse(
                        responseData,
                        decryptResult.getKeyId(),
                        decryptResult.getSessionKey());
            } catch (Exception e) {
                logger.error("响应加密失败: {}", e.getMessage(), e);
                return ResponseEntity.internalServerError()
                        .body(ApiResponse.error("响应处理失败"));
            }

            logger.debug("响应加密成功，返回给客户端");
            return ResponseEntity.ok(encryptedResponse);

        } catch (Exception e) {
            // 捕获任何未预期的异常
            logger.error("处理加密请求时发生未知错误: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("系统繁忙，请稍后重试"));
        }
    }

    /**
     * 检查请求是否需要加密处理
     *
     * @param requestBody 请求体
     * @return 是否为加密请求
     */
    public boolean isEncryptedRequest(Map<String, Object> requestBody) {
        if (requestBody == null || requestBody.isEmpty()) {
            return false;
        }

        try {
            boolean isEncrypted = encryptionService.isEncryptedRequest(requestBody);
            logger.debug("请求加密检查结果: {}", isEncrypted);
            return isEncrypted;
        } catch (Exception e) {
            logger.warn("检查加密请求时发生错误: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 处理混合请求（支持加密和明文）
     *
     * 此方法能够自动识别请求类型并选择相应的处理方式：
     * - 如果是加密请求，使用加密处理流程
     * - 如果是明文请求，使用明文处理流程
     *
     * @param request 请求对象（可能是Map或其他类型）
     * @param encryptedProcessor 加密请求处理器
     * @param plainProcessor 明文请求处理器
     * @return 相应的响应
     */
    public ResponseEntity<?> handleMixedRequest(
            Object request,
            EncryptedRequestProcessor encryptedProcessor,
            PlainRequestProcessor plainProcessor) {

        if (request == null) {
            logger.warn("收到空的混合请求");
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("请求不能为空"));
        }

        if (encryptedProcessor == null || plainProcessor == null) {
            logger.error("处理器不能为空");
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("系统配置错误"));
        }

        try {
            // 检查是否为Map类型的请求
            if (request instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> requestMap = (Map<String, Object>) request;

                logger.debug("检测到Map类型请求，检查是否为加密格式");

                if (isEncryptedRequest(requestMap)) {
                    logger.debug("识别为加密请求，使用加密处理流程");
                    return handleEncryptedRequest(requestMap, encryptedProcessor);
                } else {
                    logger.debug("识别为明文Map请求，使用明文处理流程");
                    return plainProcessor.process(request);
                }
            } else {
                // 非Map类型，直接使用明文处理
                logger.debug("识别为非Map类型明文请求，使用明文处理流程");
                return plainProcessor.process(request);
            }

        } catch (ClassCastException e) {
            logger.warn("请求类型转换错误: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("请求格式错误"));
        } catch (Exception e) {
            logger.error("处理混合请求失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("请求处理失败，请稍后重试"));
        }
    }

    /**
     * 处理批量加密请求
     *
     * @param requestBodies 多个加密请求体
     * @param processor 批量处理器
     * @return 批量响应结果
     */
    public ResponseEntity<?> handleBatchEncryptedRequest(
            Map<String, Object>[] requestBodies,
            BatchEncryptedRequestProcessor processor) {

        if (requestBodies == null || requestBodies.length == 0) {
            logger.warn("收到空的批量加密请求");
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("批量请求不能为空"));
        }

        if (processor == null) {
            logger.error("批量处理器不能为空");
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("系统配置错误"));
        }

        logger.debug("开始处理批量加密请求，请求数量: {}", requestBodies.length);

        try {
            // 解密所有请求
            DecryptResult[] decryptResults = new DecryptResult[requestBodies.length];

            for (int i = 0; i < requestBodies.length; i++) {
                if (!encryptionService.isEncryptedRequest(requestBodies[i])) {
                    logger.warn("批量请求中第 {} 个请求未加密", i + 1);
                    return ResponseEntity.badRequest()
                            .body(ApiResponse.error("所有批量请求必须加密"));
                }

                decryptResults[i] = encryptionService.decryptRequestWithDetails(requestBodies[i]);
            }

            // 执行批量业务逻辑
            Map<String, Object>[] responseDataArray = processor.process(decryptResults);

            if (responseDataArray == null || responseDataArray.length != requestBodies.length) {
                logger.error("批量处理器返回的响应数量不匹配");
                return ResponseEntity.internalServerError()
                        .body(ApiResponse.error("批量处理失败"));
            }

            // 加密所有响应
            Map<String, Object>[] encryptedResponses = new Map[responseDataArray.length];

            for (int i = 0; i < responseDataArray.length; i++) {
                encryptedResponses[i] = encryptionService.encryptResponse(
                        responseDataArray[i],
                        decryptResults[i].getKeyId(),
                        decryptResults[i].getSessionKey());
            }

            logger.debug("批量加密请求处理成功，响应数量: {}", encryptedResponses.length);
            return ResponseEntity.ok(encryptedResponses);

        } catch (Exception e) {
            logger.error("处理批量加密请求失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("批量请求处理失败"));
        }
    }

    /**
     * 验证加密请求的完整性
     *
     * @param requestBody 请求体
     * @return 验证结果
     */
    public boolean validateEncryptedRequest(Map<String, Object> requestBody) {
        if (requestBody == null || requestBody.isEmpty()) {
            logger.debug("请求体为空，验证失败");
            return false;
        }

        try {
            // 检查基本格式
            if (!encryptionService.isEncryptedRequest(requestBody)) {
                logger.debug("请求不是加密格式，验证失败");
                return false;
            }

            // 尝试解密以验证完整性（不保存结果）
            encryptionService.decryptRequestWithDetails(requestBody);
            logger.debug("加密请求验证成功");
            return true;

        } catch (Exception e) {
            logger.debug("加密请求验证失败: {}", e.getMessage());
            return false;
        }
    }

    // ============ 处理器接口定义 ============

    /**
     * 加密请求处理器接口
     * 用于处理解密后的业务逻辑
     */
    @FunctionalInterface
    public interface EncryptedRequestProcessor {
        /**
         * 处理解密后的数据
         *
         * @param decryptedData 解密后的请求数据
         * @return 处理结果，将被加密后返回给客户端
         * @throws Exception 处理过程中的任何异常
         */
        Map<String, Object> process(Map<String, Object> decryptedData) throws Exception;
    }

    /**
     * 明文请求处理器接口
     * 用于处理明文请求的业务逻辑
     */
    @FunctionalInterface
    public interface PlainRequestProcessor {
        /**
         * 处理明文请求
         *
         * @param requestData 请求数据
         * @return 响应结果
         * @throws Exception 处理过程中的任何异常
         */
        ResponseEntity<?> process(Object requestData) throws Exception;
    }

    /**
     * 批量加密请求处理器接口
     * 用于处理批量解密后的业务逻辑
     */
    @FunctionalInterface
    public interface BatchEncryptedRequestProcessor {
        /**
         * 处理批量解密后的数据
         *
         * @param decryptResults 批量解密结果
         * @return 批量处理结果，将被逐个加密后返回
         * @throws Exception 处理过程中的任何异常
         */
        Map<String, Object>[] process(DecryptResult[] decryptResults) throws Exception;
    }
}
