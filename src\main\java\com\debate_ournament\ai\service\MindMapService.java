package com.debate_ournament.ai.service;

import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.debate_ournament.ai.entity.MindMapNode;
import com.debate_ournament.ai.entity.MindMapResponse;

/**
 * 思维导图生成服务接口
 */
public interface MindMapService {

    /**
     * 基于文本内容生成思维导图
     */
    MindMapResponse generateMindMap(String content, String sessionId, Long userId);

    /**
     * 基于对话历史生成思维导图
     */
    MindMapResponse generateMindMapFromConversation(String sessionId, Long userId);

    /**
     * 生成结构化的思维导图数据
     */
    MindMapNode generateStructuredMindMap(String content, String topic);

    /**
     * 将思维导图导出为不同格式
     */
    String exportMindMap(Long mindMapId, String format);

    /**
     * 获取用户的思维导图列表
     */
    List<MindMapResponse> getUserMindMaps(Long userId, int page, int size);

    /**
     * 删除思维导图
     */
    void deleteMindMap(Long mindMapId, Long userId);

    /**
     * 更新思维导图
     */
    MindMapResponse updateMindMap(Long mindMapId, MindMapNode updatedNode, Long userId);
}
