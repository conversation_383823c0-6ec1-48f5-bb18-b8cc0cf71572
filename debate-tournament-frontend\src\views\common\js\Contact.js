import { ref, reactive } from 'vue';
import { ElMessage } from 'element-plus';

export default {
  name: 'Contact',
  setup() {
    // 表单数据
    const contactForm = reactive({
      name: '',
      email: '',
      subject: '',
      message: '',
      type: '反馈建议'
    });

    // 表单规则
    const rules = {
      name: [
        { required: true, message: '请输入您的姓名', trigger: 'blur' },
        { min: 2, max: 30, message: '姓名长度在2到30个字符之间', trigger: 'blur' }
      ],
      email: [
        { required: true, message: '请输入您的邮箱', trigger: 'blur' },
        { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
      ],
      subject: [
        { required: true, message: '请输入主题', trigger: 'blur' },
        { min: 2, max: 100, message: '主题长度在2到100个字符之间', trigger: 'blur' }
      ],
      message: [
        { required: true, message: '请输入您的留言内容', trigger: 'blur' },
        { min: 10, max: 1000, message: '留言内容在10到1000个字符之间', trigger: 'blur' }
      ]
    };

    const contactFormRef = ref(null);
    const loading = ref(false);

    // 联系方式类型
    const contactTypes = [
      { value: '反馈建议', label: '反馈建议' },
      { value: '技术支持', label: '技术支持' },
      { value: '合作咨询', label: '合作咨询' },
      { value: '账号问题', label: '账号问题' },
      { value: '其他', label: '其他' }
    ];

    // 联系信息
    const contactInfo = ref([
      {
        icon: 'location',
        title: '地址',
        content: '中国北京市海淀区中关村科技园'
      },
      {
        icon: 'email',
        title: '电子邮箱',
        content: '<EMAIL>'
      },
      {
        icon: 'phone',
        title: '电话',
        content: '+86 10 1234 5678'
      },
      {
        icon: 'time',
        title: '工作时间',
        content: '周一至周五 9:00 - 18:00'
      }
    ]);

    // 社交媒体
    const socialMedia = ref([
      { icon: 'weixin', name: '微信', link: '#', qrcode: '/images/wechat-qr.png' },
      { icon: 'weibo', name: '微博', link: 'https://weibo.com/ai-debate' },
      { icon: 'zhihu', name: '知乎', link: 'https://zhihu.com/org/ai-debate' }
    ]);

    // 常见问题
    const commonQuestions = ref([
      { question: '如何注册账号?', link: '/faq#account' },
      { question: '忘记密码怎么办?', link: '/faq#password' },
      { question: '如何参与辩论?', link: '/faq#debate' },
      { question: '如何举报不当内容?', link: '/faq#report' }
    ]);

    // 提交表单
    const submitForm = async () => {
      if (!contactFormRef.value) return;

      await contactFormRef.value.validate(async (valid) => {
        if (!valid) return;

        loading.value = true;

        try {
          // 模拟API调用
          await new Promise(resolve => setTimeout(resolve, 1500));

          // 显示成功消息
          ElMessage({
            message: '您的信息已成功提交，我们会尽快回复您！',
            type: 'success'
          });

          // 重置表单
          resetForm();
        } catch (error) {
          ElMessage.error('提交失败，请稍后再试');
        } finally {
          loading.value = false;
        }
      });
    };

    // 重置表单
    const resetForm = () => {
      if (contactFormRef.value) {
        contactFormRef.value.resetFields();
      }
    };

    return {
      contactForm,
      rules,
      contactFormRef,
      loading,
      contactTypes,
      contactInfo,
      socialMedia,
      commonQuestions,
      submitForm,
      resetForm
    };
  }
};
