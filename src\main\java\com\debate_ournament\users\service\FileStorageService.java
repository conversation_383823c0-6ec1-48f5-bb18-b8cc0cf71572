package com.debate_ournament.users.service;

import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.UUID;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

/**
 * 文件存储服务类
 */
@Service
public class FileStorageService {

    @Value("${app.upload.dir:uploads}")
    private String uploadDir;

    @Value("${app.upload.avatar.dir:avatars}")
    private String avatarDir;

    @Value("${app.upload.max-file-size:10485760}") // 10MB
    private long maxFileSize;

    private final String[] allowedImageTypes = {"image/jpeg", "image/jpg", "image/png", "image/gif", "image/webp"};

    /**
     * 存储头像文件
     * @param file 上传的文件
     * @param userId 用户ID
     * @return 存储的文件路径
     * @throws IOException 文件操作异常
     */
    public String storeAvatarFile(MultipartFile file, Long userId) throws IOException {
        // 验证文件
        validateAvatarFile(file);

        // 创建上传目录
        Path uploadPath = createUploadDirectory();

        // 生成唯一文件名
        String filename = generateUniqueFilename(file.getOriginalFilename(), userId);

        // 存储文件
        Path targetLocation = uploadPath.resolve(filename);
        try (InputStream inputStream = file.getInputStream()) {
            Files.copy(inputStream, targetLocation, StandardCopyOption.REPLACE_EXISTING);
        }

        // 返回相对路径
        return "/" + avatarDir + "/" + filename;
    }

    /**
     * 删除头像文件
     * @param filePath 文件路径
     * @return 是否删除成功
     */
    public boolean deleteAvatarFile(String filePath) {
        if (filePath == null || filePath.isEmpty()) {
            return true;
        }

        try {
            // 从相对路径转换为绝对路径
            String filename = extractFilename(filePath);
            if (filename == null) {
                return false;
            }

            Path uploadPath = createUploadDirectory();
            Path fileToDelete = uploadPath.resolve(filename);

            return Files.deleteIfExists(fileToDelete);
        } catch (IOException e) {
            return false;
        }
    }

    /**
     * 验证头像文件
     * @param file 文件
     * @throws IllegalArgumentException 验证失败异常
     */
    private void validateAvatarFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new IllegalArgumentException("文件不能为空");
        }

        // 检查文件大小
        if (file.getSize() > maxFileSize) {
            throw new IllegalArgumentException("文件大小不能超过 " + (maxFileSize / 1024 / 1024) + "MB");
        }

        // 检查文件类型
        String contentType = file.getContentType();
        if (contentType == null || !isAllowedImageType(contentType)) {
            throw new IllegalArgumentException("不支持的文件类型，仅支持 JPEG、PNG、GIF、WebP 格式");
        }

        // 检查文件扩展名
        String filename = file.getOriginalFilename();
        if (filename == null || !hasValidImageExtension(filename)) {
            throw new IllegalArgumentException("文件扩展名无效");
        }
    }

    /**
     * 检查是否为允许的图片类型
     * @param contentType 文件类型
     * @return 是否允许
     */
    private boolean isAllowedImageType(String contentType) {
        for (String allowedType : allowedImageTypes) {
            if (allowedType.equals(contentType)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 检查文件扩展名是否有效
     * @param filename 文件名
     * @return 是否有效
     */
    private boolean hasValidImageExtension(String filename) {
        String extension = StringUtils.getFilenameExtension(filename);
        if (extension == null) {
            return false;
        }

        extension = extension.toLowerCase();
        return extension.equals("jpg") || extension.equals("jpeg") ||
               extension.equals("png") || extension.equals("gif") ||
               extension.equals("webp");
    }

    /**
     * 创建上传目录
     * @return 上传目录路径
     * @throws IOException IO异常
     */
    private Path createUploadDirectory() throws IOException {
        Path uploadPath = Paths.get(uploadDir, avatarDir);
        if (!Files.exists(uploadPath)) {
            Files.createDirectories(uploadPath);
        }
        return uploadPath;
    }

    /**
     * 生成唯一文件名
     * @param originalFilename 原始文件名
     * @param userId 用户ID
     * @return 唯一文件名
     */
    private String generateUniqueFilename(String originalFilename, Long userId) {
        String extension = StringUtils.getFilenameExtension(originalFilename);
        String uuid = UUID.randomUUID().toString();
        return "user_" + userId + "_" + uuid + "." + extension;
    }

    /**
     * 从文件路径中提取文件名
     * @param filePath 文件路径
     * @return 文件名
     */
    private String extractFilename(String filePath) {
        if (filePath == null || filePath.isEmpty()) {
            return null;
        }

        // 移除开头的斜杠和目录路径
        String path = filePath.startsWith("/") ? filePath.substring(1) : filePath;
        int lastSlashIndex = path.lastIndexOf("/");
        return lastSlashIndex >= 0 ? path.substring(lastSlashIndex + 1) : path;
    }

    /**
     * 获取文件的完整存储路径
     * @param relativePath 相对路径
     * @return 完整存储路径
     */
    public Path getFullPath(String relativePath) {
        String filename = extractFilename(relativePath);
        if (filename == null) {
            return null;
        }
        return Paths.get(uploadDir, avatarDir, filename);
    }

    /**
     * 检查文件是否存在
     * @param filePath 文件路径
     * @return 是否存在
     */
    public boolean fileExists(String filePath) {
        if (filePath == null || filePath.isEmpty()) {
            return false;
        }

        Path fullPath = getFullPath(filePath);
        return fullPath != null && Files.exists(fullPath);
    }

    /**
     * 获取文件大小
     * @param filePath 文件路径
     * @return 文件大小（字节），如果文件不存在返回-1
     */
    public long getFileSize(String filePath) {
        try {
            Path fullPath = getFullPath(filePath);
            if (fullPath != null && Files.exists(fullPath)) {
                return Files.size(fullPath);
            }
        } catch (IOException e) {
            // 忽略异常
        }
        return -1;
    }

    /**
     * 清理孤儿文件（没有关联用户的文件）
     * 这个方法应该定期执行
     */
    public void cleanupOrphanFiles() {
        // TODO: 实现清理逻辑
        // 1. 遍历上传目录中的所有文件
        // 2. 检查数据库中是否有对应的记录
        // 3. 删除没有记录的文件
    }
}
