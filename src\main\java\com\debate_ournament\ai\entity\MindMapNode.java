package com.debate_ournament.ai.entity;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * 思维导图节点实体
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MindMapNode {

    /**
     * 节点ID
     */
    private String id;

    /**
     * 节点文本内容
     */
    private String text;

    /**
     * 节点类型
     */
    private NodeType type;

    /**
     * 节点级别（深度）
     */
    private Integer level;

    /**
     * 父节点ID
     */
    private String parentId;

    /**
     * 子节点列表
     */
    private List<MindMapNode> children;

    /**
     * 节点属性
     */
    private Map<String, Object> properties;

    /**
     * 节点样式
     */
    private NodeStyle style;

    /**
     * 节点位置信息
     */
    private NodePosition position;

    public MindMapNode() {
        this.children = new ArrayList<>();
        this.properties = new HashMap<>();
    }

    public MindMapNode(String id, String text, NodeType type) {
        this();
        this.id = id;
        this.text = text;
        this.type = type;
    }

    /**
     * 添加子节点
     */
    public void addChild(MindMapNode child) {
        if (this.children == null) {
            this.children = new ArrayList<>();
        }
        child.setParentId(this.id);
        child.setLevel(this.level != null ? this.level + 1 : 1);
        this.children.add(child);
    }

    /**
     * 移除子节点
     */
    public void removeChild(String childId) {
        if (this.children != null) {
            this.children.removeIf(child -> childId.equals(child.getId()));
        }
    }

    /**
     * 获取所有叶子节点
     */
    public List<MindMapNode> getLeafNodes() {
        List<MindMapNode> leafNodes = new ArrayList<>();
        if (children == null || children.isEmpty()) {
            leafNodes.add(this);
        } else {
            for (MindMapNode child : children) {
                leafNodes.addAll(child.getLeafNodes());
            }
        }
        return leafNodes;
    }

    /**
     * 查找节点
     */
    public MindMapNode findNode(String nodeId) {
        if (this.id.equals(nodeId)) {
            return this;
        }
        if (children != null) {
            for (MindMapNode child : children) {
                MindMapNode found = child.findNode(nodeId);
                if (found != null) {
                    return found;
                }
            }
        }
        return null;
    }

    /**
     * 设置属性
     */
    public void setProperty(String key, Object value) {
        if (this.properties == null) {
            this.properties = new HashMap<>();
        }
        this.properties.put(key, value);
    }

    /**
     * 获取属性
     */
    public Object getProperty(String key) {
        return this.properties != null ? this.properties.get(key) : null;
    }

    // Getters and Setters
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public NodeType getType() {
        return type;
    }

    public void setType(NodeType type) {
        this.type = type;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public List<MindMapNode> getChildren() {
        return children;
    }

    public void setChildren(List<MindMapNode> children) {
        this.children = children;
    }

    public Map<String, Object> getProperties() {
        return properties;
    }

    public void setProperties(Map<String, Object> properties) {
        this.properties = properties;
    }

    public NodeStyle getStyle() {
        return style;
    }

    public void setStyle(NodeStyle style) {
        this.style = style;
    }

    public NodePosition getPosition() {
        return position;
    }

    public void setPosition(NodePosition position) {
        this.position = position;
    }

    /**
     * 节点类型枚举
     */
    public enum NodeType {
        ROOT("根节点"),
        TOPIC("主题节点"),
        SUBTOPIC("子主题节点"),
        CONCEPT("概念节点"),
        DETAIL("详细节点"),
        NOTE("注释节点"),
        LINK("链接节点");

        private final String description;

        NodeType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 节点样式
     */
    public static class NodeStyle {
        private String color;
        private String backgroundColor;
        private String borderColor;
        private Integer borderWidth;
        private String fontSize;
        private String fontWeight;
        private String shape;

        // Getters and Setters
        public String getColor() {
            return color;
        }

        public void setColor(String color) {
            this.color = color;
        }

        public String getBackgroundColor() {
            return backgroundColor;
        }

        public void setBackgroundColor(String backgroundColor) {
            this.backgroundColor = backgroundColor;
        }

        public String getBorderColor() {
            return borderColor;
        }

        public void setBorderColor(String borderColor) {
            this.borderColor = borderColor;
        }

        public Integer getBorderWidth() {
            return borderWidth;
        }

        public void setBorderWidth(Integer borderWidth) {
            this.borderWidth = borderWidth;
        }

        public String getFontSize() {
            return fontSize;
        }

        public void setFontSize(String fontSize) {
            this.fontSize = fontSize;
        }

        public String getFontWeight() {
            return fontWeight;
        }

        public void setFontWeight(String fontWeight) {
            this.fontWeight = fontWeight;
        }

        public String getShape() {
            return shape;
        }

        public void setShape(String shape) {
            this.shape = shape;
        }
    }

    /**
     * 节点位置
     */
    public static class NodePosition {
        private Double x;
        private Double y;
        private Double width;
        private Double height;

        public NodePosition() {}

        public NodePosition(Double x, Double y) {
            this.x = x;
            this.y = y;
        }

        // Getters and Setters
        public Double getX() {
            return x;
        }

        public void setX(Double x) {
            this.x = x;
        }

        public Double getY() {
            return y;
        }

        public void setY(Double y) {
            this.y = y;
        }

        public Double getWidth() {
            return width;
        }

        public void setWidth(Double width) {
            this.width = width;
        }

        public Double getHeight() {
            return height;
        }

        public void setHeight(Double height) {
            this.height = height;
        }
    }
}
