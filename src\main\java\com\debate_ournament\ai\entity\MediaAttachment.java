package com.debate_ournament.ai.entity;

import java.time.LocalDateTime;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;

/**
 * 媒体附件实体
 * 用于存储多模态响应的附加媒体文件
 */
@Entity
@Table(name = "media_attachments")
public class MediaAttachment {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne
    @JoinColumn(name = "multimodal_response_id", nullable = false)
    private MultiModalResponse multiModalResponse;

    @Enumerated(EnumType.STRING)
    @Column(name = "media_type", nullable = false)
    private MediaType mediaType;

    @Column(name = "file_url", nullable = false)
    private String fileUrl;

    @Column(name = "file_name")
    private String fileName;

    @Column(name = "file_size")
    private Long fileSize; // 文件大小(字节)

    @Column(name = "mime_type")
    private String mimeType;

    @Column(name = "duration")
    private Integer duration; // 音频/视频时长(秒)

    @Column(name = "width")
    private Integer width; // 图片/视频宽度

    @Column(name = "height")
    private Integer height; // 图片/视频高度

    @Column(name = "description")
    private String description;

    @Column(name = "metadata", columnDefinition = "JSON")
    private String metadata; // 额外元数据

    @Column(name = "created_at")
    private LocalDateTime createdAt;

    // 构造函数
    public MediaAttachment() {
        this.createdAt = LocalDateTime.now();
    }

    // 静态工厂方法
    public static MediaAttachment createAudioAttachment(String fileUrl, String fileName, Long fileSize, Integer duration) {
        MediaAttachment attachment = new MediaAttachment();
        attachment.setMediaType(MediaType.AUDIO);
        attachment.setFileUrl(fileUrl);
        attachment.setFileName(fileName);
        attachment.setFileSize(fileSize);
        attachment.setDuration(duration);
        return attachment;
    }

    public static MediaAttachment createImageAttachment(String fileUrl, String fileName, Long fileSize, Integer width, Integer height) {
        MediaAttachment attachment = new MediaAttachment();
        attachment.setMediaType(MediaType.IMAGE);
        attachment.setFileUrl(fileUrl);
        attachment.setFileName(fileName);
        attachment.setFileSize(fileSize);
        attachment.setWidth(width);
        attachment.setHeight(height);
        return attachment;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public MultiModalResponse getMultiModalResponse() {
        return multiModalResponse;
    }

    public void setMultiModalResponse(MultiModalResponse multiModalResponse) {
        this.multiModalResponse = multiModalResponse;
    }

    public MediaType getMediaType() {
        return mediaType;
    }

    public void setMediaType(MediaType mediaType) {
        this.mediaType = mediaType;
    }

    public String getFileUrl() {
        return fileUrl;
    }

    public void setFileUrl(String fileUrl) {
        this.fileUrl = fileUrl;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public Long getFileSize() {
        return fileSize;
    }

    public void setFileSize(Long fileSize) {
        this.fileSize = fileSize;
    }

    public String getMimeType() {
        return mimeType;
    }

    public void setMimeType(String mimeType) {
        this.mimeType = mimeType;
    }

    public Integer getDuration() {
        return duration;
    }

    public void setDuration(Integer duration) {
        this.duration = duration;
    }

    public Integer getWidth() {
        return width;
    }

    public void setWidth(Integer width) {
        this.width = width;
    }

    public Integer getHeight() {
        return height;
    }

    public void setHeight(Integer height) {
        this.height = height;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getMetadata() {
        return metadata;
    }

    public void setMetadata(String metadata) {
        this.metadata = metadata;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
}
