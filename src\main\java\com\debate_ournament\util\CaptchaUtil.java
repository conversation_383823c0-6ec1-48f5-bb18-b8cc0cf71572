package com.debate_ournament.util;

import java.awt.BasicStroke;
import java.awt.Color;
import java.awt.Font;
import java.awt.FontMetrics;
import java.awt.Graphics2D;
import java.awt.RenderingHints;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Base64;
import java.util.Random;

import javax.imageio.ImageIO;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.debate_ournament.config.AppConfig;

/**
 * 验证码工具类
 */
@Component
public class CaptchaUtil {

    private final AppConfig appConfig;
    private final Random random = new Random();

    // 验证码字符集（排除容易混淆的字符）
    private static final String CHARACTERS = "23456789ABCDEFGHJKLMNPQRSTUVWXYZ";

    @Autowired
    public CaptchaUtil(AppConfig appConfig) {
        this.appConfig = appConfig;
    }

    /**
     * 生成验证码图片
     */
    public CaptchaResult generateCaptcha() {
        int width = appConfig.getCaptcha().getWidth();
        int height = appConfig.getCaptcha().getHeight();
        int length = appConfig.getCaptcha().getLength();

        // 生成验证码文本
        String code = generateRandomCode(length);

        // 创建图片
        BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = image.createGraphics();

        // 设置渲染质量
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);

        // 填充背景
        g2d.setColor(getRandomColor(220, 250));
        g2d.fillRect(0, 0, width, height);

        // 绘制干扰线
        drawInterferenceLines(g2d, width, height);

        // 绘制验证码文本
        drawCaptchaText(g2d, code, width, height);

        // 添加噪点
        addNoise(g2d, width, height);

        g2d.dispose();

        // 转换为Base64
        String imageBase64 = imageToBase64(image);

        return new CaptchaResult(code, imageBase64);
    }

    /**
     * 生成随机验证码
     */
    private String generateRandomCode(int length) {
        StringBuilder code = new StringBuilder();
        for (int i = 0; i < length; i++) {
            code.append(CHARACTERS.charAt(random.nextInt(CHARACTERS.length())));
        }
        return code.toString();
    }

    /**
     * 绘制验证码文本
     */
    private void drawCaptchaText(Graphics2D g2d, String code, int width, int height) {
        int fontSize = height * 3 / 4;
        Font font = new Font("Arial", Font.BOLD, fontSize);
        g2d.setFont(font);

        FontMetrics fm = g2d.getFontMetrics();
        int charWidth = width / code.length();
        int charHeight = fm.getHeight();

        for (int i = 0; i < code.length(); i++) {
            char c = code.charAt(i);

            // 随机颜色
            g2d.setColor(getRandomColor(20, 130));

            // 计算字符位置
            int x = i * charWidth + (charWidth - fm.charWidth(c)) / 2;
            int y = (height - charHeight) / 2 + fm.getAscent();

            // 随机旋转角度
            double angle = (random.nextDouble() - 0.5) * 0.4; // -0.2 到 0.2 弧度

            // 保存当前变换
            Graphics2D g2dCopy = (Graphics2D) g2d.create();
            g2dCopy.rotate(angle, x + fm.charWidth(c) / 2, y - fm.getAscent() / 2);

            // 绘制字符
            g2dCopy.drawString(String.valueOf(c), x, y);
            g2dCopy.dispose();
        }
    }

    /**
     * 绘制干扰线
     */
    private void drawInterferenceLines(Graphics2D g2d, int width, int height) {
        int lineCount = 8;
        for (int i = 0; i < lineCount; i++) {
            g2d.setColor(getRandomColor(160, 200));
            g2d.setStroke(new BasicStroke(random.nextFloat() * 2 + 1));

            int x1 = random.nextInt(width);
            int y1 = random.nextInt(height);
            int x2 = random.nextInt(width);
            int y2 = random.nextInt(height);

            g2d.drawLine(x1, y1, x2, y2);
        }
    }

    /**
     * 添加噪点
     */
    private void addNoise(Graphics2D g2d, int width, int height) {
        int noiseCount = width * height / 100;
        for (int i = 0; i < noiseCount; i++) {
            g2d.setColor(getRandomColor(50, 200));
            int x = random.nextInt(width);
            int y = random.nextInt(height);
            g2d.fillOval(x, y, 1, 1);
        }
    }

    /**
     * 获取随机颜色
     */
    private Color getRandomColor(int min, int max) {
        int range = max - min;
        int r = min + random.nextInt(range);
        int g = min + random.nextInt(range);
        int b = min + random.nextInt(range);
        return new Color(r, g, b);
    }

    /**
     * 图片转Base64
     */
    private String imageToBase64(BufferedImage image) {
        try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
            ImageIO.write(image, "png", baos);
            byte[] imageBytes = baos.toByteArray();
            return "data:image/png;base64," + Base64.getEncoder().encodeToString(imageBytes);
        } catch (IOException e) {
            throw new RuntimeException("验证码图片生成失败", e);
        }
    }

    /**
     * 验证验证码（忽略大小写）
     */
    public boolean verifyCaptcha(String userInput, String correctCode) {
        if (userInput == null || correctCode == null) {
            return false;
        }
        return userInput.trim().equalsIgnoreCase(correctCode.trim());
    }

    /**
     * 验证码结果类
     */
    public static class CaptchaResult {
        private final String code;
        private final String imageBase64;

        public CaptchaResult(String code, String imageBase64) {
            this.code = code;
            this.imageBase64 = imageBase64;
        }

        public String getCode() {
            return code;
        }

        public String getImageBase64() {
            return imageBase64;
        }
    }
}
