@use '@/styles/variables' as v;
@use '@/styles/mixins' as m;

.settings-page {
    min-height: 70vh;
    background: #f7f8fa;
    padding: 48px 0 32px 0;

    .container {
        max-width: 700px;
        margin: 0 auto;
        background: #fff;
        border-radius: 16px;
        box-shadow: 0 4px 18px rgba(102, 126, 234, 0.07);
        padding: 40px 32px 32px 32px;

        @include m.respond-to(sm) {
            padding: 20px 8px;
        }
    }
}

.settings-title {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 2.2rem;
    color: #3a3a4a;
    text-align: center;
    letter-spacing: 1px;
}

.settings-section {
    margin-bottom: 2.5rem;

    h2 {
        font-size: 1.15rem;
        font-weight: 600;
        margin-bottom: 1.2rem;
        color: #4a4a5a;
    }

    .el-radio-group,
    .el-select {
        margin-bottom: 1.2rem;
    }

    .el-switch {
        margin-right: 10px;
    }
}

@media (max-width: 600px) {
    .settings-page {
        padding: 16px 0 8px 0;

        .container {
            padding: 10px 2px;
        }
    }

    .settings-title {
        font-size: 1.3rem;
    }
}
