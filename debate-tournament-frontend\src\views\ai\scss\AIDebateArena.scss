@use '@/styles/variables' as v;
@use '@/styles/mixins' as m;

.ai-debate-arena {
  min-height: 100vh;
  background: var(--bg-gradient);
  color: var(--text-primary);

  .container {
    max-width: 1440px;
    margin: 0 auto;
    padding: 0 1rem;

    @include m.respond-to('tablet') {
      padding: 0 2rem;
    }
  }

  // 顶部信息区
  .arena-header {
    background: var(--bg-secondary);
    padding: 2rem 0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    .arena-title {
      font-size: 2rem;
      font-weight: 600;
      margin-bottom: 1rem;

      @include m.respond-to('mobile') {
        font-size: 1.5rem;
      }
    }

    .arena-meta {
      display: flex;
      gap: 1rem;
      align-items: center;

      .arena-category {
        padding: 0.25rem 0.75rem;
        background: var(--primary-light);
        border-radius: 1rem;
        font-size: 0.875rem;
      }

      .arena-status {
        padding: 0.25rem 0.75rem;
        border-radius: 1rem;
        font-size: 0.875rem;

        &.status--preparing {
          background: var(--warning-light);
        }

        &.status--ongoing {
          background: var(--success-light);
        }

        &.status--paused {
          background: var(--info-light);
        }

        &.status--finished {
          background: var(--danger-light);
        }
      }
    }
  }

  // 主辩论区域
  .arena-main {
    padding: 2rem 0;

    .debate-stage {
      display: grid;
      grid-template-areas:
        "host host host"
        "supporter . opposer"
        "judges judges judges";
      gap: 2rem;
      margin-bottom: 2rem;

      @include m.respond-to('mobile') {
        grid-template-areas:
          "host"
          "supporter"
          "opposer"
          "judges";
      }

      .host-ai {
        grid-area: host;
        justify-self: center;
      }

      .debate-sides {
        display: flex;
        justify-content: space-between;
        width: 100%;
        gap: 2rem;

        @include m.respond-to('mobile') {
          flex-direction: column;
        }

        .debate-side {
          flex: 1;
          text-align: center;

          &.supporter {
            color: var(--supporter-color);
          }

          &.opposer {
            color: var(--opposer-color);
          }
        }
      }

      .judges-panel {
        grid-area: judges;
        display: flex;
        justify-content: space-around;
        gap: 1rem;

        @include m.respond-to('mobile') {
          flex-direction: column;
          align-items: center;
        }
      }
    }

    .ai-avatar {
      width: 120px;
      height: 120px;
      border-radius: 50%;
      overflow: hidden;
      margin-bottom: 1rem;
      border: 4px solid transparent;
      transition: all 0.3s ease;

      @include m.respond-to('mobile') {
        width: 80px;
        height: 80px;
      }

      &.speaking {
        border-color: var(--primary);
        box-shadow: 0 0 20px var(--primary-light);
      }

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .ai-info {
      text-align: center;

      h3 {
        font-size: 1.25rem;
        margin-bottom: 0.5rem;
      }

      .ai-role {
        font-size: 0.875rem;
        color: var(--text-secondary);
      }
    }
  }

  // 辩论内容区
  .debate-content {
    background: var(--bg-secondary);
    border-radius: 1rem;
    padding: 1.5rem;
    margin-bottom: 2rem;
    max-height: 400px;
    overflow-y: auto;

    .speech-history {
      display: flex;
      flex-direction: column;
      gap: 1rem;

      .speech-item {
        padding: 1rem;
        border-radius: 0.5rem;
        background: var(--bg-tertiary);

        &.host {
          border-left: 4px solid var(--primary);
        }

        &.supporter {
          border-left: 4px solid var(--supporter-color);
        }

        &.opposer {
          border-left: 4px solid var(--opposer-color);
        }

        &[class*="judge"] {
          border-left: 4px solid var(--judge-color);
        }

        .speech-header {
          display: flex;
          justify-content: space-between;
          margin-bottom: 0.5rem;
          font-size: 0.875rem;
          color: var(--text-secondary);
        }

        .speech-content {
          line-height: 1.6;
        }
      }
    }
  }

  // 控制面板
  .debate-controls {
    background: var(--bg-secondary);
    border-radius: 1rem;
    padding: 1.5rem;

    .control-panel {
      .debate-progress {
        display: flex;
        align-items: center;
        gap: 1rem;
        margin-bottom: 1rem;

        .round-info {
          font-weight: 600;
        }

        .progress-bar {
          flex: 1;
          height: 8px;
          background: var(--bg-tertiary);
          border-radius: 4px;
          overflow: hidden;

          .progress {
            height: 100%;
            background: var(--primary);
            transition: width 0.3s ease;
          }
        }

        .time-remaining {
          font-family: monospace;
          font-size: 1.125rem;
        }
      }

      .control-buttons {
        display: flex;
        gap: 1rem;
        justify-content: center;

        @include m.respond-to('mobile') {
          flex-direction: column;
        }

        .btn {
          padding: 0.75rem 1.5rem;
          border-radius: 0.5rem;
          font-weight: 600;
          transition: all 0.3s ease;
          min-width: 120px;

          &:disabled {
            opacity: 0.5;
            cursor: not-allowed;
          }

          &--primary {
            background: var(--primary);
            color: white;

            &:hover:not(:disabled) {
              background: var(--primary-dark);
            }
          }

          &--outline {
            border: 2px solid var(--primary);
            color: var(--primary);

            &:hover:not(:disabled) {
              background: var(--primary);
              color: white;
            }
          }

          &--danger {
            background: var(--danger);
            color: white;

            &:hover:not(:disabled) {
              background: var(--danger-dark);
            }
          }
        }
      }
    }
  }
}
