package com.debate_ournament.ai.controller;
import java.util.List;
import java.util.concurrent.CompletableFuture;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import com.debate_ournament.ai.entity.AiProvider;
import com.debate_ournament.ai.entity.ChatMessage;
import com.debate_ournament.ai.service.AiChatService;
import com.debate_ournament.ai.service.AiClient;
import com.debate_ournament.dto.common.ApiResponse;
import com.debate_ournament.security.CustomUserDetailsService.CustomUserPrincipal;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * AI聊天控制器
 * 提供AI聊天相关的REST API接口
 */
@RestController
@RequestMapping("/api/ai/chat")
public class AiChatController {

    private final AiChatService aiChatService;

    public AiChatController(AiChatService aiChatService) {
        this.aiChatService = aiChatService;
    }

    /**
     * 发送聊天消息
     */
    @PostMapping("/send")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<ChatMessage>> sendMessage(
            @Valid @RequestBody ChatRequest request,
            Authentication authentication) {

        Long userId = getUserId(authentication);

        ChatMessage response = aiChatService.sendMessage(
            userId,
            request.getSessionId(),
            request.getContent(),
            request.getConfigId()
        );

        return ResponseEntity.ok(ApiResponse.success("消息发送成功", response));
    }

    /**
     * 流式聊天
     */
    @PostMapping(value = "/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @PreAuthorize("hasRole('USER')")
    public SseEmitter sendStreamMessage(
            @Valid @RequestBody ChatRequest request,
            Authentication authentication) {

        Long userId = getUserId(authentication);
        SseEmitter emitter = new SseEmitter(300000L); // 5分钟超时

        // 异步处理流式聊天
        CompletableFuture.runAsync(() -> {
            try {
                aiChatService.sendStreamMessage(
                    userId, request.getSessionId(), request.getContent(), request.getConfigId(),
                    new AiClient.StreamCallback() {
                        @Override
                        public void onData(String chunk) {
                            try {
                                emitter.send(SseEmitter.event()
                                    .name("message")
                                    .data(chunk));
                            } catch (Exception e) {
                                emitter.completeWithError(e);
                            }
                        }

                        @Override
                        public void onComplete() {
                            try {
                                emitter.send(SseEmitter.event()
                                    .name("complete")
                                    .data(""));
                                emitter.complete();
                            } catch (Exception e) {
                                emitter.completeWithError(e);
                            }
                        }

                        @Override
                        public void onError(String error) {
                            try {
                                emitter.send(SseEmitter.event()
                                    .name("error")
                                    .data(error));
                                emitter.completeWithError(new RuntimeException(error));
                            } catch (Exception e) {
                                emitter.completeWithError(e);
                            }
                        }
                    });
            } catch (Exception e) {
                emitter.completeWithError(e);
            }
        });

        return emitter;
    }

    /**
     * 获取用户的聊天会话列表
     */
    @GetMapping("/sessions")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<List<String>>> getUserSessions(Authentication authentication) {

        Long userId = getUserId(authentication);
        List<String> sessions = aiChatService.getUserSessions(userId);

        return ResponseEntity.ok(ApiResponse.success("获取会话列表成功", sessions));
    }

    /**
     * 获取指定会话的聊天记录
     */
    @GetMapping("/sessions/{sessionId}/messages")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<List<ChatMessage>>> getSessionMessages(
            @PathVariable String sessionId,
            Authentication authentication) {

        Long userId = getUserId(authentication);
        // 验证会话是否属于当前用户
        List<ChatMessage> messages = aiChatService.getSessionMessages(sessionId, userId);

        return ResponseEntity.ok(ApiResponse.success("获取聊天记录成功", messages));
    }

    /**
     * 获取用户的聊天记录（分页）
     */
    @GetMapping("/messages")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Page<ChatMessage>>> getUserMessages(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            Authentication authentication) {

        Long userId = getUserId(authentication);
        Pageable pageable = PageRequest.of(page, size);
        Page<ChatMessage> messages = aiChatService.getUserMessages(userId, pageable);

        return ResponseEntity.ok(ApiResponse.success("获取聊天记录成功", messages));
    }

    /**
     * 删除会话
     */
    @DeleteMapping("/sessions/{sessionId}")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Void>> deleteSession(
            @PathVariable String sessionId,
            Authentication authentication) {

        Long userId = getUserId(authentication);
        aiChatService.deleteSession(sessionId, userId);

        return ResponseEntity.ok(ApiResponse.success("会话删除成功"));
    }

    /**
     * 获取用户Token消耗统计
     */
    @GetMapping("/statistics/tokens")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<TokenStatistics>> getTokenStatistics(Authentication authentication) {

        Long userId = getUserId(authentication);
        Long totalTokens = aiChatService.getUserTokenConsumption(userId);

        TokenStatistics statistics = new TokenStatistics();
        statistics.setTotalTokens(totalTokens);
        statistics.setUserId(userId);

        return ResponseEntity.ok(ApiResponse.success("获取Token统计成功", statistics));
    }

    /**
     * 获取支持的AI提供商列表
     */
    @GetMapping("/providers")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<List<AiProvider>>> getSupportedProviders() {

        List<AiProvider> providers = aiChatService.getSupportedProviders();

        return ResponseEntity.ok(ApiResponse.success("获取AI提供商列表成功", providers));
    }

    /**
     * 获取指定提供商支持的模型列表
     */
    @GetMapping("/providers/{provider}/models")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<List<String>>> getSupportedModels(
            @PathVariable AiProvider provider,
            @RequestParam(required = false) Long configId) {

        List<String> models = aiChatService.getSupportedModels(provider, configId);

        return ResponseEntity.ok(ApiResponse.success("获取模型列表成功", models));
    }

    /**
     * 测试AI配置连接
     */
    @PostMapping("/configs/{configId}/test")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<TestResult>> testAiConfig(@PathVariable Long configId) {

        boolean success = aiChatService.testAiConfig(configId);

        TestResult result = new TestResult();
        result.setConfigId(configId);
        result.setSuccess(success);
        result.setMessage(success ? "连接测试成功" : "连接测试失败");

        return ResponseEntity.ok(ApiResponse.success("测试完成", result));
    }

    /**
     * 从认证信息中获取用户ID
     */
    private Long getUserId(Authentication authentication) {
        if (authentication == null) {
            throw new RuntimeException("用户认证信息为空");
        }

        try {
            if (authentication.getPrincipal() instanceof CustomUserPrincipal) {
                return ((CustomUserPrincipal) authentication.getPrincipal()).getUserId();
            }
            // 备用方案：从用户名获取
            String username = authentication.getName();
            if (username != null && username.matches("\\d+")) {
                return Long.valueOf(username);
            }
            throw new RuntimeException("无法从认证信息中解析用户ID");
        } catch (NumberFormatException e) {
            throw new RuntimeException("无法解析用户ID: " + authentication.getName(), e);
        } catch (RuntimeException e) {
            throw e;
        } catch (Exception e) {
            throw new RuntimeException("获取用户ID时发生未知错误", e);
        }
    }

    /**
     * 聊天请求DTO
     */
    public static class ChatRequest {

        @NotBlank(message = "消息内容不能为空")
        @Size(max = 4000, message = "消息内容不能超过4000个字符")
        private String content;

        private String sessionId;

        private Long configId;

        // Getters and Setters
        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }

        public String getSessionId() {
            return sessionId;
        }

        public void setSessionId(String sessionId) {
            this.sessionId = sessionId;
        }

        public Long getConfigId() {
            return configId;
        }

        public void setConfigId(Long configId) {
            this.configId = configId;
        }
    }

    /**
     * Token统计DTO
     */
    public static class TokenStatistics {

        private Long userId;
        private Long totalTokens;
        private String period = "all-time";

        // Getters and Setters
        public Long getUserId() {
            return userId;
        }

        public void setUserId(Long userId) {
            this.userId = userId;
        }

        public Long getTotalTokens() {
            return totalTokens;
        }

        public void setTotalTokens(Long totalTokens) {
            this.totalTokens = totalTokens;
        }

        public String getPeriod() {
            return period;
        }

        public void setPeriod(String period) {
            this.period = period;
        }
    }

    /**
     * 测试结果DTO
     */
    public static class TestResult {

        private Long configId;
        private boolean success;
        private String message;
        private long responseTime;

        // Getters and Setters
        public Long getConfigId() {
            return configId;
        }

        public void setConfigId(Long configId) {
            this.configId = configId;
        }

        public boolean isSuccess() {
            return success;
        }

        public void setSuccess(boolean success) {
            this.success = success;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public long getResponseTime() {
            return responseTime;
        }

        public void setResponseTime(long responseTime) {
            this.responseTime = responseTime;
        }
    }
}
