package com.debate_ournament.auth.repository;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.boot.test.autoconfigure.orm.jpa.TestEntityManager;

import com.debate_ournament.users.entity.NotificationSettings;
import com.debate_ournament.users.entity.User;
import com.debate_ournament.users.repository.NotificationSettingsRepository;

/**
 * 通知设置数据访问层测试类
 */
@DataJpaTest
public class NotificationSettingsRepositoryTest {

    @Autowired
    private TestEntityManager entityManager;

    @Autowired
    private NotificationSettingsRepository notificationSettingsRepository;

    private User testUser;
    private NotificationSettings testSettings;

    @BeforeEach
    void setUp() {
        // 创建测试用户
        testUser = new User();
        testUser.setUsername("testuser");
        testUser.setEmail("<EMAIL>");
        testUser.setPassword("password");
        testUser = entityManager.persist(testUser);

        // 创建测试通知设置
        testSettings = new NotificationSettings();
        testSettings.setUser(testUser);
        testSettings.setEmailNotifications(true);
        testSettings.setDebateInvitations(true);
        testSettings.setDebateUpdates(true);
        testSettings.setMentions(true);
        testSettings.setSystemAnnouncements(true);
        testSettings.setEmailFrequency("daily");
        testSettings.setQuietHoursStart(22);
        testSettings.setQuietHoursEnd(7);
        testSettings.setNotificationSound(true);
        testSettings = entityManager.persist(testSettings);

        entityManager.flush();
    }

    @Test
    void findByUser_ReturnsNotificationSettings() {
        NotificationSettings found = notificationSettingsRepository.findByUser(testUser).orElse(null);

        assertNotNull(found);
        assertEquals(testSettings.getId(), found.getId());
        assertEquals(testSettings.getEmailNotifications(), found.getEmailNotifications());
        assertEquals(testSettings.getDebateInvitations(), found.getDebateInvitations());
        assertEquals(testSettings.getEmailFrequency(), found.getEmailFrequency());
    }

    @Test
    void findByUserId_ReturnsNotificationSettings() {
        NotificationSettings found = notificationSettingsRepository.findByUserId(testUser.getId()).orElse(null);

        assertNotNull(found);
        assertEquals(testSettings.getId(), found.getId());
    }

    @Test
    void findByEmailNotificationsTrue_ReturnsCorrectList() {
        List<NotificationSettings> settings = notificationSettingsRepository.findByEmailNotificationsTrue();

        assertFalse(settings.isEmpty());
        assertTrue(settings.stream().allMatch(NotificationSettings::getEmailNotifications));
    }

    @Test
    void findBySystemAnnouncementsTrue_ReturnsCorrectList() {
        List<NotificationSettings> settings = notificationSettingsRepository.findBySystemAnnouncementsTrue();

        assertFalse(settings.isEmpty());
        assertTrue(settings.stream().allMatch(NotificationSettings::getSystemAnnouncements));
    }

    @Test
    void findByEmailFrequency_ReturnsCorrectList() {
        List<NotificationSettings> settings = notificationSettingsRepository.findByEmailFrequency("daily");

        assertFalse(settings.isEmpty());
        assertTrue(settings.stream().allMatch(s -> "daily".equals(s.getEmailFrequency())));
    }

    @Test
    void findUsersInQuietHours_ReturnsCorrectList() {
        // 测试在安静时间内的用户
        List<NotificationSettings> settings = notificationSettingsRepository.findUsersInQuietHours(23);

        assertFalse(settings.isEmpty());
        assertTrue(settings.stream().allMatch(s ->
            s.getQuietHoursStart() <= 23 && s.getQuietHoursEnd() >= 23));
    }

    @Test
    void existsByUser_ReturnsTrue() {
        assertTrue(notificationSettingsRepository.existsByUser(testUser));
    }

    @Test
    void deleteByUser_Success() {
        notificationSettingsRepository.deleteByUser(testUser);
        entityManager.flush();

        assertFalse(notificationSettingsRepository.findByUser(testUser).isPresent());
    }

    @Test
    void updateEmailNotifications_Success() {
        List<Long> userIds = List.of(testUser.getId());
        int updated = notificationSettingsRepository.updateEmailNotifications(userIds, false);

        assertTrue(updated > 0);

        NotificationSettings found = notificationSettingsRepository.findByUser(testUser).orElse(null);
        assertNotNull(found);
        assertFalse(found.getEmailNotifications());
    }

    @Test
    void updateSystemAnnouncements_Success() {
        List<Long> userIds = List.of(testUser.getId());
        int updated = notificationSettingsRepository.updateSystemAnnouncements(userIds, false);

        assertTrue(updated > 0);

        NotificationSettings found = notificationSettingsRepository.findByUser(testUser).orElse(null);
        assertNotNull(found);
        assertFalse(found.getSystemAnnouncements());
    }
}
