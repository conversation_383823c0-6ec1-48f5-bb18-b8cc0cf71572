@use '@/styles/variables' as v;
@use '@/styles/mixins' as m;

// 骨架屏动画
.skeleton-loading {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 8px;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }

  100% {
    background-position: -200% 0;
  }
}

// 英雄区域
.hero-section {
  position: relative;
  padding: 96px 0 64px 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  overflow: hidden;
  min-height: 480px;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url('@/assets/images/pattern.svg');
    opacity: 0.08;
    z-index: 0;
  }

  .container {
    position: relative;
    z-index: 1;
  }

  .hero-content {
    display: flex;
    align-items: center;
    gap: 56px;

    @include m.respond-to(md) {
      flex-direction: column-reverse;
      gap: 32px;
    }

    &__text {
      flex: 1;

      .hero-content__title {
        font-size: 3.2rem;
        font-weight: 800;
        margin-bottom: 1.2rem;
        letter-spacing: 2px;
        text-shadow: 0 4px 24px rgba(80, 60, 160, 0.12);
      }

      .hero-content__subtitle {
        font-size: 1.3rem;
        margin-bottom: 2.2rem;
        opacity: 0.92;
        font-weight: 400;
      }

      .hero-content__features {
        display: flex;
        flex-wrap: wrap;
        gap: 18px 32px;
        margin-bottom: 2.2rem;

        .feature-item {
          display: flex;
          align-items: center;
          gap: 8px;
          font-size: 1.08rem;

          .el-icon {
            font-size: 22px;
            color: #fff;
            filter: drop-shadow(0 2px 8px #764ba2cc);
          }
        }
      }

      .hero-content__actions {
        display: flex;
        gap: 18px;

        .el-button {
          font-size: 1.1rem;
          padding: 0 2.2em;
          border-radius: 24px;
          font-weight: 600;
          box-shadow: 0 2px 16px rgba(102, 126, 234, 0.08);
        }
      }
    }

    &__visual {
      flex: 1;
      min-width: 320px;

    }
  }
}

// 通用标题
.section-title {
  text-align: center;
  font-size: 2.2rem;
  font-weight: 700;
  margin-bottom: 2.5rem;
  color: #3a3a4a;
  letter-spacing: 1px;

  &::after {
    content: '';
    display: block;
    width: 64px;
    height: 3px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    margin: 0.7rem auto 0;
    border-radius: 2px;
  }
}

.section-action {
  margin-top: 2.5rem;
  text-align: center;
}

// 功能卡片区
.features-section {
  padding: 72px 0 56px 0;
  background: #f7f8fa;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 32px;
}

.feature-card {
  background: #fff;
  border-radius: 18px;
  padding: 36px 28px 32px 28px;
  box-shadow: 0 4px 24px rgba(102, 126, 234, 0.08);
  transition: transform 0.25s, box-shadow 0.25s;
  text-align: center;
  position: relative;
  overflow: hidden;

  &:hover {
    transform: translateY(-8px) scale(1.03);
    box-shadow: 0 12px 32px rgba(102, 126, 234, 0.16);
  }

  &__icon {
    margin-bottom: 1.2rem;

    .el-icon {
      font-size: 48px;
      color: #667eea;
      filter: drop-shadow(0 2px 12px #667eea33);
    }
  }

  &__title {
    font-size: 1.18rem;
    font-weight: 700;
    margin-bottom: 1.1rem;
    color: #3a3a4a;
  }

  &__list {
    text-align: left;
    margin-bottom: 1.5rem;
    padding-left: 18px;

    li {
      margin-bottom: 7px;
      color: #7b7b8b;
      font-size: 1.01rem;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .el-button {
    border-radius: 20px;
    font-weight: 600;
    font-size: 1.05rem;
  }
}

// 实时辩论区
.live-debates-section {
  padding: 72px 0 56px 0;
  background: #fff;
}

.live-debates-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(340px, 1fr));
  gap: 32px;
}

// AI模型区
.ai-models-section {
  padding: 72px 0 56px 0;
  background: #f5f7fa;
}

.models-tabs {
  max-width: 1100px;
  margin: 0 auto;
}

.models-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
  gap: 32px;
  margin-top: 2rem;
}

.model-card {
  background: #fff;
  border-radius: 16px;
  padding: 32px 20px 28px 20px;
  box-shadow: 0 4px 18px rgba(102, 126, 234, 0.07);
  transition: all 0.22s;
  text-align: center;

  &:hover {
    transform: translateY(-6px) scale(1.02);
    box-shadow: 0 10px 28px rgba(102, 126, 234, 0.13);
  }

  &__name {
    font-size: 1.13rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: #3a3a4a;
  }

  &__features {
    margin-bottom: 1.3rem;
  }

  &--custom {
    border: 2px dashed #ddd;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
}

.model-feature {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 8px;

  .el-icon {
    color: #667eea;
  }
}

// 思维导图区
.mind-map-section {
  padding: 72px 0 56px 0;
  background: #fff;
}

.mind-map-demo {
  max-width: 1100px;
  margin: 0 auto;
}

.mind-map-controls {
  margin-top: 1.5rem;
  text-align: center;
}

// 社区互动区
.community-section {
  padding: 72px 0 56px 0;
  background: #f5f7fa;
}

.community-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(340px, 1fr));
  gap: 32px;
  max-width: 1100px;
  margin: 0 auto;
}

.community-card {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 18px rgba(102, 126, 234, 0.07);
  overflow: hidden;

  &__header {
    padding: 1.2rem 1.5rem;
    background: #f8f9fa;
    border-bottom: 1px solid #eee;
  }

  &__title {
    font-size: 1.08rem;
    font-weight: 700;
    margin: 0;
    color: #3a3a4a;
  }

  &__content {
    padding: 1.5rem 1.5rem 1.2rem 1.5rem;
  }
}

.user-showcase {
  display: flex;
  gap: 1.2rem;

  .user-avatar {
    flex-shrink: 0;
  }

  .user-info {
    flex: 1;
  }

  .user-name {
    font-size: 1.08rem;
    font-weight: 700;
    margin-top: 0;
    margin-bottom: 8px;
    color: #3a3a4a;
  }

  .user-stats {
    font-size: 0.97rem;
    color: #7b7b8b;
    margin-bottom: 8px;
  }

  .user-quote {
    font-style: italic;
    margin-bottom: 1.2rem;
    color: #3a3a4a;
  }

  .user-actions {
    display: flex;
    gap: 8px;
  }
}

.topic-showcase {
  .topic-title {
    font-size: 1.08rem;
    font-weight: 700;
    margin-top: 0;
    margin-bottom: 8px;
    color: #3a3a4a;
  }

  .topic-stats {
    font-size: 0.97rem;
    color: #7b7b8b;
    margin-bottom: 1.2rem;
  }

  .topic-votes {
    margin-bottom: 1.2rem;

    .vote-bar {
      display: flex;
      height: 24px;
      border-radius: 4px;
      overflow: hidden;

      &__support {
        background: linear-gradient(90deg, #667eea 0%, #4caf50 100%);
        color: white;
        text-align: center;
        line-height: 24px;
        font-size: 12px;
        font-weight: 600;
      }

      &__oppose {
        background: linear-gradient(90deg, #f44336 0%, #764ba2 100%);
        color: white;
        text-align: center;
        line-height: 24px;
        font-size: 12px;
        font-weight: 600;
      }
    }
  }

  .topic-actions {
    display: flex;
    gap: 8px;
  }
}

// 响应式调整
@media (max-width: 900px) {
  .hero-section {
    padding: 64px 0 32px 0;
  }

  .features-section,
  .live-debates-section,
  .ai-models-section,
  .mind-map-section,
  .community-section {
    padding: 40px 0 24px 0;
  }
}

@media (max-width: 600px) {
  .hero-section {
    padding: 32px 0 16px 0;
  }

  .features-section,
  .live-debates-section,
  .ai-models-section,
  .mind-map-section,
  .community-section {
    padding: 20px 0 12px 0;
  }

  .features-grid,
  .live-debates-grid,
  .models-grid,
  .community-cards {
    grid-template-columns: 1fr;
  }
}
